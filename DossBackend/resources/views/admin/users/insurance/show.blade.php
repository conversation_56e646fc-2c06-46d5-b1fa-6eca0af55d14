@extends('layouts.master')

@section('title','Employee Insurance Details')

@section('action','View')

@section('button')
    <div class="float-end">
        <a href="{{route('admin.users.show', $userDetail->id)}}">
            <button class="btn btn-sm btn-primary"><i class="link-icon" data-feather="arrow-left"></i> Back to Employee</button>
        </a>
        @can('edit_employee')
            @if($userDetail->insurance_type)
                <a href="{{route('admin.users.insurance.edit', $userDetail->id)}}">
                    <button class="btn btn-sm btn-warning"><i class="link-icon" data-feather="edit"></i> Edit Insurance</button>
                </a>
            @else
                <a href="{{route('admin.users.insurance.create', $userDetail->id)}}">
                    <button class="btn btn-sm btn-success"><i class="link-icon" data-feather="plus"></i> Add Insurance</button>
                </a>
            @endif
        @endcan
    </div>
@endsection

@section('main-content')

    <section class="content">

        @include('admin.section.flash_message')

        @include('admin.users.common.breadcrumb')

        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-4">
                        <div class="border-end">
                            @if($userDetail->avatar)
                                <img src="{{asset(\App\Models\User::AVATAR_UPLOAD_PATH.$userDetail->avatar)}}" alt="profile" class="img-fluid rounded-circle" width="110">
                            @else
                                <img src="{{asset('assets/images/img.png')}}" alt="profile" class="img-fluid rounded-circle" width="110">
                            @endif
                            <h4 class="mt-3 mb-0">{{ucfirst($userDetail->name)}}</h4>
                            <p class="text-muted">{{$userDetail->email}}</p>
                        </div>
                    </div>
                    <div class="col-lg-8">
                        <div class="profile-detail">
                            <h5 class="mb-3 border-bottom pb-3">Insurance Information</h5>
                            
                            @if($userDetail->insurance_type)
                                <div class="row">
                                    <div class="col-lg-6 mb-3">
                                        <label class="form-label fw-bold">Insurance Type:</label>
                                        <p class="mb-0">{{ ucfirst($userDetail->insurance_type) }}</p>
                                    </div>
                                    
                                    @if($userDetail->insurance_type == 'medical')
                                        <div class="col-lg-6 mb-3">
                                            <label class="form-label fw-bold">Medical Insurance Category:</label>
                                            <p class="mb-0">{{ ucfirst($userDetail->medical_insurance_category) }}</p>
                                        </div>
                                    @endif
                                    
                                    @if($userDetail->insurance_institution_id)
                                        <div class="col-lg-6 mb-3">
                                            <label class="form-label fw-bold">Insurance Institution:</label>
                                            <p class="mb-0">
                                                @php
                                                    $institution = $insuranceInstitutions->find($userDetail->insurance_institution_id);
                                                @endphp
                                                {{ $institution ? $institution->name : 'N/A' }}
                                            </p>
                                        </div>
                                    @endif
                                    
                                    @if($userDetail->insurance_job_title)
                                        <div class="col-lg-6 mb-3">
                                            <label class="form-label fw-bold">Insurance Job Title:</label>
                                            <p class="mb-0">{{ $userDetail->insurance_job_title }}</p>
                                        </div>
                                    @endif
                                    
                                    @if($userDetail->insurance_start_date)
                                        <div class="col-lg-6 mb-3">
                                            <label class="form-label fw-bold">Insurance Start Date:</label>
                                            <p class="mb-0">{{ \Carbon\Carbon::parse($userDetail->insurance_start_date)->format('d M Y') }}</p>
                                        </div>
                                    @endif
                                    
                                    @if($userDetail->insurance_salary)
                                        <div class="col-lg-6 mb-3">
                                            <label class="form-label fw-bold">Insurance Salary:</label>
                                            <p class="mb-0">${{ number_format($userDetail->insurance_salary, 2) }}</p>
                                        </div>
                                    @endif
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="link-icon" data-feather="shield-off" style="width: 48px; height: 48px; color: #6c757d;"></i>
                                    <h5 class="mt-3 text-muted">No Insurance Information</h5>
                                    <p class="text-muted">This employee doesn't have insurance information set up yet.</p>
                                    @can('edit_employee')
                                        <a href="{{route('admin.users.insurance.create', $userDetail->id)}}" class="btn btn-primary">
                                            <i class="link-icon" data-feather="plus"></i> Add Insurance Information
                                        </a>
                                    @endcan
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </section>
@endsection
