<script src="{{ asset('assets/jquery-validation/jquery.validate.min.js') }}"></script>
<script src="{{ asset('assets/jquery-validation/additional-methods.min.js') }}"></script>

<script>
    $(document).ready(function () {
        // Handle insurance type change
        $('#insurance_type').change(function () {
            if ($(this).val()) {
                $('#insurance_div').removeClass('d-none');
                if ($(this).val() === 'medical') {
                    $('#medical_insurance_div').removeClass('d-none');
                } else {
                    $('#medical_insurance_div').addClass('d-none');
                    $('#medical_insurance_category').val(''); // Clear medical category when not medical
                }
            } else {
                $('#insurance_div').addClass('d-none');
                $('#medical_insurance_div').addClass('d-none');
                // Clear all insurance fields when no insurance type selected
                $('#medical_insurance_category').val('');
                $('#insurance_institution_id').val('');
                $('#insurance_job_title').val('');
                $('#insurance_start_date').val('');
                $('#insurance_salary').val('');
            }
        });

        // Trigger change event on page load to show/hide fields based on existing data
        $('#insurance_type').trigger('change');

        // Form validation
        $('#insuranceForm').validate({
            rules: {
                insurance_type: { 
                    required: true 
                },
                medical_insurance_category: {
                    required: function(element) {
                        return $('#insurance_type').val() === 'medical';
                    }
                },
                insurance_institution_id: {
                    required: function(element) {
                        return $('#insurance_type').val() !== '';
                    }
                },
                insurance_start_date: {
                    date: true
                },
                insurance_salary: {
                    number: true,
                    min: 0
                }
            },
            messages: {
                insurance_type: {
                    required: "Please select an insurance type"
                },
                medical_insurance_category: {
                    required: "Please select medical insurance category"
                },
                insurance_institution_id: {
                    required: "Please select an insurance institution"
                },
                insurance_start_date: {
                    date: "Please enter a valid date"
                },
                insurance_salary: {
                    number: "Please enter a valid number",
                    min: "Insurance salary cannot be negative"
                }
            },
            errorElement: 'span',
            errorPlacement: function (error, element) {
                error.addClass('invalid-feedback');
                element.closest('div').append(error);
            },
            highlight: function (element) {
                $(element).addClass('is-invalid');
                $(element).removeClass('is-valid');
            },
            unhighlight: function (element) {
                $(element).removeClass('is-invalid');
                $(element).addClass('is-valid');
            }
        });
    });
</script>
