<div class="card">
    <div class="card-body">
        <div class="profile-detail">
            <h5 class="mb-3 border-bottom pb-3">Insurance Details</h5>
            <div class="row">

                <div class="col-lg-6 mb-3">
                    <label for="insurance_type" class="form-label">Insurance Type</label>
                    <select class="form-select" id="insurance_type" name="insurance_type">
                        <option value="">Select Insurance Type</option>
                        <option value="social" {{ isset($userDetail) && $userDetail->insurance_type == 'social' ? 'selected' : '' }}>Social</option>
                        <option value="medical" {{ isset($userDetail) && $userDetail->insurance_type == 'medical' ? 'selected' : '' }}>Medical</option>
                    </select>
                    @error('insurance_type')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                <div class="col-lg-6 mb-3 {{ isset($userDetail) && $userDetail->insurance_type == 'medical' ? '' : 'd-none'}}" id="medical_insurance_div">
                    <label for="medical_insurance_category" class="form-label">Medical Insurance Category</label>
                    <select class="form-select" id="medical_insurance_category" name="medical_insurance_category">
                        <option value="">Select Category</option>
                        <option value="public" {{ isset($userDetail) && $userDetail->medical_insurance_category == 'public' ? 'selected' : '' }}>Public</option>
                        <option value="private" {{ isset($userDetail) && $userDetail->medical_insurance_category == 'private' ? 'selected' : '' }}>Private</option>
                    </select>
                    @error('medical_insurance_category')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
                
                <div class="row {{ isset($userDetail) && $userDetail->insurance_type ? '' : 'd-none'}}" id="insurance_div">
    
                    <div class="col-lg-6 mb-3">
                        <label for="insurance_institution_id" class="form-label">Insurance Institution</label>
                        <select class="form-select" id="insurance_institution_id" name="insurance_institution_id">
                            <option value="">Select Institution</option>
                            @foreach($insuranceInstitutions as $institution)
                                <option value="{{ $institution->id }}" {{ isset($userDetail) && $userDetail->insurance_institution_id == $institution->id ? 'selected' : '' }}>
                                    {{ $institution->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('insurance_institution_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
    
                    <div class="col-lg-6 mb-3">
                        <label for="insurance_job_title" class="form-label">Insurance Job Title</label>
                        <input type="text" class="form-control" id="insurance_job_title" name="insurance_job_title" value="{{ isset($userDetail) ? $userDetail->insurance_job_title : old('insurance_job_title') }}" placeholder="Enter Insurance Job Title">
                        @error('insurance_job_title')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
    
                    <div class="col-lg-6 mb-3">
                        <label for="insurance_start_date" class="form-label">Insurance Start Date</label>
                        <input type="date" class="form-control" id="insurance_start_date" name="insurance_start_date" value="{{ isset($userDetail) ? $userDetail->insurance_start_date : old('insurance_start_date') }}">
                        @error('insurance_start_date')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
    
                    <div class="col-lg-6 mb-3">
                        <label for="insurance_salary" class="form-label">Insurance Salary</label>
                        <input type="number" class="form-control" id="insurance_salary" name="insurance_salary" value="{{ isset($userDetail) ? $userDetail->insurance_salary : old('insurance_salary') }}" placeholder="Enter Insurance Salary" step="0.01">
                        @error('insurance_salary')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                
                </div>
                
            </div>
        </div>
    </div>
</div>

<div class="d-flex justify-content-end mt-3">
    <a href="{{ route('admin.users.insurance.show', $userDetail->id) }}" class="btn btn-secondary me-2">Cancel</a>
    <button type="submit" class="btn btn-primary">
        <i class="link-icon" data-feather="save"></i> Save Insurance Information
    </button>
</div>
