@extends('layouts.master')

@section('title','Edit Employee Insurance')

@section('action','Edit')

@section('button')
    <div class="float-end">
        <a href="{{route('admin.users.insurance.show', $userDetail->id)}}">
            <button class="btn btn-sm btn-primary"><i class="link-icon" data-feather="arrow-left"></i> Back</button>
        </a>
    </div>
@endsection

@section('main-content')

    <section class="content">

        @include('admin.section.flash_message')

        @include('admin.users.common.breadcrumb')

        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3">
                        @if($userDetail->avatar)
                            <img src="{{asset(\App\Models\User::AVATAR_UPLOAD_PATH.$userDetail->avatar)}}" alt="profile" class="img-fluid rounded-circle" width="80">
                        @else
                            <img src="{{asset('assets/images/img.png')}}" alt="profile" class="img-fluid rounded-circle" width="80">
                        @endif
                    </div>
                    <div class="col-lg-9">
                        <h4 class="mb-1">{{ucfirst($userDetail->name)}}</h4>
                        <p class="text-muted mb-0">{{$userDetail->email}}</p>
                        <p class="text-muted">Editing insurance information</p>
                    </div>
                </div>
            </div>
        </div>

        <form class="forms-sample" id="insuranceForm" action="{{route('admin.users.insurance.update', $userDetail->id)}}" method="POST">
            @csrf
            @method('PUT')
            @include('admin.users.insurance.form')
        </form>

    </section>
@endsection

@section('scripts')
    @include('admin.users.insurance.scripts')
@endsection
