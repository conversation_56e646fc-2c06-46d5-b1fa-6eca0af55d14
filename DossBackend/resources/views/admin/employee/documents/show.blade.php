@extends('layouts.master')

@section('title', 'تفاصيل المصوغة - ' . $document->document_name)

@section('action')
    <div class="float-end">
        <a href="{{ route('admin.employee.documents.download', $document->id) }}" class="btn btn-success">
            <i class="link-icon" data-feather="download"></i> تحميل الملف
        </a>
        @can('edit_employee')
            <a href="{{ route('admin.employee.documents.edit', $document->id) }}" class="btn btn-warning">
                <i class="link-icon" data-feather="edit"></i> تعديل
            </a>
        @endcan
        <a href="{{ route('admin.employee.documents.index', $document->user_id) }}" class="btn btn-secondary">
            <i class="link-icon" data-feather="arrow-left"></i> العودة للقائمة
        </a>
    </div>
@endsection

@section('main-content')
    <section class="content">
        @include('admin.section.flash_message')

        <!-- Document Details Card -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">تفاصيل المصوغة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">اسم المصوغة:</th>
                                <td>{{ $document->document_name }}</td>
                            </tr>
                            <tr>
                                <th>نوع المصوغة:</th>
                                <td>
                                    <span class="badge bg-info">{{ $document->document_type_label }}</span>
                                    @if($document->is_required)
                                        <span class="badge bg-warning ms-1">مطلوبة</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>الموظف:</th>
                                <td>{{ $document->user->name ?? 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <th>الوصف:</th>
                                <td>{{ $document->description ?? 'لا يوجد وصف' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">اسم الملف الأصلي:</th>
                                <td>{{ $document->original_name }}</td>
                            </tr>
                            <tr>
                                <th>حجم الملف:</th>
                                <td>{{ $document->file_size_formatted }}</td>
                            </tr>
                            <tr>
                                <th>نوع الملف:</th>
                                <td>{{ strtoupper($document->file_extension) }}</td>
                            </tr>
                            <tr>
                                <th>تاريخ الرفع:</th>
                                <td>{{ $document->created_at->format('Y-m-d H:i:s') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Information Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">معلومات الرفع</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">رفع بواسطة:</th>
                                <td>{{ $document->uploadedBy->name ?? 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <th>تاريخ الرفع:</th>
                                <td>{{ $document->created_at->format('Y-m-d H:i:s') }}</td>
                            </tr>
                            @if($document->updated_at != $document->created_at)
                                <tr>
                                    <th>آخر تحديث:</th>
                                    <td>{{ $document->updated_at->format('Y-m-d H:i:s') }}</td>
                                </tr>
                            @endif
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">حالة التحقق:</th>
                                <td>
                                    @if($document->is_verified)
                                        <span class="badge bg-success">متحقق منها</span>
                                    @else
                                        <span class="badge bg-secondary">غير متحقق منها</span>
                                    @endif
                                </td>
                            </tr>
                            @if($document->is_verified)
                                <tr>
                                    <th>تاريخ التحقق:</th>
                                    <td>{{ $document->verified_at->format('Y-m-d H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <th>تم التحقق بواسطة:</th>
                                    <td>{{ $document->verifiedBy->name ?? 'غير محدد' }}</td>
                                </tr>
                            @endif
                        </table>
                    </div>
                </div>

                @if($document->verification_notes)
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <h6>ملاحظات التحقق:</h6>
                            <div class="alert alert-info">
                                {{ $document->verification_notes }}
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- File Preview Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">معاينة الملف</h6>
            </div>
            <div class="card-body">
                @if(in_array($document->file_extension, ['jpg', 'jpeg', 'png']))
                    <!-- Image Preview -->
                    <div class="text-center">
                        <img src="{{ $document->full_file_path }}" class="img-fluid" style="max-height: 500px;" alt="{{ $document->document_name }}">
                    </div>
                @elseif($document->file_extension == 'pdf')
                    <!-- PDF Preview -->
                    <div class="text-center">
                        <embed src="{{ $document->full_file_path }}" type="application/pdf" width="100%" height="600px">
                        <p class="mt-2">
                            <a href="{{ route('admin.employee.documents.download', $document->id) }}" class="btn btn-primary">
                                تحميل ملف PDF
                            </a>
                        </p>
                    </div>
                @else
                    <!-- Other File Types -->
                    <div class="text-center py-4">
                        <i class="link-icon" data-feather="file-text" style="width: 64px; height: 64px;"></i>
                        <h5 class="mt-3">{{ $document->original_name }}</h5>
                        <p class="text-muted">نوع الملف: {{ strtoupper($document->file_extension) }}</p>
                        <a href="{{ route('admin.employee.documents.download', $document->id) }}" class="btn btn-primary">
                            <i class="link-icon" data-feather="download"></i> تحميل الملف
                        </a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Actions Card -->
        @can('edit_employee')
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">الإجراءات</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>إدارة التحقق:</h6>
                            @if($document->is_verified)
                                <button type="button" class="btn btn-warning" onclick="toggleVerification({{ $document->id }}, false)">
                                    <i class="link-icon" data-feather="x-circle"></i> إلغاء التحقق
                                </button>
                            @else
                                <button type="button" class="btn btn-success" onclick="toggleVerification({{ $document->id }}, true)">
                                    <i class="link-icon" data-feather="check-circle"></i> تحقق من المصوغة
                                </button>
                            @endif
                        </div>
                        <div class="col-md-6">
                            <h6>إدارة الملف:</h6>
                            <a href="{{ route('admin.employee.documents.edit', $document->id) }}" class="btn btn-primary">
                                <i class="link-icon" data-feather="edit"></i> تعديل المعلومات
                            </a>
                            @can('delete_employee')
                                <button type="button" class="btn btn-danger" onclick="deleteDocument({{ $document->id }})">
                                    <i class="link-icon" data-feather="trash-2"></i> حذف المصوغة
                                </button>
                            @endcan
                        </div>
                    </div>
                </div>
            </div>
        @endcan
    </section>

    <!-- Verification Modal -->
    <div class="modal fade" id="verificationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تحقق من المصوغة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="verificationForm" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="verification_notes" class="form-label">ملاحظات التحقق</label>
                            <textarea name="verification_notes" id="verification_notes" class="form-control" rows="3" 
                                      placeholder="أدخل ملاحظات التحقق (اختياري)">{{ $document->verification_notes }}</textarea>
                        </div>
                        <input type="hidden" name="is_verified" id="is_verified_input">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف هذه المصوغة؟ لا يمكن التراجع عن هذا الإجراء.</p>
                    <div class="alert alert-warning">
                        <strong>تحذير:</strong> سيتم حذف الملف نهائياً من الخادم.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">حذف نهائياً</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        function toggleVerification(documentId, isVerified) {
            document.getElementById('verificationForm').action = `/admin/employee-documents/${documentId}/verify`;
            document.getElementById('is_verified_input').value = isVerified;
            
            const modal = new bootstrap.Modal(document.getElementById('verificationModal'));
            modal.show();
        }

        function deleteDocument(documentId) {
            document.getElementById('deleteForm').action = `/admin/employee-documents/${documentId}`;
            
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }
    </script>
@endsection
