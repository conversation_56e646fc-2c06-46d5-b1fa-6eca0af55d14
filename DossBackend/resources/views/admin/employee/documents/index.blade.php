@extends('layouts.master')

@section('title', 'مصوغات الموظف - ' . $employee->name)

@section('action')
    <div class="float-end">
        <a href="{{ route('admin.employee.documents.create', $employee->id) }}" class="btn btn-primary">
            <i class="link-icon" data-feather="plus"></i> رفع مصوغة جديدة
        </a>
        <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
            <i class="link-icon" data-feather="arrow-left"></i> العودة للموظفين
        </a>
    </div>
@endsection

@section('main-content')
    <section class="content">
        @include('admin.section.flash_message')

        <!-- Employee Info Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">معلومات الموظف</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>الاسم:</strong> {{ $employee->name }}
                    </div>
                    <div class="col-md-3">
                        <strong>الفرع:</strong> {{ $employee->branch->name ?? 'غير محدد' }}
                    </div>
                    <div class="col-md-3">
                        <strong>الإدارة:</strong> {{ $employee->department->dept_name ?? 'غير محدد' }}
                    </div>
                    <div class="col-md-3">
                        <strong>الوظيفة:</strong> {{ $employee->post->post_name ?? 'غير محدد' }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-primary">{{ $stats['total_documents'] }}</h3>
                        <p class="mb-0">إجمالي المصوغات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-success">{{ $stats['verified_documents'] }}</h3>
                        <p class="mb-0">المصوغات المتحقق منها</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-warning">{{ $stats['required_documents'] }}</h3>
                        <p class="mb-0">المصوغات المطلوبة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-info">{{ $stats['completion_percentage'] }}%</h3>
                        <p class="mb-0">نسبة الإكمال</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Missing Documents Alert -->
        @if(count($missingDocuments) > 0)
            <div class="alert alert-warning">
                <h6><i class="link-icon" data-feather="alert-triangle"></i> مصوغات مطلوبة مفقودة:</h6>
                <ul class="mb-0">
                    @foreach($missingDocuments as $missing)
                        <li>{{ $missing['label'] }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('admin.employee.documents.index', $employee->id) }}">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="document_type" class="form-label">نوع المصوغة</label>
                            <select name="document_type" id="document_type" class="form-select">
                                <option value="">جميع الأنواع</option>
                                @foreach(\App\Models\EmployeeDocument::DOCUMENT_TYPES as $key => $label)
                                    <option value="{{ $key }}" {{ $filters['document_type'] == $key ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="is_verified" class="form-label">حالة التحقق</label>
                            <select name="is_verified" id="is_verified" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="1" {{ $filters['is_verified'] == '1' ? 'selected' : '' }}>متحقق منها</option>
                                <option value="0" {{ $filters['is_verified'] == '0' ? 'selected' : '' }}>غير متحقق منها</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   value="{{ $filters['search'] }}" placeholder="البحث في اسم المصوغة أو الوصف">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">بحث</button>
                                <a href="{{ route('admin.employee.documents.index', $employee->id) }}" class="btn btn-secondary">إعادة تعيين</a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Documents Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">قائمة المصوغات</h5>
            </div>
            <div class="card-body">
                @if($documents->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>نوع المصوغة</th>
                                    <th>اسم المصوغة</th>
                                    <th>حجم الملف</th>
                                    <th>حالة التحقق</th>
                                    <th>تاريخ الرفع</th>
                                    <th>رفع بواسطة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($documents as $index => $document)
                                    <tr>
                                        <td>{{ $documents->firstItem() + $index }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ $document->document_type_label }}</span>
                                            @if($document->is_required)
                                                <span class="badge bg-warning ms-1">مطلوبة</span>
                                            @endif
                                        </td>
                                        <td>{{ $document->document_name }}</td>
                                        <td>{{ $document->file_size_formatted }}</td>
                                        <td>
                                            @if($document->is_verified)
                                                <span class="badge bg-success">متحقق منها</span>
                                                <small class="d-block text-muted">
                                                    {{ $document->verified_at->format('Y-m-d') }}
                                                    بواسطة {{ $document->verifiedBy->name ?? 'غير محدد' }}
                                                </small>
                                            @else
                                                <span class="badge bg-secondary">غير متحقق منها</span>
                                            @endif
                                        </td>
                                        <td>{{ $document->created_at->format('Y-m-d H:i') }}</td>
                                        <td>{{ $document->uploadedBy->name ?? 'غير محدد' }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.employee.documents.show', $document->id) }}" 
                                                   class="btn btn-sm btn-info" title="عرض">
                                                    <i class="link-icon" data-feather="eye"></i>
                                                </a>
                                                <a href="{{ route('admin.employee.documents.download', $document->id) }}" 
                                                   class="btn btn-sm btn-success" title="تحميل">
                                                    <i class="link-icon" data-feather="download"></i>
                                                </a>
                                                @can('edit_employee')
                                                    <a href="{{ route('admin.employee.documents.edit', $document->id) }}" 
                                                       class="btn btn-sm btn-warning" title="تعديل">
                                                        <i class="link-icon" data-feather="edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-primary" 
                                                            onclick="toggleVerification({{ $document->id }}, {{ $document->is_verified ? 'false' : 'true' }})"
                                                            title="{{ $document->is_verified ? 'إلغاء التحقق' : 'تحقق' }}">
                                                        <i class="link-icon" data-feather="{{ $document->is_verified ? 'x-circle' : 'check-circle' }}"></i>
                                                    </button>
                                                @endcan
                                                @can('delete_employee')
                                                    <button type="button" class="btn btn-sm btn-danger" 
                                                            onclick="deleteDocument({{ $document->id }})" title="حذف">
                                                        <i class="link-icon" data-feather="trash-2"></i>
                                                    </button>
                                                @endcan
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $documents->appends(request()->query())->links() }}
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="link-icon" data-feather="file-text" style="width: 48px; height: 48px;"></i>
                        <h5 class="mt-3">لا توجد مصوغات</h5>
                        <p class="text-muted">لم يتم رفع أي مصوغات لهذا الموظف بعد</p>
                        <a href="{{ route('admin.employee.documents.create', $employee->id) }}" class="btn btn-primary">
                            رفع مصوغة جديدة
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Verification Modal -->
    <div class="modal fade" id="verificationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تحقق من المصوغة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="verificationForm" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="verification_notes" class="form-label">ملاحظات التحقق</label>
                            <textarea name="verification_notes" id="verification_notes" class="form-control" rows="3"></textarea>
                        </div>
                        <input type="hidden" name="is_verified" id="is_verified_input">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف هذه المصوغة؟ لا يمكن التراجع عن هذا الإجراء.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        function toggleVerification(documentId, isVerified) {
            document.getElementById('verificationForm').action = `/admin/employee-documents/${documentId}/verify`;
            document.getElementById('is_verified_input').value = isVerified;
            document.getElementById('verification_notes').value = '';
            
            const modal = new bootstrap.Modal(document.getElementById('verificationModal'));
            modal.show();
        }

        function deleteDocument(documentId) {
            document.getElementById('deleteForm').action = `/admin/employee-documents/${documentId}`;
            
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }
    </script>
@endsection
