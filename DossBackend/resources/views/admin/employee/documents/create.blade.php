@extends('layouts.master')

@section('title', 'رفع مصوغة جديدة - ' . $employee->name)

@section('action')
    <div class="float-end">
        <a href="{{ route('admin.employee.documents.index', $employee->id) }}" class="btn btn-secondary">
            <i class="link-icon" data-feather="arrow-left"></i> العودة لقائمة المصوغات
        </a>
    </div>
@endsection

@section('main-content')
    <section class="content">
        @include('admin.section.flash_message')

        <!-- Employee Info Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">معلومات الموظف</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12">
                        <strong>الموظف:</strong> {{ $employee->name }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Missing Documents Alert -->
        @if(count($missingDocuments) > 0)
            <div class="alert alert-info">
                <h6><i class="link-icon" data-feather="info"></i> مصوغات مطلوبة مفقودة:</h6>
                <ul class="mb-0">
                    @foreach($missingDocuments as $missing)
                        <li>{{ $missing['label'] }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Upload Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">رفع مصوغة جديدة</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.employee.documents.store') }}" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="user_id" value="{{ $employee->id }}">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="document_type" class="form-label">نوع المصوغة <span class="text-danger">*</span></label>
                                <select name="document_type" id="document_type" class="form-select @error('document_type') is-invalid @enderror" required>
                                    <option value="">اختر نوع المصوغة</option>
                                    @foreach($documentTypes as $key => $label)
                                        <option value="{{ $key }}" {{ old('document_type') == $key ? 'selected' : '' }}>
                                            {{ $label }}
                                            @if(in_array($key, \App\Models\EmployeeDocument::REQUIRED_DOCUMENTS))
                                                (مطلوبة)
                                            @endif
                                        </option>
                                    @endforeach
                                </select>
                                @error('document_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="document_name" class="form-label">اسم المصوغة <span class="text-danger">*</span></label>
                                <input type="text" name="document_name" id="document_name" 
                                       class="form-control @error('document_name') is-invalid @enderror" 
                                       value="{{ old('document_name') }}" required
                                       placeholder="أدخل اسم المصوغة">
                                @error('document_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="description" class="form-label">الوصف</label>
                                <textarea name="description" id="description" 
                                          class="form-control @error('description') is-invalid @enderror" 
                                          rows="3" placeholder="وصف اختياري للمصوغة">{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="document_file" class="form-label">ملف المصوغة <span class="text-danger">*</span></label>
                                <input type="file" name="document_file" id="document_file" 
                                       class="form-control @error('document_file') is-invalid @enderror" 
                                       accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" required>
                                <div class="form-text">
                                    الأنواع المدعومة: PDF, DOC, DOCX, JPG, PNG | الحد الأقصى: {{ \App\Models\EmployeeDocument::MAX_FILE_SIZE }} KB
                                </div>
                                @error('document_file')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- File Preview -->
                    <div class="row" id="file-preview" style="display: none;">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">معاينة الملف</h6>
                                </div>
                                <div class="card-body">
                                    <div id="preview-content"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-between">
                                <button type="submit" class="btn btn-primary">
                                    <i class="link-icon" data-feather="upload"></i> رفع المصوغة
                                </button>
                                <a href="{{ route('admin.employee.documents.index', $employee->id) }}" class="btn btn-secondary">
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- File Upload Guidelines -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">إرشادات رفع الملفات</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>الأنواع المدعومة:</h6>
                        <ul>
                            <li>ملفات PDF (.pdf)</li>
                            <li>مستندات Word (.doc, .docx)</li>
                            <li>الصور (.jpg, .jpeg, .png)</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>المتطلبات:</h6>
                        <ul>
                            <li>الحد الأقصى لحجم الملف: {{ \App\Models\EmployeeDocument::MAX_FILE_SIZE }} KB</li>
                            <li>يجب أن تكون الملفات واضحة ومقروءة</li>
                            <li>تأكد من صحة المعلومات قبل الرفع</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('document_file');
            const previewDiv = document.getElementById('file-preview');
            const previewContent = document.getElementById('preview-content');

            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    showFilePreview(file);
                } else {
                    hideFilePreview();
                }
            });

            function showFilePreview(file) {
                const fileSize = (file.size / 1024).toFixed(2);
                const maxSize = {{ \App\Models\EmployeeDocument::MAX_FILE_SIZE }};
                
                let sizeClass = 'text-success';
                let sizeText = 'حجم مناسب';
                
                if (fileSize > maxSize) {
                    sizeClass = 'text-danger';
                    sizeText = 'حجم كبير جداً!';
                }

                let previewHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            <strong>اسم الملف:</strong> ${file.name}
                        </div>
                        <div class="col-md-3">
                            <strong>الحجم:</strong> <span class="${sizeClass}">${fileSize} KB (${sizeText})</span>
                        </div>
                        <div class="col-md-3">
                            <strong>النوع:</strong> ${file.type || 'غير محدد'}
                        </div>
                    </div>
                `;

                // Show image preview for image files
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        previewHtml += `
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <img src="${e.target.result}" class="img-thumbnail" style="max-height: 200px;">
                                </div>
                            </div>
                        `;
                        previewContent.innerHTML = previewHtml;
                    };
                    reader.readAsDataURL(file);
                } else {
                    previewContent.innerHTML = previewHtml;
                }

                previewDiv.style.display = 'block';
            }

            function hideFilePreview() {
                previewDiv.style.display = 'none';
                previewContent.innerHTML = '';
            }

            // Auto-fill document name based on file name
            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                const documentNameInput = document.getElementById('document_name');
                
                if (file && !documentNameInput.value) {
                    // Remove file extension and clean up the name
                    let fileName = file.name.replace(/\.[^/.]+$/, "");
                    fileName = fileName.replace(/[_-]/g, ' ');
                    documentNameInput.value = fileName;
                }
            });

            // Auto-select document type based on missing documents
            const documentTypeSelect = document.getElementById('document_type');
            const missingDocuments = @json($missingDocuments);
            
            if (missingDocuments.length === 1) {
                documentTypeSelect.value = missingDocuments[0].type;
            }
        });
    </script>
@endsection
