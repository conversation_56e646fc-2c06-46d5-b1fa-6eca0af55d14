@extends('layouts.master')

@section('title', 'تعديل المصوغة - ' . $document->document_name)

@section('action')
    <div class="float-end">
        <a href="{{ route('admin.admin.employee.documents.show', $document->id) }}" class="btn btn-info">
            <i class="link-icon" data-feather="eye"></i> عرض المصوغة
        </a>
        <a href="{{ route('admin.admin.employee.documents.index', $document->user_id) }}" class="btn btn-secondary">
            <i class="link-icon" data-feather="arrow-left"></i> العودة للقائمة
        </a>
    </div>
@endsection

@section('main-content')
    <section class="content">
        @include('admin.section.flash_message')

        <!-- Document Info Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">معلومات المصوغة الحالية</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>الموظف:</strong> {{ $document->user->name ?? 'غير محدد' }}
                    </div>
                    <div class="col-md-3">
                        <strong>نوع المصوغة:</strong> {{ $document->document_type_label }}
                    </div>
                    <div class="col-md-3">
                        <strong>الملف الأصلي:</strong> {{ $document->original_name }}
                    </div>
                    <div class="col-md-3">
                        <strong>حجم الملف:</strong> {{ $document->file_size_formatted }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">تعديل معلومات المصوغة</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.admin.employee.documents.update', $document->id) }}">
                    @csrf
                    @method('PUT')

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="document_name" class="form-label">اسم المصوغة <span class="text-danger">*</span></label>
                                <input type="text" name="document_name" id="document_name" 
                                       class="form-control @error('document_name') is-invalid @enderror" 
                                       value="{{ old('document_name', $document->document_name) }}" required
                                       placeholder="أدخل اسم المصوغة">
                                @error('document_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع المصوغة</label>
                                <input type="text" class="form-control" value="{{ $document->document_type_label }}" readonly>
                                <div class="form-text">لا يمكن تغيير نوع المصوغة بعد الرفع</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="description" class="form-label">الوصف</label>
                                <textarea name="description" id="description" 
                                          class="form-control @error('description') is-invalid @enderror" 
                                          rows="4" placeholder="وصف اختياري للمصوغة">{{ old('description', $document->description) }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-between">
                                <button type="submit" class="btn btn-primary">
                                    <i class="link-icon" data-feather="save"></i> حفظ التغييرات
                                </button>
                                <div>
                                    <a href="{{ route('admin.admin.employee.documents.show', $document->id) }}" class="btn btn-info me-2">
                                        عرض المصوغة
                                    </a>
                                    <a href="{{ route('admin.admin.employee.documents.index', $document->user_id) }}" class="btn btn-secondary">
                                        إلغاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Current File Preview -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">الملف الحالي</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">اسم الملف الأصلي:</th>
                                <td>{{ $document->original_name }}</td>
                            </tr>
                            <tr>
                                <th>حجم الملف:</th>
                                <td>{{ $document->file_size_formatted }}</td>
                            </tr>
                            <tr>
                                <th>نوع الملف:</th>
                                <td>{{ strtoupper($document->file_extension) }}</td>
                            </tr>
                            <tr>
                                <th>تاريخ الرفع:</th>
                                <td>{{ $document->created_at->format('Y-m-d H:i') }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        @if(in_array($document->file_extension, ['jpg', 'jpeg', 'png']))
                            <!-- Image Preview -->
                            <div class="text-center">
                                <img src="{{ $document->full_file_path }}" class="img-thumbnail" style="max-height: 200px;" alt="{{ $document->document_name }}">
                            </div>
                        @else
                            <!-- File Icon -->
                            <div class="text-center py-4">
                                <i class="link-icon" data-feather="file-text" style="width: 48px; height: 48px;"></i>
                                <p class="mt-2 mb-0">{{ $document->original_name }}</p>
                            </div>
                        @endif
                        
                        <div class="text-center mt-3">
                            <a href="{{ route('admin.admin.employee.documents.download', $document->id) }}" class="btn btn-sm btn-success">
                                <i class="link-icon" data-feather="download"></i> تحميل
                            </a>
                            <a href="{{ route('admin.admin.employee.documents.show', $document->id) }}" class="btn btn-sm btn-info">
                                <i class="link-icon" data-feather="eye"></i> عرض
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Verification Status -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">حالة التحقق</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <strong class="me-3">الحالة:</strong>
                            @if($document->is_verified)
                                <span class="badge bg-success">متحقق منها</span>
                            @else
                                <span class="badge bg-secondary">غير متحقق منها</span>
                            @endif
                        </div>
                        
                        @if($document->is_verified)
                            <div class="mt-2">
                                <small class="text-muted">
                                    تم التحقق في {{ $document->verified_at->format('Y-m-d H:i') }}
                                    بواسطة {{ $document->verifiedBy->name ?? 'غير محدد' }}
                                </small>
                            </div>
                        @endif
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-end">
                            @if($document->is_verified)
                                <button type="button" class="btn btn-warning btn-sm" onclick="toggleVerification({{ $document->id }}, false)">
                                    <i class="link-icon" data-feather="x-circle"></i> إلغاء التحقق
                                </button>
                            @else
                                <button type="button" class="btn btn-success btn-sm" onclick="toggleVerification({{ $document->id }}, true)">
                                    <i class="link-icon" data-feather="check-circle"></i> تحقق من المصوغة
                                </button>
                            @endif
                        </div>
                    </div>
                </div>

                @if($document->verification_notes)
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <strong>ملاحظات التحقق:</strong>
                            <div class="alert alert-info mt-2">
                                {{ $document->verification_notes }}
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- File Replacement Note -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">ملاحظة مهمة</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="link-icon" data-feather="info"></i>
                    <strong>تنبيه:</strong> يمكن تعديل اسم المصوغة والوصف فقط. لاستبدال الملف، يجب حذف المصوغة الحالية ورفع مصوغة جديدة.
                </div>
            </div>
        </div>
    </section>

    <!-- Verification Modal -->
    <div class="modal fade" id="verificationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تحقق من المصوغة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="verificationForm" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="verification_notes" class="form-label">ملاحظات التحقق</label>
                            <textarea name="verification_notes" id="verification_notes" class="form-control" rows="3" 
                                      placeholder="أدخل ملاحظات التحقق (اختياري)">{{ $document->verification_notes }}</textarea>
                        </div>
                        <input type="hidden" name="is_verified" id="is_verified_input">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        function toggleVerification(documentId, isVerified) {
            document.getElementById('verificationForm').action = `/admin/employee-documents/${documentId}/verify`;
            document.getElementById('is_verified_input').value = isVerified;
            
            const modal = new bootstrap.Modal(document.getElementById('verificationModal'));
            modal.show();
        }
    </script>
@endsection
