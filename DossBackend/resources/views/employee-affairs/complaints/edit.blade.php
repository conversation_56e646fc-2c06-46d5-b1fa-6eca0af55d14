@extends('layouts.app')

@section('title', 'تعديل الشكوى')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        تعديل الشكوى #{{ $complaint->id }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-complaints.index') }}">الشكاوى</a></li>
                            <li class="breadcrumb-item active">تعديل الشكوى</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('employee-complaints.show', $complaint->id) }}" class="btn btn-info btn-sm">
                        <i class="fas fa-eye"></i>
                        عرض التفاصيل
                    </a>
                    <a href="{{ route('employee-complaints.index') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-right"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Warning -->
    @if($complaint->status !== 'pending')
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                @if($complaint->status === 'under_investigation')
                    هذه الشكوى قيد التحقيق حالياً. يمكن تعديل بعض الحقول فقط.
                @elseif($complaint->status === 'resolved')
                    هذه الشكوى تم حلها. لا يمكن تعديل المحتوى الأساسي.
                @elseif($complaint->status === 'dismissed')
                    هذه الشكوى تم رفضها. يمكنك إنشاء شكوى جديدة بدلاً من ذلك.
                @elseif($complaint->status === 'closed')
                    هذه الشكوى مغلقة ولا يمكن تعديلها.
                @endif
            </div>
        </div>
    </div>
    @endif

    <!-- Confidentiality Alert -->
    @if($complaint->is_anonymous || $complaint->confidentiality_level === 'high')
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-danger">
                <i class="fas fa-user-secret"></i>
                <strong>تنبيه سرية:</strong>
                @if($complaint->is_anonymous)
                    هذه شكوى مجهولة. يجب الحفاظ على سرية هوية مقدم الشكوى.
                @endif
                @if($complaint->confidentiality_level === 'high')
                    هذه الشكوى ذات مستوى سرية عالي. يجب التعامل معها بحذر شديد.
                @endif
            </div>
        </div>
    </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <!-- Main Form -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit"></i>
                        تعديل بيانات الشكوى
                    </h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('employee-complaints.update', $complaint->id) }}" method="POST" id="complaint-form">
                        @csrf
                        @method('PUT')
                        
                        <!-- Employee Info (Conditional Display) -->
                        @if(!$complaint->is_anonymous)
                        <div class="form-group">
                            <label>معلومات مقدم الشكوى</label>
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <div class="d-flex align-items-center">
                                        <img src="{{ $complaint->employee->avatar ?? '/images/default-avatar.png' }}" 
                                             class="rounded-circle mr-3" width="40" height="40" alt="صورة الموظف">
                                        <div>
                                            <strong>{{ $complaint->employee->name }}</strong><br>
                                            <small class="text-muted">{{ $complaint->employee->employee_code }} - {{ $complaint->employee->department->name ?? 'غير محدد' }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @else
                        <div class="form-group">
                            <div class="alert alert-info">
                                <i class="fas fa-user-secret"></i>
                                <strong>شكوى مجهولة</strong> - تم إخفاء معلومات مقدم الشكوى للحفاظ على السرية
                            </div>
                        </div>
                        @endif

                        <!-- Subject -->
                        <div class="form-group">
                            <label for="subject">موضوع الشكوى <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('subject') is-invalid @enderror" 
                                   id="subject" name="subject" value="{{ old('subject', $complaint->subject) }}" 
                                   {{ in_array($complaint->status, ['resolved', 'closed']) ? 'readonly' : '' }}
                                   placeholder="موضوع مختصر وواضح للشكوى" required>
                            @error('subject')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Category -->
                        <div class="form-group">
                            <label for="category">فئة الشكوى <span class="text-danger">*</span></label>
                            <select class="form-control @error('category') is-invalid @enderror" 
                                    id="category" name="category" 
                                    {{ in_array($complaint->status, ['resolved', 'closed']) ? 'disabled' : '' }}
                                    required>
                                <option value="">اختر فئة الشكوى</option>
                                <option value="harassment" {{ old('category', $complaint->category) == 'harassment' ? 'selected' : '' }}>تحرش</option>
                                <option value="discrimination" {{ old('category', $complaint->category) == 'discrimination' ? 'selected' : '' }}>تمييز</option>
                                <option value="workplace_safety" {{ old('category', $complaint->category) == 'workplace_safety' ? 'selected' : '' }}>سلامة مكان العمل</option>
                                <option value="management_issues" {{ old('category', $complaint->category) == 'management_issues' ? 'selected' : '' }}>مشاكل إدارية</option>
                                <option value="policy_violation" {{ old('category', $complaint->category) == 'policy_violation' ? 'selected' : '' }}>مخالفة السياسات</option>
                                <option value="ethical_concerns" {{ old('category', $complaint->category) == 'ethical_concerns' ? 'selected' : '' }}>مخاوف أخلاقية</option>
                                <option value="financial_irregularities" {{ old('category', $complaint->category) == 'financial_irregularities' ? 'selected' : '' }}>مخالفات مالية</option>
                                <option value="other" {{ old('category', $complaint->category) == 'other' ? 'selected' : '' }}>أخرى</option>
                            </select>
                            @error('category')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Priority -->
                        <div class="form-group">
                            <label for="priority">أولوية الشكوى <span class="text-danger">*</span></label>
                            <select class="form-control @error('priority') is-invalid @enderror" 
                                    id="priority" name="priority" required>
                                <option value="">اختر أولوية الشكوى</option>
                                <option value="low" {{ old('priority', $complaint->priority) == 'low' ? 'selected' : '' }}>منخفضة</option>
                                <option value="medium" {{ old('priority', $complaint->priority) == 'medium' ? 'selected' : '' }}>متوسطة</option>
                                <option value="high" {{ old('priority', $complaint->priority) == 'high' ? 'selected' : '' }}>عالية</option>
                                <option value="urgent" {{ old('priority', $complaint->priority) == 'urgent' ? 'selected' : '' }}>عاجلة</option>
                            </select>
                            @error('priority')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="form-group">
                            <label for="description">وصف الشكوى <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="5" 
                                      {{ in_array($complaint->status, ['resolved', 'closed']) ? 'readonly' : '' }}
                                      placeholder="وصف تفصيلي للشكوى والأحداث المتعلقة بها" required>{{ old('description', $complaint->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Incident Date -->
                        <div class="form-group">
                            <label for="incident_date">تاريخ الحادثة</label>
                            <input type="date" class="form-control @error('incident_date') is-invalid @enderror" 
                                   id="incident_date" name="incident_date" 
                                   value="{{ old('incident_date', $complaint->incident_date ? $complaint->incident_date->format('Y-m-d') : '') }}" 
                                   {{ in_array($complaint->status, ['resolved', 'closed']) ? 'readonly' : '' }}
                                   max="{{ date('Y-m-d') }}">
                            @error('incident_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Location -->
                        <div class="form-group">
                            <label for="location">مكان الحادثة</label>
                            <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                   id="location" name="location" value="{{ old('location', $complaint->location) }}" 
                                   {{ in_array($complaint->status, ['resolved', 'closed']) ? 'readonly' : '' }}
                                   placeholder="مكان حدوث الشكوى (مكتب، قسم، إلخ)">
                            @error('location')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Witnesses -->
                        <div class="form-group">
                            <label for="witnesses">الشهود</label>
                            <textarea class="form-control @error('witnesses') is-invalid @enderror" 
                                      id="witnesses" name="witnesses" rows="3" 
                                      {{ in_array($complaint->status, ['resolved', 'closed']) ? 'readonly' : '' }}
                                      placeholder="أسماء الشهود أو الأشخاص الذين شاهدوا الحادثة">{{ old('witnesses', $complaint->witnesses) }}</textarea>
                            @error('witnesses')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Evidence Description -->
                        <div class="form-group">
                            <label for="evidence_description">وصف الأدلة</label>
                            <textarea class="form-control @error('evidence_description') is-invalid @enderror" 
                                      id="evidence_description" name="evidence_description" rows="3" 
                                      placeholder="وصف للأدلة المتاحة (مستندات، صور، تسجيلات، إلخ)">{{ old('evidence_description', $complaint->evidence_description) }}</textarea>
                            @error('evidence_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Desired Resolution -->
                        <div class="form-group">
                            <label for="desired_resolution">الحل المطلوب</label>
                            <textarea class="form-control @error('desired_resolution') is-invalid @enderror" 
                                      id="desired_resolution" name="desired_resolution" rows="3" 
                                      placeholder="ما هو الحل أو الإجراء الذي تتوقعه لحل هذه الشكوى؟">{{ old('desired_resolution', $complaint->desired_resolution) }}</textarea>
                            @error('desired_resolution')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Confidentiality Level -->
                        <div class="form-group">
                            <label for="confidentiality_level">مستوى السرية <span class="text-danger">*</span></label>
                            <select class="form-control @error('confidentiality_level') is-invalid @enderror" 
                                    id="confidentiality_level" name="confidentiality_level" required>
                                <option value="low" {{ old('confidentiality_level', $complaint->confidentiality_level) == 'low' ? 'selected' : '' }}>منخفض</option>
                                <option value="medium" {{ old('confidentiality_level', $complaint->confidentiality_level) == 'medium' ? 'selected' : '' }}>متوسط</option>
                                <option value="high" {{ old('confidentiality_level', $complaint->confidentiality_level) == 'high' ? 'selected' : '' }}>عالي</option>
                            </select>
                            @error('confidentiality_level')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Additional Notes -->
                        <div class="form-group">
                            <label for="additional_notes">ملاحظات إضافية</label>
                            <textarea class="form-control @error('additional_notes') is-invalid @enderror" 
                                      id="additional_notes" name="additional_notes" rows="3" 
                                      placeholder="أي ملاحظات أو معلومات إضافية">{{ old('additional_notes', $complaint->additional_notes) }}</textarea>
                            @error('additional_notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Form Actions -->
                        <div class="form-group">
                            @if(!in_array($complaint->status, ['resolved', 'closed']))
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    حفظ التعديلات
                                </button>
                            @endif
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                            <a href="{{ route('employee-complaints.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Current Status -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i>
                        حالة الشكوى
                    </h6>
                </div>
                <div class="card-body">
                    <p><strong>الحالة الحالية:</strong> 
                        @switch($complaint->status)
                            @case('pending')
                                <span class="badge badge-warning">في الانتظار</span>
                                @break
                            @case('under_investigation')
                                <span class="badge badge-info">قيد التحقيق</span>
                                @break
                            @case('resolved')
                                <span class="badge badge-success">محلولة</span>
                                @break
                            @case('dismissed')
                                <span class="badge badge-danger">مرفوضة</span>
                                @break
                            @case('closed')
                                <span class="badge badge-secondary">مغلقة</span>
                                @break
                        @endswitch
                    </p>
                    <p><strong>تاريخ الإنشاء:</strong> {{ $complaint->created_at->format('Y-m-d H:i') }}</p>
                    @if($complaint->investigation_started_at)
                        <p><strong>بداية التحقيق:</strong> {{ $complaint->investigation_started_at->format('Y-m-d H:i') }}</p>
                    @endif
                    @if($complaint->resolved_at)
                        <p><strong>تاريخ الحل:</strong> {{ $complaint->resolved_at->format('Y-m-d H:i') }}</p>
                    @endif
                </div>
            </div>

            <!-- Edit Guidelines -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        إرشادات التعديل
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            يمكن تعديل الأولوية والملاحظات في أي وقت
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            يمكن تحديث وصف الأدلة والحل المطلوب
                        </li>
                        @if($complaint->status === 'pending')
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i>
                                يمكن تعديل جميع الحقول للشكاوى المعلقة
                            </li>
                        @endif
                        @if(in_array($complaint->status, ['resolved', 'closed']))
                            <li class="mb-2">
                                <i class="fas fa-times text-danger"></i>
                                لا يمكن تعديل المحتوى الأساسي للشكاوى المحلولة
                            </li>
                        @endif
                        @if($complaint->is_anonymous)
                            <li class="mb-2">
                                <i class="fas fa-user-secret text-warning"></i>
                                يجب الحفاظ على سرية هوية مقدم الشكوى
                            </li>
                        @endif
                    </ul>
                </div>
            </div>

            <!-- Confidentiality Guide -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-shield-alt"></i>
                        دليل السرية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-success">منخفض</h6>
                        <small>يمكن مشاركة المعلومات مع الإدارة المباشرة</small>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-warning">متوسط</h6>
                        <small>يقتصر الوصول على الإدارة العليا وقسم الموارد البشرية</small>
                    </div>
                    <div>
                        <h6 class="text-danger">عالي</h6>
                        <small>وصول محدود جداً - الإدارة التنفيذية فقط</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Form validation
    $('#complaint-form').on('submit', function(e) {
        let isValid = true;

        // Check required fields
        const requiredFields = ['subject', 'category', 'priority', 'description', 'confidentiality_level'];
        requiredFields.forEach(function(field) {
            const element = $(`#${field}`);
            if (!element.val().trim()) {
                element.addClass('is-invalid');
                isValid = false;
            } else {
                element.removeClass('is-invalid');
            }
        });

        // Validate incident date (cannot be in future)
        const incidentDate = $('#incident_date').val();
        if (incidentDate && new Date(incidentDate) > new Date()) {
            $('#incident_date').addClass('is-invalid');
            alert('تاريخ الحادثة لا يمكن أن يكون في المستقبل');
            isValid = false;
        }

        // Check description length
        const description = $('#description').val();
        if (description.length < 10) {
            $('#description').addClass('is-invalid');
            alert('وصف الشكوى يجب أن يكون 10 أحرف على الأقل');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            return false;
        }

        // Show loading state
        $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');
    });

    // Real-time validation
    $('#subject, #description').on('input', function() {
        const element = $(this);
        if (element.val().trim()) {
            element.removeClass('is-invalid');
        }
    });

    // Category change handler
    $('#category').on('change', function() {
        const category = $(this).val();

        // Auto-set priority based on category
        if (category === 'harassment' || category === 'discrimination' || category === 'workplace_safety') {
            $('#priority').val('high');
        } else if (category === 'financial_irregularities' || category === 'ethical_concerns') {
            $('#priority').val('urgent');
        }

        // Auto-set confidentiality level
        if (category === 'harassment' || category === 'discrimination' || category === 'financial_irregularities') {
            $('#confidentiality_level').val('high');
        } else if (category === 'ethical_concerns' || category === 'policy_violation') {
            $('#confidentiality_level').val('medium');
        }

        $(this).removeClass('is-invalid');
    });

    // Priority change handler
    $('#priority').on('change', function() {
        $(this).removeClass('is-invalid');

        // Show warning for urgent complaints
        if ($(this).val() === 'urgent') {
            if (!$('#urgent-warning').length) {
                $(this).after('<div id="urgent-warning" class="alert alert-warning mt-2"><i class="fas fa-exclamation-triangle"></i> الشكاوى العاجلة تتطلب معالجة فورية</div>');
            }
        } else {
            $('#urgent-warning').remove();
        }
    });

    // Confidentiality level change handler
    $('#confidentiality_level').on('change', function() {
        $(this).removeClass('is-invalid');

        const level = $(this).val();
        $('#confidentiality-info').remove();

        let infoText = '';
        let alertClass = 'alert-info';

        switch(level) {
            case 'high':
                infoText = 'مستوى السرية العالي: وصول محدود جداً للمعلومات';
                alertClass = 'alert-danger';
                break;
            case 'medium':
                infoText = 'مستوى السرية المتوسط: وصول محدود للإدارة العليا';
                alertClass = 'alert-warning';
                break;
            case 'low':
                infoText = 'مستوى السرية المنخفض: يمكن مشاركة المعلومات مع الإدارة المباشرة';
                alertClass = 'alert-info';
                break;
        }

        if (infoText) {
            $(this).after(`<div id="confidentiality-info" class="alert ${alertClass} mt-2"><i class="fas fa-info-circle"></i> ${infoText}</div>`);
        }
    });

    // Character counter for description
    $('#description').on('input', function() {
        const current = $(this).val().length;
        const min = 10;
        const max = 2000;

        $('#char-counter').remove();

        let counterClass = 'text-muted';
        if (current < min) {
            counterClass = 'text-danger';
        } else if (current > max * 0.9) {
            counterClass = 'text-warning';
        }

        $(this).after(`<small id="char-counter" class="${counterClass}">عدد الأحرف: ${current} (الحد الأدنى: ${min})</small>`);
    });

    // Incident date validation
    $('#incident_date').on('change', function() {
        const selectedDate = new Date($(this).val());
        const today = new Date();

        if (selectedDate > today) {
            $(this).addClass('is-invalid');
            $(this).after('<div class="invalid-feedback">تاريخ الحادثة لا يمكن أن يكون في المستقبل</div>');
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // Auto-save draft functionality (every 2 minutes)
    let autoSaveInterval;
    if (!$('input[name="_method"]').length || $('input[name="_method"]').val() !== 'PUT') {
        autoSaveInterval = setInterval(function() {
            saveDraft();
        }, 120000); // 2 minutes
    }

    // Clear auto-save on form submit
    $('#complaint-form').on('submit', function() {
        if (autoSaveInterval) {
            clearInterval(autoSaveInterval);
        }
    });
});

function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع التغييرات غير المحفوظة.')) {
        document.getElementById('complaint-form').reset();
        $('.is-invalid').removeClass('is-invalid');
        $('.alert').not('.alert-warning:first, .alert-danger:first, .alert-info:first').remove();
        $('#char-counter, #urgent-warning, #confidentiality-info').remove();
    }
}

function saveDraft() {
    const formData = new FormData(document.getElementById('complaint-form'));
    formData.append('is_draft', '1');

    $.ajax({
        url: '{{ route("employee-complaints.save-draft") }}',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            // Show brief success message
            if (!$('#draft-saved').length) {
                $('body').append('<div id="draft-saved" class="alert alert-success position-fixed" style="top: 20px; right: 20px; z-index: 9999;"><i class="fas fa-check"></i> تم حفظ المسودة</div>');
                setTimeout(function() {
                    $('#draft-saved').fadeOut(function() {
                        $(this).remove();
                    });
                }, 3000);
            }
        },
        error: function() {
            // Silently fail for draft saves
        }
    });
}

// Prevent accidental navigation away
window.addEventListener('beforeunload', function(e) {
    const form = document.getElementById('complaint-form');
    const formData = new FormData(form);
    let hasChanges = false;

    // Check if form has been modified
    for (let [key, value] of formData.entries()) {
        if (key !== '_token' && key !== '_method' && value.toString().trim() !== '') {
            hasChanges = true;
            break;
        }
    }

    if (hasChanges && !form.submitted) {
        e.preventDefault();
        e.returnValue = '';
        return '';
    }
});

// Mark form as submitted to prevent beforeunload warning
$('#complaint-form').on('submit', function() {
    this.submitted = true;
});
</script>
@endsection
