@extends('layouts.app')

@section('title', 'تفاصيل الشكوى')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        تفاصيل الشكوى #{{ $complaint->id }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-complaints.index') }}">الشكاوى</a></li>
                            <li class="breadcrumb-item active">تفاصيل الشكوى</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button onclick="window.print()" class="btn btn-secondary btn-sm">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                    @if($complaint->status === 'pending')
                        <a href="{{ route('employee-complaints.edit', $complaint->id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </a>
                    @endif
                    <a href="{{ route('employee-complaints.index') }}" class="btn btn-info btn-sm">
                        <i class="fas fa-arrow-right"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Actions -->
    @if($complaint->status === 'pending')
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning shadow">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <button type="button" class="btn btn-info btn-block" data-toggle="modal" data-target="#investigateModal">
                                <i class="fas fa-search"></i>
                                بدء التحقيق
                            </button>
                        </div>
                        <div class="col-md-4">
                            <button type="button" class="btn btn-success btn-block" data-toggle="modal" data-target="#resolveModal">
                                <i class="fas fa-check"></i>
                                حل الشكوى
                            </button>
                        </div>
                        <div class="col-md-4">
                            <button type="button" class="btn btn-danger btn-block" data-toggle="modal" data-target="#dismissModal">
                                <i class="fas fa-times"></i>
                                رفض الشكوى
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @elseif($complaint->status === 'under_investigation')
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info shadow">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-success btn-block" data-toggle="modal" data-target="#resolveModal">
                                <i class="fas fa-check"></i>
                                حل الشكوى
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-warning btn-block" data-toggle="modal" data-target="#updateInvestigationModal">
                                <i class="fas fa-edit"></i>
                                تحديث التحقيق
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Confidentiality Alert -->
    @if($complaint->is_anonymous || $complaint->confidentiality_level === 'high')
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-danger">
                <i class="fas fa-user-secret"></i>
                <strong>تنبيه سرية:</strong>
                @if($complaint->is_anonymous)
                    هذه شكوى مجهولة. يجب الحفاظ على سرية هوية مقدم الشكوى.
                @endif
                @if($complaint->confidentiality_level === 'high')
                    هذه الشكوى ذات مستوى سرية عالي. يجب التعامل معها بحذر شديد.
                @endif
            </div>
        </div>
    </div>
    @endif

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Complaint Details -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-file-alt"></i>
                        تفاصيل الشكوى
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>موضوع الشكوى:</strong></p>
                            <p class="text-muted">{{ $complaint->subject }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>فئة الشكوى:</strong></p>
                            <p>
                                @switch($complaint->category)
                                    @case('harassment')
                                        <span class="badge badge-danger">تحرش</span>
                                        @break
                                    @case('discrimination')
                                        <span class="badge badge-warning">تمييز</span>
                                        @break
                                    @case('workplace_safety')
                                        <span class="badge badge-danger">سلامة مكان العمل</span>
                                        @break
                                    @case('management_issues')
                                        <span class="badge badge-info">مشاكل إدارية</span>
                                        @break
                                    @case('policy_violation')
                                        <span class="badge badge-warning">مخالفة السياسات</span>
                                        @break
                                    @case('ethical_concerns')
                                        <span class="badge badge-primary">مخاوف أخلاقية</span>
                                        @break
                                    @case('financial_irregularities')
                                        <span class="badge badge-danger">مخالفات مالية</span>
                                        @break
                                    @default
                                        <span class="badge badge-secondary">أخرى</span>
                                @endswitch
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>الأولوية:</strong></p>
                            <p>
                                @switch($complaint->priority)
                                    @case('urgent')
                                        <span class="badge badge-danger">عاجلة</span>
                                        @break
                                    @case('high')
                                        <span class="badge badge-warning">عالية</span>
                                        @break
                                    @case('medium')
                                        <span class="badge badge-info">متوسطة</span>
                                        @break
                                    @default
                                        <span class="badge badge-secondary">منخفضة</span>
                                @endswitch
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>مستوى السرية:</strong></p>
                            <p>
                                @switch($complaint->confidentiality_level)
                                    @case('high')
                                        <span class="badge badge-danger">عالي</span>
                                        @break
                                    @case('medium')
                                        <span class="badge badge-warning">متوسط</span>
                                        @break
                                    @default
                                        <span class="badge badge-info">منخفض</span>
                                @endswitch
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <p><strong>وصف الشكوى:</strong></p>
                            <div class="bg-light p-3 rounded">
                                {{ $complaint->description }}
                            </div>
                        </div>
                    </div>

                    @if($complaint->incident_date)
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <p><strong>تاريخ الحادثة:</strong></p>
                            <p class="text-muted">{{ $complaint->incident_date->format('Y-m-d') }}</p>
                        </div>
                        @if($complaint->location)
                        <div class="col-md-6">
                            <p><strong>مكان الحادثة:</strong></p>
                            <p class="text-muted">{{ $complaint->location }}</p>
                        </div>
                        @endif
                    </div>
                    @endif

                    @if($complaint->witnesses)
                    <div class="row mt-3">
                        <div class="col-12">
                            <p><strong>الشهود:</strong></p>
                            <div class="bg-light p-3 rounded">
                                {{ $complaint->witnesses }}
                            </div>
                        </div>
                    </div>
                    @endif

                    @if($complaint->evidence_description)
                    <div class="row mt-3">
                        <div class="col-12">
                            <p><strong>وصف الأدلة:</strong></p>
                            <div class="bg-light p-3 rounded">
                                {{ $complaint->evidence_description }}
                            </div>
                        </div>
                    </div>
                    @endif

                    @if($complaint->desired_resolution)
                    <div class="row mt-3">
                        <div class="col-12">
                            <p><strong>الحل المطلوب:</strong></p>
                            <div class="bg-light p-3 rounded">
                                {{ $complaint->desired_resolution }}
                            </div>
                        </div>
                    </div>
                    @endif

                    @if($complaint->additional_notes)
                    <div class="row mt-3">
                        <div class="col-12">
                            <p><strong>ملاحظات إضافية:</strong></p>
                            <div class="bg-light p-3 rounded">
                                {{ $complaint->additional_notes }}
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Investigation History -->
            @if($complaint->investigation_notes || $complaint->status !== 'pending')
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-search"></i>
                        سجل التحقيق
                    </h6>
                </div>
                <div class="card-body">
                    @if($complaint->investigation_notes)
                        <div class="mb-3">
                            <h6 class="text-primary">ملاحظات التحقيق:</h6>
                            <div class="bg-light p-3 rounded">
                                {{ $complaint->investigation_notes }}
                            </div>
                        </div>
                    @endif

                    @if($complaint->resolution_details)
                        <div class="mb-3">
                            <h6 class="text-success">تفاصيل الحل:</h6>
                            <div class="bg-light p-3 rounded">
                                {{ $complaint->resolution_details }}
                            </div>
                        </div>
                    @endif

                    @if($complaint->dismissal_reason)
                        <div class="mb-3">
                            <h6 class="text-danger">سبب الرفض:</h6>
                            <div class="bg-light p-3 rounded">
                                {{ $complaint->dismissal_reason }}
                            </div>
                        </div>
                    @endif

                    <!-- Timeline -->
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">إنشاء الشكوى</h6>
                                <p class="timeline-text">{{ $complaint->created_at->format('Y-m-d H:i') }}</p>
                            </div>
                        </div>

                        @if($complaint->investigation_started_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">بداية التحقيق</h6>
                                <p class="timeline-text">{{ $complaint->investigation_started_at->format('Y-m-d H:i') }}</p>
                            </div>
                        </div>
                        @endif

                        @if($complaint->resolved_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">حل الشكوى</h6>
                                <p class="timeline-text">{{ $complaint->resolved_at->format('Y-m-d H:i') }}</p>
                            </div>
                        </div>
                        @endif

                        @if($complaint->dismissed_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">رفض الشكوى</h6>
                                <p class="timeline-text">{{ $complaint->dismissed_at->format('Y-m-d H:i') }}</p>
                            </div>
                        </div>
                        @endif

                        @if($complaint->closed_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-secondary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">إغلاق الشكوى</h6>
                                <p class="timeline-text">{{ $complaint->closed_at->format('Y-m-d H:i') }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Employee Information -->
            @if(!$complaint->is_anonymous)
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-user"></i>
                        معلومات مقدم الشكوى
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <img src="{{ $complaint->employee->avatar ?? '/images/default-avatar.png' }}" 
                             class="rounded-circle" width="80" height="80" alt="صورة الموظف">
                    </div>
                    <div class="text-center">
                        <h5 class="mb-1">{{ $complaint->employee->name }}</h5>
                        <p class="text-muted mb-2">{{ $complaint->employee->employee_code }}</p>
                        <p class="text-muted mb-2">{{ $complaint->employee->department->name ?? 'غير محدد' }}</p>
                        <p class="text-muted mb-0">{{ $complaint->employee->position->title ?? 'غير محدد' }}</p>
                    </div>
                    
                    @if($complaint->employee->phone)
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <p class="mb-1"><strong>الهاتف:</strong></p>
                            <p class="text-muted">{{ $complaint->employee->phone }}</p>
                        </div>
                    </div>
                    @endif
                    
                    @if($complaint->employee->email)
                    <div class="row">
                        <div class="col-12">
                            <p class="mb-1"><strong>البريد الإلكتروني:</strong></p>
                            <p class="text-muted">{{ $complaint->employee->email }}</p>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            @else
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-user-secret"></i>
                        شكوى مجهولة
                    </h6>
                </div>
                <div class="card-body text-center">
                    <i class="fas fa-user-secret fa-3x text-muted mb-3"></i>
                    <p class="text-muted">تم إخفاء معلومات مقدم الشكوى للحفاظ على السرية</p>
                </div>
            </div>
            @endif

            <!-- Status Information -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i>
                        معلومات الحالة
                    </h6>
                </div>
                <div class="card-body">
                    <p><strong>الحالة الحالية:</strong></p>
                    <p class="mb-3">
                        @switch($complaint->status)
                            @case('pending')
                                <span class="badge badge-warning">في الانتظار</span>
                                @break
                            @case('under_investigation')
                                <span class="badge badge-info">قيد التحقيق</span>
                                @break
                            @case('resolved')
                                <span class="badge badge-success">محلولة</span>
                                @break
                            @case('dismissed')
                                <span class="badge badge-danger">مرفوضة</span>
                                @break
                            @case('closed')
                                <span class="badge badge-secondary">مغلقة</span>
                                @break
                        @endswitch
                    </p>

                    <p><strong>تاريخ الإنشاء:</strong></p>
                    <p class="text-muted mb-3">{{ $complaint->created_at->format('Y-m-d H:i') }}</p>

                    @if($complaint->investigation_started_at)
                        <p><strong>بداية التحقيق:</strong></p>
                        <p class="text-muted mb-3">{{ $complaint->investigation_started_at->format('Y-m-d H:i') }}</p>
                    @endif

                    @if($complaint->resolved_at)
                        <p><strong>تاريخ الحل:</strong></p>
                        <p class="text-muted mb-3">{{ $complaint->resolved_at->format('Y-m-d H:i') }}</p>
                    @endif

                    @if($complaint->dismissed_at)
                        <p><strong>تاريخ الرفض:</strong></p>
                        <p class="text-muted mb-3">{{ $complaint->dismissed_at->format('Y-m-d H:i') }}</p>
                    @endif

                    <!-- Duration Calculation -->
                    @php
                        $duration = null;
                        if ($complaint->resolved_at) {
                            $duration = $complaint->created_at->diffInDays($complaint->resolved_at);
                        } elseif ($complaint->dismissed_at) {
                            $duration = $complaint->created_at->diffInDays($complaint->dismissed_at);
                        } else {
                            $duration = $complaint->created_at->diffInDays(now());
                        }
                    @endphp

                    <p><strong>المدة:</strong></p>
                    <p class="text-muted">{{ $duration }} يوم</p>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt"></i>
                        إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if($complaint->status === 'pending')
                            <button type="button" class="btn btn-info btn-sm" data-toggle="modal" data-target="#investigateModal">
                                <i class="fas fa-search"></i>
                                بدء التحقيق
                            </button>
                        @endif
                        
                        @if(in_array($complaint->status, ['pending', 'under_investigation']))
                            <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#resolveModal">
                                <i class="fas fa-check"></i>
                                حل الشكوى
                            </button>
                        @endif
                        
                        @if($complaint->status === 'resolved')
                            <button type="button" class="btn btn-secondary btn-sm" data-toggle="modal" data-target="#closeModal">
                                <i class="fas fa-lock"></i>
                                إغلاق الشكوى
                            </button>
                        @endif
                        
                        <button onclick="window.print()" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-print"></i>
                            طباعة التقرير
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Investigation Modal -->
<div class="modal fade" id="investigateModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-search text-info"></i>
                    بدء التحقيق في الشكوى
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-complaints.investigate', $complaint->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        سيتم تغيير حالة الشكوى إلى "قيد التحقيق" وتسجيل تاريخ بداية التحقيق.
                    </div>

                    <div class="form-group">
                        <label for="investigator_id">المحقق المسؤول <span class="text-danger">*</span></label>
                        <select class="form-control select2" id="investigator_id" name="investigator_id" required>
                            <option value="">اختر المحقق</option>
                            @foreach($investigators as $investigator)
                                <option value="{{ $investigator->id }}">{{ $investigator->name }} - {{ $investigator->department->name ?? '' }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="investigation_priority">أولوية التحقيق <span class="text-danger">*</span></label>
                        <select class="form-control" id="investigation_priority" name="investigation_priority" required>
                            <option value="normal">عادية</option>
                            <option value="high" {{ $complaint->priority === 'high' ? 'selected' : '' }}>عالية</option>
                            <option value="urgent" {{ $complaint->priority === 'urgent' ? 'selected' : '' }}>عاجلة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="expected_completion_date">التاريخ المتوقع للانتهاء</label>
                        <input type="date" class="form-control" id="expected_completion_date" name="expected_completion_date"
                               min="{{ date('Y-m-d', strtotime('+1 day')) }}" value="{{ date('Y-m-d', strtotime('+7 days')) }}">
                    </div>

                    <div class="form-group">
                        <label for="investigation_notes">ملاحظات بداية التحقيق <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="investigation_notes" name="investigation_notes" rows="4"
                                  placeholder="خطة التحقيق والخطوات المبدئية..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-search"></i>
                        بدء التحقيق
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Resolve Modal -->
<div class="modal fade" id="resolveModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check text-success"></i>
                    حل الشكوى
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-complaints.resolve', $complaint->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        سيتم تغيير حالة الشكوى إلى "محلولة" وتسجيل تاريخ الحل.
                    </div>

                    <div class="form-group">
                        <label for="resolution_type">نوع الحل <span class="text-danger">*</span></label>
                        <select class="form-control" id="resolution_type" name="resolution_type" required>
                            <option value="">اختر نوع الحل</option>
                            <option value="corrective_action">إجراء تصحيحي</option>
                            <option value="policy_change">تغيير السياسة</option>
                            <option value="training">تدريب</option>
                            <option value="disciplinary_action">إجراء تأديبي</option>
                            <option value="mediation">وساطة</option>
                            <option value="compensation">تعويض</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="resolution_details">تفاصيل الحل <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="resolution_details" name="resolution_details" rows="5"
                                  placeholder="وصف تفصيلي للحل المطبق والإجراءات المتخذة..." required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="actions_taken">الإجراءات المتخذة</label>
                        <textarea class="form-control" id="actions_taken" name="actions_taken" rows="3"
                                  placeholder="قائمة بالإجراءات العملية التي تم تنفيذها..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="follow_up_required">متابعة مطلوبة؟</label>
                        <select class="form-control" id="follow_up_required" name="follow_up_required">
                            <option value="0">لا</option>
                            <option value="1">نعم</option>
                        </select>
                    </div>

                    <div class="form-group" id="follow_up_details" style="display: none;">
                        <label for="follow_up_date">تاريخ المتابعة</label>
                        <input type="date" class="form-control" id="follow_up_date" name="follow_up_date"
                               min="{{ date('Y-m-d', strtotime('+1 day')) }}">
                        <small class="form-text text-muted">تاريخ المراجعة للتأكد من فعالية الحل</small>
                    </div>

                    <div class="form-group">
                        <label for="satisfaction_level">مستوى رضا مقدم الشكوى</label>
                        <select class="form-control" id="satisfaction_level" name="satisfaction_level">
                            <option value="">غير محدد</option>
                            <option value="very_satisfied">راضي جداً</option>
                            <option value="satisfied">راضي</option>
                            <option value="neutral">محايد</option>
                            <option value="dissatisfied">غير راضي</option>
                            <option value="very_dissatisfied">غير راضي جداً</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i>
                        حل الشكوى
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Dismiss Modal -->
<div class="modal fade" id="dismissModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-times text-danger"></i>
                    رفض الشكوى
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-complaints.dismiss', $complaint->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> رفض الشكوى يعني أنها لا تستحق المتابعة أو التحقيق.
                    </div>

                    <div class="form-group">
                        <label for="dismissal_reason">سبب الرفض <span class="text-danger">*</span></label>
                        <select class="form-control" id="dismissal_reason_select" name="dismissal_reason_select" required>
                            <option value="">اختر سبب الرفض</option>
                            <option value="insufficient_evidence">أدلة غير كافية</option>
                            <option value="outside_jurisdiction">خارج الاختصاص</option>
                            <option value="frivolous">شكوى تافهة</option>
                            <option value="duplicate">شكوى مكررة</option>
                            <option value="resolved_informally">تم حلها بشكل غير رسمي</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="dismissal_reason">تفاصيل سبب الرفض <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="dismissal_reason" name="dismissal_reason" rows="4"
                                  placeholder="اشرح بالتفصيل سبب رفض هذه الشكوى..." required></textarea>
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="notify_complainant" name="notify_complainant" value="1" checked>
                            <label class="custom-control-label" for="notify_complainant">
                                إشعار مقدم الشكوى بالرفض
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times"></i>
                        رفض الشكوى
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Update Investigation Modal -->
<div class="modal fade" id="updateInvestigationModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit text-warning"></i>
                    تحديث التحقيق
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-complaints.update-investigation', $complaint->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="investigation_update">تحديث التحقيق <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="investigation_update" name="investigation_update" rows="5"
                                  placeholder="آخر التطورات في التحقيق، النتائج المبدئية، الخطوات التالية..." required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="progress_percentage">نسبة الإنجاز</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="progress_percentage" name="progress_percentage"
                                   min="0" max="100" value="50">
                            <div class="input-group-append">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="next_steps">الخطوات التالية</label>
                        <textarea class="form-control" id="next_steps" name="next_steps" rows="3"
                                  placeholder="ما هي الخطوات المخطط لها في المرحلة القادمة من التحقيق؟"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="expected_completion_update">تحديث التاريخ المتوقع للانتهاء</label>
                        <input type="date" class="form-control" id="expected_completion_update" name="expected_completion_date"
                               min="{{ date('Y-m-d') }}" value="{{ $complaint->expected_completion_date ? $complaint->expected_completion_date->format('Y-m-d') : '' }}">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save"></i>
                        حفظ التحديث
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Close Modal -->
<div class="modal fade" id="closeModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-lock text-secondary"></i>
                    إغلاق الشكوى
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-complaints.close', $complaint->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        إغلاق الشكوى يعني انتهاء جميع الإجراءات المتعلقة بها نهائياً.
                    </div>

                    <div class="form-group">
                        <label for="closure_notes">ملاحظات الإغلاق</label>
                        <textarea class="form-control" id="closure_notes" name="closure_notes" rows="3"
                                  placeholder="أي ملاحظات نهائية حول إغلاق الشكوى..."></textarea>
                    </div>

                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="confirm_closure" name="confirm_closure" value="1" required>
                            <label class="custom-control-label" for="confirm_closure">
                                أؤكد أن جميع الإجراءات المطلوبة قد تم تنفيذها
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-secondary">
                        <i class="fas fa-lock"></i>
                        إغلاق الشكوى
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('styles')
<style>
/* Timeline Styles */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e3e6f0;
}

.timeline-content {
    background: #f8f9fc;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #5a5c69;
}

.timeline-title {
    margin-bottom: 5px;
    font-weight: 600;
    color: #5a5c69;
}

.timeline-text {
    margin: 0;
    color: #858796;
    font-size: 0.9rem;
}

/* Print Styles */
@media print {
    .btn, .modal, .card-header .dropdown, nav, .sidebar {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }

    .card-body {
        padding: 15px !important;
    }

    .badge {
        border: 1px solid #000 !important;
        color: #000 !important;
        background: transparent !important;
    }

    .timeline-marker {
        background: #000 !important;
    }

    .timeline::before {
        background: #000 !important;
    }

    .alert {
        border: 1px solid #000 !important;
        background: transparent !important;
        color: #000 !important;
    }

    h1, h2, h3, h4, h5, h6 {
        color: #000 !important;
    }

    .text-muted {
        color: #666 !important;
    }
}

/* Confidentiality Indicators */
.confidentiality-high {
    border-left: 4px solid #dc3545 !important;
}

.confidentiality-medium {
    border-left: 4px solid #ffc107 !important;
}

.confidentiality-low {
    border-left: 4px solid #17a2b8 !important;
}

/* Status Indicators */
.status-pending {
    background: linear-gradient(45deg, #ffc107, #ffca2c);
}

.status-investigating {
    background: linear-gradient(45deg, #17a2b8, #20c9e7);
}

.status-resolved {
    background: linear-gradient(45deg, #28a745, #34ce57);
}

.status-dismissed {
    background: linear-gradient(45deg, #dc3545, #e55a6b);
}

.status-closed {
    background: linear-gradient(45deg, #6c757d, #7d8a96);
}

/* Anonymous Complaint Styling */
.anonymous-complaint {
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255,193,7,0.1) 10px,
        rgba(255,193,7,0.1) 20px
    );
}

/* Priority Indicators */
.priority-urgent {
    animation: pulse-urgent 2s infinite;
}

@keyframes pulse-urgent {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

.priority-high {
    border-left: 4px solid #ffc107;
}

/* Investigation Progress */
.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% { background-position: 1rem 0; }
    100% { background-position: 0 0; }
}

/* Modal Enhancements */
.modal-header {
    border-bottom: 2px solid #e3e6f0;
}

.modal-footer {
    border-top: 2px solid #e3e6f0;
}

/* Form Enhancements */
.form-control:focus {
    border-color: #5a5c69;
    box-shadow: 0 0 0 0.2rem rgba(90, 92, 105, 0.25);
}

.select2-container--default .select2-selection--single:focus {
    border-color: #5a5c69;
    box-shadow: 0 0 0 0.2rem rgba(90, 92, 105, 0.25);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .timeline {
        padding-left: 20px;
    }

    .timeline-marker {
        left: -17px;
        width: 10px;
        height: 10px;
    }

    .timeline::before {
        left: 10px;
    }

    .card-body {
        padding: 15px;
    }
}
</style>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%',
        placeholder: 'اختر من القائمة...',
        allowClear: true
    });

    // Follow-up toggle
    $('#follow_up_required').on('change', function() {
        if ($(this).val() == '1') {
            $('#follow_up_details').slideDown();
            $('#follow_up_date').prop('required', true);
        } else {
            $('#follow_up_details').slideUp();
            $('#follow_up_date').prop('required', false);
        }
    });

    // Dismissal reason handling
    $('#dismissal_reason_select').on('change', function() {
        const reason = $(this).val();
        let placeholder = 'اشرح بالتفصيل سبب رفض هذه الشكوى...';

        switch(reason) {
            case 'insufficient_evidence':
                placeholder = 'اشرح ما هي الأدلة المطلوبة التي لم تتوفر...';
                break;
            case 'outside_jurisdiction':
                placeholder = 'وضح لماذا هذه الشكوى خارج نطاق اختصاص المؤسسة...';
                break;
            case 'frivolous':
                placeholder = 'اشرح لماذا تعتبر هذه الشكوى تافهة أو غير جدية...';
                break;
            case 'duplicate':
                placeholder = 'أشر إلى الشكوى الأصلية أو المماثلة...';
                break;
            case 'resolved_informally':
                placeholder = 'اشرح كيف تم حل الشكوى بشكل غير رسمي...';
                break;
        }

        $('#dismissal_reason').attr('placeholder', placeholder);
    });

    // Progress percentage slider
    $('#progress_percentage').on('input', function() {
        const value = $(this).val();
        let color = '#28a745'; // Green

        if (value < 25) color = '#dc3545'; // Red
        else if (value < 50) color = '#ffc107'; // Yellow
        else if (value < 75) color = '#17a2b8'; // Blue

        $(this).css('border-left', `4px solid ${color}`);
    });

    // Form validation
    $('form').on('submit', function(e) {
        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');

        // Show loading state
        submitBtn.prop('disabled', true);
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...');

        // Validate required fields
        let isValid = true;
        form.find('[required]').each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            submitBtn.prop('disabled', false);
            submitBtn.html(originalText);

            // Show error message
            if (!$('#validation-error').length) {
                form.prepend('<div id="validation-error" class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> يرجى ملء جميع الحقول المطلوبة</div>');
                setTimeout(function() {
                    $('#validation-error').fadeOut(function() {
                        $(this).remove();
                    });
                }, 5000);
            }

            return false;
        }

        // Reset button after 10 seconds (in case of slow response)
        setTimeout(function() {
            submitBtn.prop('disabled', false);
            submitBtn.html(originalText);
        }, 10000);
    });

    // Real-time validation
    $('[required]').on('input change', function() {
        if ($(this).val().trim()) {
            $(this).removeClass('is-invalid');
        }
    });

    // Auto-resize textareas
    $('textarea').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Confirmation dialogs for critical actions
    $('#dismissModal form, #closeModal form').on('submit', function(e) {
        const action = $(this).closest('.modal').find('.modal-title').text().trim();
        if (!confirm(`هل أنت متأكد من ${action}؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
            e.preventDefault();
            return false;
        }
    });

    // Print functionality
    window.print = function() {
        // Hide non-essential elements
        $('.btn, .modal, nav, .sidebar').hide();

        // Add print-specific styling
        $('body').addClass('printing');

        // Trigger print
        window.print();

        // Restore elements after print
        setTimeout(function() {
            $('.btn, .modal, nav, .sidebar').show();
            $('body').removeClass('printing');
        }, 1000);
    };

    // Auto-save investigation updates
    let autoSaveTimer;
    $('#investigation_update, #next_steps').on('input', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(function() {
            saveInvestigationDraft();
        }, 30000); // Auto-save after 30 seconds of inactivity
    });

    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        // Ctrl+P for print
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            window.print();
        }

        // Ctrl+E for edit (if available)
        if (e.ctrlKey && e.key === 'e' && $('#edit-btn').length) {
            e.preventDefault();
            window.location.href = $('#edit-btn').attr('href');
        }

        // Escape to close modals
        if (e.key === 'Escape') {
            $('.modal').modal('hide');
        }
    });

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Initialize popovers
    $('[data-toggle="popover"]').popover();

    // Animate timeline on scroll
    $(window).on('scroll', function() {
        $('.timeline-item').each(function() {
            const elementTop = $(this).offset().top;
            const elementBottom = elementTop + $(this).outerHeight();
            const viewportTop = $(window).scrollTop();
            const viewportBottom = viewportTop + $(window).height();

            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                $(this).addClass('animate__animated animate__fadeInLeft');
            }
        });
    });
});

function saveInvestigationDraft() {
    const updateText = $('#investigation_update').val();
    const nextSteps = $('#next_steps').val();

    if (updateText.trim() || nextSteps.trim()) {
        $.ajax({
            url: '{{ route("employee-complaints.save-investigation-draft", $complaint->id) }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                investigation_update: updateText,
                next_steps: nextSteps
            },
            success: function(response) {
                // Show brief success message
                if (!$('#draft-saved').length) {
                    $('body').append('<div id="draft-saved" class="alert alert-success position-fixed" style="top: 20px; right: 20px; z-index: 9999;"><i class="fas fa-check"></i> تم حفظ مسودة التحديث</div>');
                    setTimeout(function() {
                        $('#draft-saved').fadeOut(function() {
                            $(this).remove();
                        });
                    }, 3000);
                }
            },
            error: function() {
                // Silently fail for draft saves
            }
        });
    }
}

// Export complaint details
function exportComplaint(format) {
    const url = `{{ route('employee-complaints.export', $complaint->id) }}?format=${format}`;
    window.open(url, '_blank');
}

// Share complaint (for authorized users)
function shareComplaint() {
    if (navigator.share) {
        navigator.share({
            title: 'تفاصيل الشكوى #{{ $complaint->id }}',
            text: '{{ $complaint->subject }}',
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(function() {
            alert('تم نسخ رابط الشكوى إلى الحافظة');
        });
    }
}
</script>
@endsection
