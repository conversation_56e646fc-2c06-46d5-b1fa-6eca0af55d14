@extends('layouts.app')

@section('title', 'إدارة الشكاوى')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        إدارة الشكاوى
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item active">الشكاوى</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button type="button" class="btn btn-info btn-sm" data-toggle="modal" data-target="#exportModal">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                    <a href="{{ route('employee-complaints.create') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i>
                        شكوى جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الشكاوى
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-complaints">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                في الانتظار
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="pending-complaints">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                قيد التحقيق
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="investigating-complaints">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-search fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                محلولة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="resolved-complaints">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt"></i>
                        إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <button type="button" class="btn btn-warning btn-block" onclick="filterByStatus('pending')">
                                <i class="fas fa-clock"></i>
                                الشكاوى المعلقة
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-danger btn-block" onclick="filterByPriority('urgent')">
                                <i class="fas fa-exclamation"></i>
                                الشكاوى العاجلة
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-info btn-block" onclick="filterByCategory('harassment')">
                                <i class="fas fa-shield-alt"></i>
                                شكاوى التحرش
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-secondary btn-block" onclick="clearAllFilters()">
                                <i class="fas fa-times"></i>
                                مسح الفلاتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-filter"></i>
                        فلاتر متقدمة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="employee_filter">الموظف</label>
                                <select class="form-control select2" id="employee_filter" name="employee_id">
                                    <option value="">جميع الموظفين</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="status_filter">الحالة</label>
                                <select class="form-control" id="status_filter" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending">في الانتظار</option>
                                    <option value="under_investigation">قيد التحقيق</option>
                                    <option value="resolved">محلولة</option>
                                    <option value="dismissed">مرفوضة</option>
                                    <option value="closed">مغلقة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="category_filter">الفئة</label>
                                <select class="form-control" id="category_filter" name="category">
                                    <option value="">جميع الفئات</option>
                                    <option value="harassment">تحرش</option>
                                    <option value="discrimination">تمييز</option>
                                    <option value="workplace_safety">سلامة مكان العمل</option>
                                    <option value="management_issues">مشاكل إدارية</option>
                                    <option value="policy_violation">مخالفة السياسات</option>
                                    <option value="ethical_concerns">مخاوف أخلاقية</option>
                                    <option value="financial_irregularities">مخالفات مالية</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="priority_filter">الأولوية</label>
                                <select class="form-control" id="priority_filter" name="priority">
                                    <option value="">جميع الأولويات</option>
                                    <option value="low">منخفضة</option>
                                    <option value="medium">متوسطة</option>
                                    <option value="high">عالية</option>
                                    <option value="urgent">عاجلة</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="confidentiality_filter">مستوى السرية</label>
                                <select class="form-control" id="confidentiality_filter" name="confidentiality_level">
                                    <option value="">جميع المستويات</option>
                                    <option value="low">منخفض</option>
                                    <option value="medium">متوسط</option>
                                    <option value="high">عالي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="date_from">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="date_to">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="anonymous_filter">الشكاوى المجهولة</label>
                                <select class="form-control" id="anonymous_filter" name="is_anonymous">
                                    <option value="">الكل</option>
                                    <option value="1">مجهولة فقط</option>
                                    <option value="0">غير مجهولة فقط</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Complaints Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list"></i>
                        قائمة الشكاوى
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="complaints-table" width="100%" cellspacing="0">
                            <thead class="thead-light">
                                <tr>
                                    <th>رقم الشكوى</th>
                                    <th>مقدم الشكوى</th>
                                    <th>الموضوع</th>
                                    <th>الفئة</th>
                                    <th>الأولوية</th>
                                    <th>الحالة</th>
                                    <th>السرية</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via DataTables AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-download text-info"></i>
                    تصدير الشكاوى
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="export-form">
                    <div class="form-group">
                        <label for="export_format">تنسيق التصدير</label>
                        <select class="form-control" id="export_format" name="format" required>
                            <option value="excel">Excel (.xlsx)</option>
                            <option value="csv">CSV (.csv)</option>
                            <option value="pdf">PDF (.pdf)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="export_fields">الحقول المطلوبة</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="field_id" name="fields[]" value="id" checked>
                                    <label class="custom-control-label" for="field_id">رقم الشكوى</label>
                                </div>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="field_employee" name="fields[]" value="employee" checked>
                                    <label class="custom-control-label" for="field_employee">مقدم الشكوى</label>
                                </div>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="field_subject" name="fields[]" value="subject" checked>
                                    <label class="custom-control-label" for="field_subject">الموضوع</label>
                                </div>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="field_category" name="fields[]" value="category" checked>
                                    <label class="custom-control-label" for="field_category">الفئة</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="field_priority" name="fields[]" value="priority" checked>
                                    <label class="custom-control-label" for="field_priority">الأولوية</label>
                                </div>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="field_status" name="fields[]" value="status" checked>
                                    <label class="custom-control-label" for="field_status">الحالة</label>
                                </div>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="field_created_at" name="fields[]" value="created_at" checked>
                                    <label class="custom-control-label" for="field_created_at">تاريخ الإنشاء</label>
                                </div>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="field_description" name="fields[]" value="description">
                                    <label class="custom-control-label" for="field_description">الوصف</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="apply_filters" name="apply_filters" value="1" checked>
                            <label class="custom-control-label" for="apply_filters">
                                تطبيق الفلاتر الحالية على التصدير
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" onclick="exportComplaints()">
                    <i class="fas fa-download"></i>
                    تصدير
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
/* Statistics Cards Animation */
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Priority Indicators */
.priority-urgent {
    background: linear-gradient(45deg, #dc3545, #e55a6b);
    color: white;
    animation: pulse-urgent 2s infinite;
}

.priority-high {
    background: linear-gradient(45deg, #ffc107, #ffca2c);
    color: #212529;
}

.priority-medium {
    background: linear-gradient(45deg, #17a2b8, #20c9e7);
    color: white;
}

.priority-low {
    background: linear-gradient(45deg, #6c757d, #7d8a96);
    color: white;
}

@keyframes pulse-urgent {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

/* Status Indicators */
.status-pending {
    background: linear-gradient(45deg, #ffc107, #ffca2c);
    color: #212529;
}

.status-under_investigation {
    background: linear-gradient(45deg, #17a2b8, #20c9e7);
    color: white;
}

.status-resolved {
    background: linear-gradient(45deg, #28a745, #34ce57);
    color: white;
}

.status-dismissed {
    background: linear-gradient(45deg, #dc3545, #e55a6b);
    color: white;
}

.status-closed {
    background: linear-gradient(45deg, #6c757d, #7d8a96);
    color: white;
}

/* Confidentiality Indicators */
.confidentiality-high {
    border-left: 4px solid #dc3545 !important;
    background: rgba(220, 53, 69, 0.1);
}

.confidentiality-medium {
    border-left: 4px solid #ffc107 !important;
    background: rgba(255, 193, 7, 0.1);
}

.confidentiality-low {
    border-left: 4px solid #17a2b8 !important;
    background: rgba(23, 162, 184, 0.1);
}

/* Anonymous Complaint Styling */
.anonymous-complaint {
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255,193,7,0.1) 10px,
        rgba(255,193,7,0.1) 20px
    );
}

.anonymous-complaint::before {
    content: '🔒';
    position: absolute;
    top: 5px;
    right: 5px;
    font-size: 12px;
}

/* DataTables Customization */
.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.375rem 0.75rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    margin: 0 2px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #5a5c69;
    border-color: #5a5c69;
    color: white !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: #5a5c69;
    border-color: #5a5c69;
    color: white !important;
}

/* Table Row Hover Effects */
#complaints-table tbody tr {
    transition: background-color 0.2s ease-in-out;
}

#complaints-table tbody tr:hover {
    background-color: rgba(90, 92, 105, 0.1) !important;
}

/* Badge Styling */
.badge {
    font-size: 0.75em;
    padding: 0.375rem 0.5rem;
    border-radius: 0.25rem;
}

/* Quick Action Buttons */
.btn-block {
    transition: all 0.2s ease-in-out;
}

.btn-block:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* Filter Section */
.card-header {
    background: linear-gradient(45deg, #f8f9fc, #ffffff);
    border-bottom: 1px solid #e3e6f0;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Statistics Counter Animation */
.counter {
    font-size: 2rem;
    font-weight: bold;
    color: #5a5c69;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }

    .btn-block {
        margin-bottom: 0.5rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Print Styles */
@media print {
    .btn, .modal, .card-header .dropdown, nav, .sidebar, .dataTables_wrapper .dataTables_length, .dataTables_wrapper .dataTables_filter, .dataTables_wrapper .dataTables_paginate {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        break-inside: avoid;
    }

    .table {
        font-size: 10px !important;
    }

    .badge {
        border: 1px solid #000 !important;
        color: #000 !important;
        background: transparent !important;
    }
}

/* Export Modal Styling */
.modal-content {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 2px solid #e3e6f0;
    background: linear-gradient(45deg, #f8f9fc, #ffffff);
}

.modal-footer {
    border-top: 2px solid #e3e6f0;
    background: linear-gradient(45deg, #ffffff, #f8f9fc);
}

/* Custom Checkbox Styling */
.custom-control-input:checked ~ .custom-control-label::before {
    background-color: #5a5c69;
    border-color: #5a5c69;
}

.custom-control-input:focus ~ .custom-control-label::before {
    box-shadow: 0 0 0 0.2rem rgba(90, 92, 105, 0.25);
}

/* Select2 Customization */
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(1.5em + 0.75rem + 2px);
    border: 1px solid #d1d3e2;
}

.select2-container--bootstrap4 .select2-selection--single:focus {
    border-color: #5a5c69;
    box-shadow: 0 0 0 0.2rem rgba(90, 92, 105, 0.25);
}

/* Action Buttons */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.2rem;
}

.btn-group-sm > .btn, .btn-sm {
    line-height: 1.5;
}

/* Table Action Buttons */
.table .btn {
    margin: 0 2px;
}

/* Tooltip Styling */
.tooltip-inner {
    background-color: #5a5c69;
    color: #fff;
    border-radius: 0.25rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.tooltip.bs-tooltip-top .arrow::before {
    border-top-color: #5a5c69;
}

.tooltip.bs-tooltip-bottom .arrow::before {
    border-bottom-color: #5a5c69;
}

.tooltip.bs-tooltip-left .arrow::before {
    border-left-color: #5a5c69;
}

.tooltip.bs-tooltip-right .arrow::before {
    border-right-color: #5a5c69;
}
</style>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize DataTable
    const complaintsTable = $('#complaints-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("employee-complaints.data") }}',
            data: function(d) {
                d.employee_id = $('#employee_filter').val();
                d.status = $('#status_filter').val();
                d.category = $('#category_filter').val();
                d.priority = $('#priority_filter').val();
                d.confidentiality_level = $('#confidentiality_filter').val();
                d.date_from = $('#date_from').val();
                d.date_to = $('#date_to').val();
                d.is_anonymous = $('#anonymous_filter').val();
            }
        },
        columns: [
            { data: 'id', name: 'id' },
            {
                data: 'employee',
                name: 'employee.name',
                render: function(data, type, row) {
                    if (row.is_anonymous) {
                        return '<span class="badge badge-warning"><i class="fas fa-user-secret"></i> مجهول</span>';
                    }
                    return data ? data.name : 'غير محدد';
                }
            },
            {
                data: 'subject',
                name: 'subject',
                render: function(data, type, row) {
                    return data.length > 50 ? data.substring(0, 50) + '...' : data;
                }
            },
            {
                data: 'category',
                name: 'category',
                render: function(data, type, row) {
                    const categories = {
                        'harassment': '<span class="badge badge-danger">تحرش</span>',
                        'discrimination': '<span class="badge badge-warning">تمييز</span>',
                        'workplace_safety': '<span class="badge badge-danger">سلامة مكان العمل</span>',
                        'management_issues': '<span class="badge badge-info">مشاكل إدارية</span>',
                        'policy_violation': '<span class="badge badge-warning">مخالفة السياسات</span>',
                        'ethical_concerns': '<span class="badge badge-primary">مخاوف أخلاقية</span>',
                        'financial_irregularities': '<span class="badge badge-danger">مخالفات مالية</span>',
                        'other': '<span class="badge badge-secondary">أخرى</span>'
                    };
                    return categories[data] || '<span class="badge badge-secondary">غير محدد</span>';
                }
            },
            {
                data: 'priority',
                name: 'priority',
                render: function(data, type, row) {
                    const priorities = {
                        'urgent': '<span class="badge priority-urgent">عاجلة</span>',
                        'high': '<span class="badge priority-high">عالية</span>',
                        'medium': '<span class="badge priority-medium">متوسطة</span>',
                        'low': '<span class="badge priority-low">منخفضة</span>'
                    };
                    return priorities[data] || '<span class="badge badge-secondary">غير محدد</span>';
                }
            },
            {
                data: 'status',
                name: 'status',
                render: function(data, type, row) {
                    const statuses = {
                        'pending': '<span class="badge status-pending">في الانتظار</span>',
                        'under_investigation': '<span class="badge status-under_investigation">قيد التحقيق</span>',
                        'resolved': '<span class="badge status-resolved">محلولة</span>',
                        'dismissed': '<span class="badge status-dismissed">مرفوضة</span>',
                        'closed': '<span class="badge status-closed">مغلقة</span>'
                    };
                    return statuses[data] || '<span class="badge badge-secondary">غير محدد</span>';
                }
            },
            {
                data: 'confidentiality_level',
                name: 'confidentiality_level',
                render: function(data, type, row) {
                    const levels = {
                        'high': '<span class="badge badge-danger">عالي</span>',
                        'medium': '<span class="badge badge-warning">متوسط</span>',
                        'low': '<span class="badge badge-info">منخفض</span>'
                    };
                    return levels[data] || '<span class="badge badge-secondary">غير محدد</span>';
                }
            },
            {
                data: 'created_at',
                name: 'created_at',
                render: function(data, type, row) {
                    return new Date(data).toLocaleDateString('ar-SA');
                }
            },
            {
                data: 'actions',
                name: 'actions',
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    let actions = `
                        <a href="/employee-affairs/complaints/${row.id}" class="btn btn-info btn-sm" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </a>
                    `;

                    if (row.status === 'pending') {
                        actions += `
                            <a href="/employee-affairs/complaints/${row.id}/edit" class="btn btn-primary btn-sm" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                        `;
                    }

                    if (row.status === 'pending') {
                        actions += `
                            <button type="button" class="btn btn-warning btn-sm" onclick="quickInvestigate(${row.id})" title="بدء التحقيق">
                                <i class="fas fa-search"></i>
                            </button>
                        `;
                    }

                    if (['pending', 'under_investigation'].includes(row.status)) {
                        actions += `
                            <button type="button" class="btn btn-success btn-sm" onclick="quickResolve(${row.id})" title="حل سريع">
                                <i class="fas fa-check"></i>
                            </button>
                        `;
                    }

                    return actions;
                }
            }
        ],
        order: [[0, 'desc']],
        pageLength: 25,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
        },
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: '<i class="fas fa-file-excel"></i> Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'pdf',
                text: '<i class="fas fa-file-pdf"></i> PDF',
                className: 'btn btn-danger btn-sm'
            },
            {
                extend: 'print',
                text: '<i class="fas fa-print"></i> طباعة',
                className: 'btn btn-info btn-sm'
            }
        ],
        drawCallback: function(settings) {
            // Apply confidentiality styling
            $('#complaints-table tbody tr').each(function() {
                const rowData = complaintsTable.row(this).data();
                if (rowData) {
                    $(this).removeClass('confidentiality-high confidentiality-medium confidentiality-low anonymous-complaint');

                    if (rowData.is_anonymous) {
                        $(this).addClass('anonymous-complaint');
                    }

                    if (rowData.confidentiality_level) {
                        $(this).addClass('confidentiality-' + rowData.confidentiality_level);
                    }
                }
            });

            // Initialize tooltips
            $('[title]').tooltip();
        }
    });

    // Initialize Select2 for employee filter
    $('#employee_filter').select2({
        theme: 'bootstrap4',
        placeholder: 'اختر موظف...',
        allowClear: true,
        ajax: {
            url: '{{ route("employees.search") }}',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    q: params.term,
                    page: params.page
                };
            },
            processResults: function(data, params) {
                params.page = params.page || 1;
                return {
                    results: data.data,
                    pagination: {
                        more: (params.page * 30) < data.total
                    }
                };
            },
            cache: true
        }
    });

    // Filter change handlers
    $('#employee_filter, #status_filter, #category_filter, #priority_filter, #confidentiality_filter, #date_from, #date_to, #anonymous_filter').on('change', function() {
        complaintsTable.ajax.reload();
    });

    // Load statistics
    loadStatistics();

    // Auto-refresh statistics every 30 seconds
    setInterval(loadStatistics, 30000);
});

// Load statistics
function loadStatistics() {
    $.ajax({
        url: '{{ route("employee-complaints.statistics") }}',
        method: 'GET',
        success: function(data) {
            animateCounter('#total-complaints', data.total);
            animateCounter('#pending-complaints', data.pending);
            animateCounter('#investigating-complaints', data.investigating);
            animateCounter('#resolved-complaints', data.resolved);
        },
        error: function() {
            $('#total-complaints, #pending-complaints, #investigating-complaints, #resolved-complaints').html('<i class="fas fa-exclamation-triangle text-danger"></i>');
        }
    });
}

// Animate counter
function animateCounter(selector, endValue) {
    const element = $(selector);
    const startValue = parseInt(element.text()) || 0;
    const duration = 1000;
    const stepTime = 50;
    const steps = duration / stepTime;
    const increment = (endValue - startValue) / steps;

    let currentValue = startValue;
    const timer = setInterval(function() {
        currentValue += increment;
        if ((increment > 0 && currentValue >= endValue) || (increment < 0 && currentValue <= endValue)) {
            currentValue = endValue;
            clearInterval(timer);
        }
        element.text(Math.floor(currentValue));
    }, stepTime);
}

// Quick filter functions
function filterByStatus(status) {
    $('#status_filter').val(status).trigger('change');
}

function filterByPriority(priority) {
    $('#priority_filter').val(priority).trigger('change');
}

function filterByCategory(category) {
    $('#category_filter').val(category).trigger('change');
}

function clearAllFilters() {
    $('#employee_filter').val(null).trigger('change');
    $('#status_filter, #category_filter, #priority_filter, #confidentiality_filter, #anonymous_filter').val('');
    $('#date_from, #date_to').val('');
    $('#complaints-table').DataTable().ajax.reload();
}

// Quick actions
function quickInvestigate(complaintId) {
    if (confirm('هل تريد بدء التحقيق في هذه الشكوى؟')) {
        $.ajax({
            url: `/employee-affairs/complaints/${complaintId}/quick-investigate`,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    $('#complaints-table').DataTable().ajax.reload();
                    showAlert('success', 'تم بدء التحقيق بنجاح');
                } else {
                    showAlert('error', response.message || 'حدث خطأ أثناء بدء التحقيق');
                }
            },
            error: function() {
                showAlert('error', 'حدث خطأ أثناء بدء التحقيق');
            }
        });
    }
}

function quickResolve(complaintId) {
    const resolution = prompt('أدخل تفاصيل الحل:');
    if (resolution && resolution.trim()) {
        $.ajax({
            url: `/employee-affairs/complaints/${complaintId}/quick-resolve`,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                resolution_details: resolution
            },
            success: function(response) {
                if (response.success) {
                    $('#complaints-table').DataTable().ajax.reload();
                    showAlert('success', 'تم حل الشكوى بنجاح');
                } else {
                    showAlert('error', response.message || 'حدث خطأ أثناء حل الشكوى');
                }
            },
            error: function() {
                showAlert('error', 'حدث خطأ أثناء حل الشكوى');
            }
        });
    }
}

// Export function
function exportComplaints() {
    const form = $('#export-form');
    const formData = new FormData(form[0]);

    // Add current filters if requested
    if ($('#apply_filters').is(':checked')) {
        formData.append('employee_id', $('#employee_filter').val());
        formData.append('status', $('#status_filter').val());
        formData.append('category', $('#category_filter').val());
        formData.append('priority', $('#priority_filter').val());
        formData.append('confidentiality_level', $('#confidentiality_filter').val());
        formData.append('date_from', $('#date_from').val());
        formData.append('date_to', $('#date_to').val());
        formData.append('is_anonymous', $('#anonymous_filter').val());
    }

    // Create download link
    const params = new URLSearchParams(formData);
    const url = `{{ route('employee-complaints.export') }}?${params.toString()}`;

    // Open in new window for download
    window.open(url, '_blank');

    // Close modal
    $('#exportModal').modal('hide');

    showAlert('info', 'جاري تحضير ملف التصدير...');
}

// Show alert function
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : 'alert-info';
    const alertIcon = type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-triangle' : 'fa-info-circle';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="fas ${alertIcon}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;

    $('body').append(alertHtml);

    // Auto-remove after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut(function() {
            $(this).remove();
        });
    }, 5000);
}

// Keyboard shortcuts
$(document).on('keydown', function(e) {
    // Ctrl+N for new complaint
    if (e.ctrlKey && e.key === 'n') {
        e.preventDefault();
        window.location.href = '{{ route("employee-complaints.create") }}';
    }

    // Ctrl+R for refresh
    if (e.ctrlKey && e.key === 'r') {
        e.preventDefault();
        $('#complaints-table').DataTable().ajax.reload();
        loadStatistics();
    }

    // Ctrl+E for export
    if (e.ctrlKey && e.key === 'e') {
        e.preventDefault();
        $('#exportModal').modal('show');
    }
});
</script>
@endsection
