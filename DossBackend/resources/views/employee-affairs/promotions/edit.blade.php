@extends('layouts.app')

@section('title', 'تعديل طلب الترقية')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-edit text-primary"></i>
                        تعديل طلب الترقية #{{ $promotion->id }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-promotions.index') }}">الترقيات</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-promotions.show', $promotion->id) }}">تفاصيل الترقية</a></li>
                            <li class="breadcrumb-item active">تعديل</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('employee-promotions.show', $promotion->id) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i>
                        العودة للتفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Check -->
    @if($promotion->status !== 'pending')
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                @if($promotion->status === 'approved')
                    هذا الطلب تم اعتماده ولا يمكن تعديله. يمكنك فقط عرض التفاصيل.
                @elseif($promotion->status === 'rejected')
                    هذا الطلب تم رفضه. يمكنك إنشاء طلب جديد إذا لزم الأمر.
                @elseif($promotion->status === 'completed')
                    هذه الترقية تم تنفيذها بالكامل ولا يمكن تعديلها.
                @endif
                <div class="mt-2">
                    <a href="{{ route('employee-promotions.show', $promotion->id) }}" class="btn btn-sm btn-outline-primary">
                        عرض التفاصيل
                    </a>
                    <a href="{{ route('employee-promotions.create') }}" class="btn btn-sm btn-outline-success">
                        طلب ترقية جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
    @endif

    @if($promotion->status === 'pending')
    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit"></i>
                        تعديل بيانات الترقية
                    </h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('employee-promotions.update', $promotion->id) }}" method="POST" id="promotion-form">
                        @csrf
                        @method('PUT')

                        <!-- Employee Info (Read-only) -->
                        <div class="form-group">
                            <label>الموظف</label>
                            <div class="form-control-plaintext border rounded p-2 bg-light">
                                <div class="d-flex align-items-center">
                                    <img src="{{ $promotion->employee->avatar ?? '/images/default-avatar.png' }}" 
                                         class="rounded-circle mr-2" width="30" height="30" alt="صورة الموظف">
                                    <div>
                                        <strong>{{ $promotion->employee->name }}</strong>
                                        <br><small class="text-muted">{{ $promotion->employee->employee_code }}</small>
                                    </div>
                                </div>
                            </div>
                            <small class="text-muted">لا يمكن تغيير الموظف بعد إنشاء الطلب</small>
                        </div>

                        <!-- Promotion Type -->
                        <div class="form-group">
                            <label for="type">نوع الترقية <span class="text-danger">*</span></label>
                            <select class="form-control @error('type') is-invalid @enderror" id="type" name="type" required>
                                <option value="">اختر نوع الترقية</option>
                                <option value="position" {{ old('type', $promotion->type) == 'position' ? 'selected' : '' }}>ترقية منصب</option>
                                <option value="grade" {{ old('type', $promotion->type) == 'grade' ? 'selected' : '' }}>ترقية درجة</option>
                                <option value="salary" {{ old('type', $promotion->type) == 'salary' ? 'selected' : '' }}>ترقية راتب</option>
                                <option value="comprehensive" {{ old('type', $promotion->type) == 'comprehensive' ? 'selected' : '' }}>ترقية شاملة</option>
                            </select>
                            @error('type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Promotion Details (Dynamic based on type) -->
                        <div id="promotion-details">
                            <!-- Position Promotion -->
                            <div id="position-section" class="promotion-section" style="display: none;">
                                <div class="form-group">
                                    <label for="current_position_id">المنصب الحالي</label>
                                    <select class="form-control" id="current_position_id" name="current_position_id">
                                        <option value="">اختر المنصب الحالي</option>
                                        {{-- Position options will be loaded via AJAX --}}
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="new_position_id">المنصب الجديد <span class="text-danger">*</span></label>
                                    <select class="form-control" id="new_position_id" name="new_position_id">
                                        <option value="">اختر المنصب الجديد</option>
                                        {{-- Position options will be loaded via AJAX --}}
                                    </select>
                                </div>
                            </div>

                            <!-- Grade Promotion -->
                            <div id="grade-section" class="promotion-section" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="current_grade">الدرجة الحالية</label>
                                            <input type="number" class="form-control" id="current_grade" name="current_grade" 
                                                   value="{{ old('current_grade', $promotion->current_grade) }}" min="1" max="15">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="new_grade">الدرجة الجديدة <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="new_grade" name="new_grade" 
                                                   value="{{ old('new_grade', $promotion->new_grade) }}" min="1" max="15">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Salary Promotion -->
                            <div id="salary-section" class="promotion-section" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="current_salary">الراتب الحالي</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="current_salary" name="current_salary" 
                                                       value="{{ old('current_salary', $promotion->current_salary) }}" step="0.01" min="0">
                                                <div class="input-group-append">
                                                    <span class="input-group-text">ج.م</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="new_salary">الراتب الجديد <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="new_salary" name="new_salary" 
                                                       value="{{ old('new_salary', $promotion->new_salary) }}" step="0.01" min="0" required>
                                                <div class="input-group-append">
                                                    <span class="input-group-text">ج.م</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="salary_increase">مقدار الزيادة</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="salary_increase" name="salary_increase" 
                                                       value="{{ old('salary_increase', $promotion->salary_increase) }}" step="0.01" readonly>
                                                <div class="input-group-append">
                                                    <span class="input-group-text">ج.م</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="increase_percentage">نسبة الزيادة</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="increase_percentage" name="increase_percentage" 
                                                       value="{{ old('increase_percentage', $promotion->increase_percentage) }}" step="0.01" readonly>
                                                <div class="input-group-append">
                                                    <span class="input-group-text">%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Comprehensive Promotion -->
                            <div id="comprehensive-section" class="promotion-section" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="comp_current_position_id">المنصب الحالي</label>
                                            <select class="form-control" id="comp_current_position_id" name="comp_current_position_id">
                                                <option value="">اختر المنصب الحالي</option>
                                                {{-- Position options will be loaded via AJAX --}}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="comp_new_position_id">المنصب الجديد <span class="text-danger">*</span></label>
                                            <select class="form-control" id="comp_new_position_id" name="comp_new_position_id">
                                                <option value="">اختر المنصب الجديد</option>
                                                {{-- Position options will be loaded via AJAX --}}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="comp_current_grade">الدرجة الحالية</label>
                                            <input type="number" class="form-control" id="comp_current_grade" name="comp_current_grade" 
                                                   value="{{ old('comp_current_grade', $promotion->current_grade) }}" min="1" max="15">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="comp_new_grade">الدرجة الجديدة <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="comp_new_grade" name="comp_new_grade" 
                                                   value="{{ old('comp_new_grade', $promotion->new_grade) }}" min="1" max="15">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="comp_current_salary">الراتب الحالي</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="comp_current_salary" name="comp_current_salary" 
                                                       value="{{ old('comp_current_salary', $promotion->current_salary) }}" step="0.01" min="0">
                                                <div class="input-group-append">
                                                    <span class="input-group-text">ج.م</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="comp_new_salary">الراتب الجديد <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="comp_new_salary" name="comp_new_salary" 
                                                       value="{{ old('comp_new_salary', $promotion->new_salary) }}" step="0.01" min="0" required>
                                                <div class="input-group-append">
                                                    <span class="input-group-text">ج.م</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Effective Date -->
                        <div class="form-group">
                            <label for="effective_date">تاريخ سريان الترقية <span class="text-danger">*</span></label>
                            <input type="date" class="form-control @error('effective_date') is-invalid @enderror" 
                                   id="effective_date" name="effective_date" 
                                   value="{{ old('effective_date', $promotion->effective_date ? $promotion->effective_date->format('Y-m-d') : '') }}" required>
                            @error('effective_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Justification -->
                        <div class="form-group">
                            <label for="justification">مبررات الترقية <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('justification') is-invalid @enderror" 
                                      id="justification" name="justification" rows="4" required 
                                      placeholder="اذكر المبررات التفصيلية للترقية">{{ old('justification', $promotion->justification) }}</textarea>
                            @error('justification')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Performance Rating -->
                        <div class="form-group">
                            <label for="performance_rating">تقييم الأداء</label>
                            <select class="form-control @error('performance_rating') is-invalid @enderror" id="performance_rating" name="performance_rating">
                                <option value="">اختر تقييم الأداء</option>
                                <option value="excellent" {{ old('performance_rating', $promotion->performance_rating) == 'excellent' ? 'selected' : '' }}>ممتاز</option>
                                <option value="very_good" {{ old('performance_rating', $promotion->performance_rating) == 'very_good' ? 'selected' : '' }}>جيد جداً</option>
                                <option value="good" {{ old('performance_rating', $promotion->performance_rating) == 'good' ? 'selected' : '' }}>جيد</option>
                                <option value="satisfactory" {{ old('performance_rating', $promotion->performance_rating) == 'satisfactory' ? 'selected' : '' }}>مرضي</option>
                            </select>
                            @error('performance_rating')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Notes -->
                        <div class="form-group">
                            <label for="notes">ملاحظات إضافية</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                      id="notes" name="notes" rows="3" 
                                      placeholder="أي ملاحظات أو تفاصيل إضافية">{{ old('notes', $promotion->notes) }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Form Actions -->
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ التعديلات
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                            <a href="{{ route('employee-promotions.show', $promotion->id) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Current Promotion Info -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i>
                        معلومات الترقية الحالية
                    </h6>
                </div>
                <div class="card-body">
                    <p><strong>رقم الطلب:</strong> #{{ $promotion->id }}</p>
                    <p><strong>نوع الترقية:</strong> 
                        @switch($promotion->type)
                            @case('position')
                                ترقية منصب
                                @break
                            @case('grade')
                                ترقية درجة
                                @break
                            @case('salary')
                                ترقية راتب
                                @break
                            @case('comprehensive')
                                ترقية شاملة
                                @break
                        @endswitch
                    </p>
                    <p><strong>تاريخ الطلب:</strong> {{ $promotion->created_at->format('Y-m-d H:i') }}</p>
                    <p><strong>الحالة:</strong> 
                        <span class="badge badge-warning">في الانتظار</span>
                    </p>
                    @if($promotion->effective_date)
                        <p><strong>تاريخ السريان:</strong> {{ $promotion->effective_date->format('Y-m-d') }}</p>
                    @endif
                </div>
            </div>

            <!-- Employee Info -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user"></i>
                        معلومات الموظف
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <img src="{{ $promotion->employee->avatar ?? '/images/default-avatar.png' }}" 
                             class="rounded-circle" width="80" height="80" alt="صورة الموظف">
                    </div>
                    <p><strong>الاسم:</strong> {{ $promotion->employee->name }}</p>
                    <p><strong>الكود:</strong> {{ $promotion->employee->employee_code }}</p>
                    <p><strong>القسم:</strong> {{ $promotion->employee->department->name ?? 'غير محدد' }}</p>
                    <p><strong>المنصب الحالي:</strong> {{ $promotion->employee->position->name ?? 'غير محدد' }}</p>
                    <p><strong>الراتب الحالي:</strong> {{ number_format($promotion->employee->salary ?? 0, 2) }} ج.م</p>
                    
                    <div class="mt-3">
                        <a href="{{ route('employees.show', $promotion->employee->id) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i>
                            عرض ملف الموظف
                        </a>
                    </div>
                </div>
            </div>

            <!-- Salary Calculator -->
            <div class="card shadow mt-3" id="salary-calculator" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-calculator"></i>
                        حاسبة الراتب
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <h4 class="text-success" id="calculated-increase">0.00 ج.م</h4>
                        <small class="text-muted">مقدار الزيادة</small>
                    </div>
                    <hr>
                    <div class="text-center">
                        <h5 class="text-info" id="calculated-percentage">0.00%</h5>
                        <small class="text-muted">نسبة الزيادة</small>
                    </div>
                    <hr>
                    <div class="text-center">
                        <h5 class="text-primary" id="annual-increase">0.00 ج.م</h5>
                        <small class="text-muted">الزيادة السنوية</small>
                    </div>
                </div>
            </div>

            <!-- Edit Guidelines -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-lightbulb"></i>
                        إرشادات التعديل
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            يمكن تعديل جميع بيانات الترقية
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-times text-danger"></i>
                            لا يمكن تغيير الموظف
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-info text-info"></i>
                            تأكد من صحة حسابات الراتب
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-exclamation text-warning"></i>
                            سيتم إعادة تقييم الطلب بعد التعديل
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize form
    initializeForm();

    // Load positions
    loadPositions();

    // Set current promotion type
    const currentType = $('#type').val();
    if (currentType) {
        showPromotionSection(currentType);
    }

    // Type change handler
    $('#type').change(function() {
        const type = $(this).val();
        showPromotionSection(type);
    });

    // Salary calculation handlers
    $('#current_salary, #new_salary, #comp_current_salary, #comp_new_salary').on('input', function() {
        calculateSalaryIncrease();
    });

    // Form validation
    $('#promotion-form').on('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
});

function initializeForm() {
    // Set minimum date for effective date (today)
    const today = new Date().toISOString().split('T')[0];
    $('#effective_date').attr('min', today);
}

function loadPositions() {
    $.get('/api/positions')
        .done(function(response) {
            if (response.success) {
                const positions = response.data;
                const selects = [
                    '#current_position_id',
                    '#new_position_id',
                    '#comp_current_position_id',
                    '#comp_new_position_id'
                ];

                selects.forEach(function(selector) {
                    const select = $(selector);
                    positions.forEach(function(position) {
                        select.append(`<option value="${position.id}">${position.name}</option>`);
                    });
                });

                // Set current values if editing
                setCurrentPositionValues();
            }
        })
        .fail(function() {
            console.error('Failed to load positions');
        });
}

function setCurrentPositionValues() {
    // Set current position values based on promotion data
    const promotion = @json($promotion);

    if (promotion.current_position_id) {
        $('#current_position_id, #comp_current_position_id').val(promotion.current_position_id);
    }
    if (promotion.new_position_id) {
        $('#new_position_id, #comp_new_position_id').val(promotion.new_position_id);
    }
}

function showPromotionSection(type) {
    // Hide all sections
    $('.promotion-section').hide();
    $('#salary-calculator').hide();

    // Show relevant section
    switch(type) {
        case 'position':
            $('#position-section').show();
            break;
        case 'grade':
            $('#grade-section').show();
            break;
        case 'salary':
            $('#salary-section').show();
            $('#salary-calculator').show();
            calculateSalaryIncrease();
            break;
        case 'comprehensive':
            $('#comprehensive-section').show();
            $('#salary-calculator').show();
            calculateSalaryIncrease();
            break;
    }
}

function calculateSalaryIncrease() {
    let currentSalary = 0;
    let newSalary = 0;

    const type = $('#type').val();

    if (type === 'salary') {
        currentSalary = parseFloat($('#current_salary').val()) || 0;
        newSalary = parseFloat($('#new_salary').val()) || 0;
    } else if (type === 'comprehensive') {
        currentSalary = parseFloat($('#comp_current_salary').val()) || 0;
        newSalary = parseFloat($('#comp_new_salary').val()) || 0;
    }

    if (currentSalary > 0 && newSalary > 0) {
        const increase = newSalary - currentSalary;
        const percentage = (increase / currentSalary) * 100;
        const annualIncrease = increase * 12;

        // Update salary section fields
        $('#salary_increase').val(increase.toFixed(2));
        $('#increase_percentage').val(percentage.toFixed(2));

        // Update calculator display
        $('#calculated-increase').text(increase.toLocaleString('ar-EG', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + ' ج.م');

        $('#calculated-percentage').text(percentage.toFixed(2) + '%');

        $('#annual-increase').text(annualIncrease.toLocaleString('ar-EG', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + ' ج.م');

        // Color coding based on increase percentage
        if (percentage > 20) {
            $('#calculated-percentage').removeClass('text-info text-warning').addClass('text-success');
        } else if (percentage > 10) {
            $('#calculated-percentage').removeClass('text-info text-success').addClass('text-warning');
        } else {
            $('#calculated-percentage').removeClass('text-success text-warning').addClass('text-info');
        }
    } else {
        // Reset calculator
        $('#salary_increase, #increase_percentage').val('');
        $('#calculated-increase, #calculated-percentage, #annual-increase').text('0.00');
    }
}

function validateForm() {
    let isValid = true;
    const type = $('#type').val();

    // Clear previous errors
    $('.is-invalid').removeClass('is-invalid');
    $('.invalid-feedback').remove();

    // Validate based on promotion type
    switch(type) {
        case 'position':
            if (!$('#new_position_id').val()) {
                showFieldError('#new_position_id', 'يجب اختيار المنصب الجديد');
                isValid = false;
            }
            break;

        case 'grade':
            if (!$('#new_grade').val() || $('#new_grade').val() <= 0) {
                showFieldError('#new_grade', 'يجب إدخال الدرجة الجديدة');
                isValid = false;
            }
            break;

        case 'salary':
            if (!$('#new_salary').val() || $('#new_salary').val() <= 0) {
                showFieldError('#new_salary', 'يجب إدخال الراتب الجديد');
                isValid = false;
            }

            const currentSal = parseFloat($('#current_salary').val()) || 0;
            const newSal = parseFloat($('#new_salary').val()) || 0;

            if (newSal <= currentSal) {
                showFieldError('#new_salary', 'الراتب الجديد يجب أن يكون أكبر من الراتب الحالي');
                isValid = false;
            }
            break;

        case 'comprehensive':
            if (!$('#comp_new_position_id').val()) {
                showFieldError('#comp_new_position_id', 'يجب اختيار المنصب الجديد');
                isValid = false;
            }
            if (!$('#comp_new_grade').val() || $('#comp_new_grade').val() <= 0) {
                showFieldError('#comp_new_grade', 'يجب إدخال الدرجة الجديدة');
                isValid = false;
            }
            if (!$('#comp_new_salary').val() || $('#comp_new_salary').val() <= 0) {
                showFieldError('#comp_new_salary', 'يجب إدخال الراتب الجديد');
                isValid = false;
            }
            break;
    }

    // Validate effective date
    const effectiveDate = new Date($('#effective_date').val());
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (effectiveDate < today) {
        showFieldError('#effective_date', 'تاريخ السريان لا يمكن أن يكون في الماضي');
        isValid = false;
    }

    return isValid;
}

function showFieldError(fieldSelector, message) {
    const field = $(fieldSelector);
    field.addClass('is-invalid');
    field.after(`<div class="invalid-feedback">${message}</div>`);
}

function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع التغييرات غير المحفوظة.')) {
        $('#promotion-form')[0].reset();
        $('.promotion-section').hide();
        $('#salary-calculator').hide();
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();
    }
}
</script>
@endsection
