@extends('layouts.app')

@section('title', 'إدارة الترقيات')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-trophy text-primary"></i>
                        إدارة الترقيات
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item active">الترقيات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button type="button" class="btn btn-info btn-sm" onclick="loadStatistics()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث الإحصائيات
                    </button>
                    <a href="{{ route('employee-promotions.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        طلب ترقية جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الترقيات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-promotions">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-trophy fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                في الانتظار
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="pending-promotions">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                معتمدة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="approved-promotions">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                مكتملة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="completed-promotions">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-double fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt"></i>
                        إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-warning h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <i class="fas fa-clock fa-2x text-warning"></i>
                                        </div>
                                        <div>
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                طلبات معلقة
                                            </div>
                                            <div class="h6 mb-0 font-weight-bold text-gray-800" id="pending-count">0</div>
                                            <a href="#" class="btn btn-warning btn-sm mt-2" onclick="filterByStatus('pending')">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-success h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <i class="fas fa-money-bill-wave fa-2x text-success"></i>
                                        </div>
                                        <div>
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                ترقيات راتب
                                            </div>
                                            <div class="h6 mb-0 font-weight-bold text-gray-800" id="salary-promotions">0</div>
                                            <a href="#" class="btn btn-success btn-sm mt-2" onclick="filterByType('salary')">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-info h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <i class="fas fa-star fa-2x text-info"></i>
                                        </div>
                                        <div>
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                ترقيات شاملة
                                            </div>
                                            <div class="h6 mb-0 font-weight-bold text-gray-800" id="comprehensive-promotions">0</div>
                                            <a href="#" class="btn btn-info btn-sm mt-2" onclick="filterByType('comprehensive')">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-primary h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <i class="fas fa-file-export fa-2x text-primary"></i>
                                        </div>
                                        <div>
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                تقرير شامل
                                            </div>
                                            <div class="h6 mb-0 font-weight-bold text-gray-800">تصدير</div>
                                            <button type="button" class="btn btn-primary btn-sm mt-2" onclick="exportPromotions()">
                                                <i class="fas fa-download"></i>
                                                Excel
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-filter"></i>
                        فلترة النتائج
                    </h6>
                </div>
                <div class="card-body">
                    <form id="filter-form">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="employee_filter">الموظف</label>
                                    <select class="form-control select2" id="employee_filter" name="employee_id">
                                        <option value="">جميع الموظفين</option>
                                        {{-- Employee options will be loaded via AJAX --}}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="status_filter">الحالة</label>
                                    <select class="form-control" id="status_filter" name="status">
                                        <option value="">جميع الحالات</option>
                                        <option value="pending">في الانتظار</option>
                                        <option value="approved">معتمد</option>
                                        <option value="rejected">مرفوض</option>
                                        <option value="completed">مكتمل</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="type_filter">نوع الترقية</label>
                                    <select class="form-control" id="type_filter" name="type">
                                        <option value="">جميع الأنواع</option>
                                        <option value="position">ترقية منصب</option>
                                        <option value="grade">ترقية درجة</option>
                                        <option value="salary">ترقية راتب</option>
                                        <option value="comprehensive">ترقية شاملة</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="performance_filter">تقييم الأداء</label>
                                    <select class="form-control" id="performance_filter" name="performance_rating">
                                        <option value="">جميع التقييمات</option>
                                        <option value="excellent">ممتاز</option>
                                        <option value="very_good">جيد جداً</option>
                                        <option value="good">جيد</option>
                                        <option value="satisfactory">مرضي</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_from">من تاريخ</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_to">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="salary_min">الحد الأدنى للراتب</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="salary_min" name="salary_min" step="0.01" min="0">
                                        <div class="input-group-append">
                                            <span class="input-group-text">ج.م</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="salary_max">الحد الأقصى للراتب</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="salary_max" name="salary_max" step="0.01" min="0">
                                        <div class="input-group-append">
                                            <span class="input-group-text">ج.م</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <button type="button" class="btn btn-primary" onclick="applyFilters()">
                                    <i class="fas fa-search"></i>
                                    تطبيق الفلاتر
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                    <i class="fas fa-times"></i>
                                    مسح الفلاتر
                                </button>
                                <button type="button" class="btn btn-info" onclick="exportFiltered()">
                                    <i class="fas fa-file-excel"></i>
                                    تصدير النتائج المفلترة
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Promotions Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-table"></i>
                        قائمة الترقيات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="promotions-table" width="100%" cellspacing="0">
                            <thead class="thead-light">
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>الموظف</th>
                                    <th>نوع الترقية</th>
                                    <th>تفاصيل التغيير</th>
                                    <th>تاريخ الطلب</th>
                                    <th>تاريخ السريان</th>
                                    <th>الحالة</th>
                                    <th>تقييم الأداء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        placeholder: 'اختر موظف...',
        allowClear: true,
        language: 'ar'
    });

    // Load employees for filter
    loadEmployees();

    // Load statistics
    loadStatistics();

    // Initialize DataTable
    initializeDataTable();

    // Filter change handlers
    $('#employee_filter, #status_filter, #type_filter, #performance_filter, #date_from, #date_to, #salary_min, #salary_max').change(function() {
        $('#promotions-table').DataTable().ajax.reload();
    });
});

function initializeDataTable() {
    $('#promotions-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("employee-promotions.data") }}',
            data: function(d) {
                d.employee_id = $('#employee_filter').val();
                d.status = $('#status_filter').val();
                d.type = $('#type_filter').val();
                d.performance_rating = $('#performance_filter').val();
                d.date_from = $('#date_from').val();
                d.date_to = $('#date_to').val();
                d.salary_min = $('#salary_min').val();
                d.salary_max = $('#salary_max').val();
            }
        },
        columns: [
            {
                data: 'id',
                name: 'id',
                render: function(data, type, row) {
                    return '#' + data;
                }
            },
            {
                data: 'employee',
                name: 'employee.name',
                render: function(data, type, row) {
                    return `
                        <div class="d-flex align-items-center">
                            <img src="${data.avatar || '/images/default-avatar.png'}"
                                 class="rounded-circle mr-2" width="30" height="30">
                            <div>
                                <strong>${data.name}</strong><br>
                                <small class="text-muted">${data.employee_code}</small>
                            </div>
                        </div>
                    `;
                }
            },
            {
                data: 'type',
                name: 'type',
                render: function(data, type, row) {
                    const types = {
                        'position': '<span class="badge badge-info">ترقية منصب</span>',
                        'grade': '<span class="badge badge-warning">ترقية درجة</span>',
                        'salary': '<span class="badge badge-success">ترقية راتب</span>',
                        'comprehensive': '<span class="badge badge-primary">ترقية شاملة</span>'
                    };
                    return types[data] || data;
                }
            },
            {
                data: 'changes',
                name: 'changes',
                orderable: false,
                render: function(data, type, row) {
                    let changes = '';

                    if (row.type === 'position' || row.type === 'comprehensive') {
                        if (row.current_position && row.new_position) {
                            changes += `<div><small class="text-muted">المنصب:</small> ${row.current_position} → ${row.new_position}</div>`;
                        }
                    }

                    if (row.type === 'grade' || row.type === 'comprehensive') {
                        if (row.current_grade && row.new_grade) {
                            changes += `<div><small class="text-muted">الدرجة:</small> ${row.current_grade} → ${row.new_grade}</div>`;
                        }
                    }

                    if (row.type === 'salary' || row.type === 'comprehensive') {
                        if (row.current_salary && row.new_salary) {
                            const increase = row.new_salary - row.current_salary;
                            changes += `<div><small class="text-muted">الراتب:</small> ${parseFloat(row.current_salary).toLocaleString('ar-EG')} → ${parseFloat(row.new_salary).toLocaleString('ar-EG')} ج.م</div>`;
                            changes += `<div><small class="text-success">زيادة: ${increase.toLocaleString('ar-EG')} ج.م</small></div>`;
                        }
                    }

                    return changes || '<span class="text-muted">لا توجد تفاصيل</span>';
                }
            },
            {
                data: 'created_at',
                name: 'created_at',
                render: function(data, type, row) {
                    return new Date(data).toLocaleDateString('ar-EG');
                }
            },
            {
                data: 'effective_date',
                name: 'effective_date',
                render: function(data, type, row) {
                    return data ? new Date(data).toLocaleDateString('ar-EG') : '<span class="text-muted">غير محدد</span>';
                }
            },
            {
                data: 'status',
                name: 'status',
                render: function(data, type, row) {
                    const statuses = {
                        'pending': '<span class="badge badge-warning">في الانتظار</span>',
                        'approved': '<span class="badge badge-success">معتمد</span>',
                        'rejected': '<span class="badge badge-danger">مرفوض</span>',
                        'completed': '<span class="badge badge-info">مكتمل</span>'
                    };
                    return statuses[data] || data;
                }
            },
            {
                data: 'performance_rating',
                name: 'performance_rating',
                render: function(data, type, row) {
                    if (!data) return '<span class="text-muted">غير محدد</span>';

                    const ratings = {
                        'excellent': '<span class="badge badge-success">ممتاز</span>',
                        'very_good': '<span class="badge badge-info">جيد جداً</span>',
                        'good': '<span class="badge badge-primary">جيد</span>',
                        'satisfactory': '<span class="badge badge-secondary">مرضي</span>'
                    };
                    return ratings[data] || data;
                }
            },
            {
                data: 'actions',
                name: 'actions',
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    let actions = `
                        <a href="/employee-promotions/${row.id}" class="btn btn-info btn-sm" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </a>
                    `;

                    if (row.status === 'pending') {
                        actions += `
                            <a href="/employee-promotions/${row.id}/edit" class="btn btn-primary btn-sm ml-1" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                        `;
                    }

                    return actions;
                }
            }
        ],
        order: [[0, 'desc']],
        pageLength: 25,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: '<i class="fas fa-file-excel"></i> Excel',
                className: 'btn btn-success btn-sm',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6, 7]
                }
            },
            {
                extend: 'pdf',
                text: '<i class="fas fa-file-pdf"></i> PDF',
                className: 'btn btn-danger btn-sm',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6, 7]
                }
            }
        ]
    });
}

function loadEmployees() {
    $.get('/api/employees')
        .done(function(response) {
            if (response.success) {
                const employees = response.data;
                const select = $('#employee_filter');

                employees.forEach(function(employee) {
                    select.append(`<option value="${employee.id}">${employee.name} - ${employee.employee_code}</option>`);
                });
            }
        })
        .fail(function() {
            console.error('Failed to load employees');
        });
}

function loadStatistics() {
    // Show loading spinners
    $('#total-promotions, #pending-promotions, #approved-promotions, #completed-promotions').html('<div class="spinner-border spinner-border-sm" role="status"></div>');
    $('#pending-count, #salary-promotions, #comprehensive-promotions').html('<div class="spinner-border spinner-border-sm" role="status"></div>');

    $.get('/api/promotions/statistics')
        .done(function(response) {
            if (response.success) {
                const stats = response.data;

                // Update main statistics
                $('#total-promotions').text(stats.total || 0);
                $('#pending-promotions').text(stats.pending || 0);
                $('#approved-promotions').text(stats.approved || 0);
                $('#completed-promotions').text(stats.completed || 0);

                // Update quick actions
                $('#pending-count').text(stats.pending || 0);
                $('#salary-promotions').text(stats.salary_type || 0);
                $('#comprehensive-promotions').text(stats.comprehensive_type || 0);
            }
        })
        .fail(function() {
            console.error('Failed to load statistics');
            // Show error state
            $('#total-promotions, #pending-promotions, #approved-promotions, #completed-promotions').text('خطأ');
            $('#pending-count, #salary-promotions, #comprehensive-promotions').text('خطأ');
        });
}

function filterByStatus(status) {
    $('#status_filter').val(status);
    applyFilters();
}

function filterByType(type) {
    $('#type_filter').val(type);
    applyFilters();
}

function applyFilters() {
    $('#promotions-table').DataTable().ajax.reload();
}

function clearFilters() {
    $('#filter-form')[0].reset();
    $('#employee_filter').val(null).trigger('change');
    $('#promotions-table').DataTable().ajax.reload();
}

function exportPromotions() {
    const params = new URLSearchParams({
        employee_id: $('#employee_filter').val() || '',
        status: $('#status_filter').val() || '',
        type: $('#type_filter').val() || '',
        performance_rating: $('#performance_filter').val() || '',
        date_from: $('#date_from').val() || '',
        date_to: $('#date_to').val() || '',
        salary_min: $('#salary_min').val() || '',
        salary_max: $('#salary_max').val() || ''
    });

    window.open(`/employee-promotions/export?${params.toString()}`, '_blank');
}

function exportFiltered() {
    exportPromotions();
}
</script>
@endsection
