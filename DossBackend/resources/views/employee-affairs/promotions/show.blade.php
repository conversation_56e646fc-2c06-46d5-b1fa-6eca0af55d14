@extends('layouts.app')

@section('title', 'تفاصيل طلب الترقية')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-trophy text-primary"></i>
                        تفاصيل طلب الترقية #{{ $promotion->id }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-promotions.index') }}">الترقيات</a></li>
                            <li class="breadcrumb-item active">تفاصيل الترقية</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button type="button" class="btn btn-info btn-sm" onclick="window.print()">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                    @if($promotion->status === 'pending')
                        <a href="{{ route('employee-promotions.edit', $promotion->id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </a>
                    @endif
                    <a href="{{ route('employee-promotions.index') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-right"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Actions -->
    @if($promotion->status === 'pending')
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning shadow">
                <div class="card-header bg-warning text-white py-3">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-clock"></i>
                        إجراءات الموافقة - طلب في الانتظار
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-success btn-block" data-toggle="modal" data-target="#approveModal">
                                <i class="fas fa-check"></i>
                                اعتماد الترقية
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-danger btn-block" data-toggle="modal" data-target="#rejectModal">
                                <i class="fas fa-times"></i>
                                رفض الترقية
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @elseif($promotion->status === 'approved')
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-success shadow">
                <div class="card-header bg-success text-white py-3">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-check"></i>
                        ترقية معتمدة - جاهزة للتنفيذ
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <p class="mb-2">
                                <strong>تم اعتماد الترقية في:</strong> {{ $promotion->approved_at->format('Y-m-d H:i') }}
                            </p>
                            @if($promotion->approved_by)
                                <p class="mb-0">
                                    <strong>تم الاعتماد بواسطة:</strong> {{ $promotion->approvedBy->name }}
                                </p>
                            @endif
                        </div>
                        <div class="col-md-4">
                            <button type="button" class="btn btn-primary btn-block" data-toggle="modal" data-target="#completeModal">
                                <i class="fas fa-check-double"></i>
                                تأكيد تنفيذ الترقية
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Main Content -->
    <div class="row">
        <div class="col-lg-8">
            <!-- Promotion Details -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle"></i>
                        تفاصيل الترقية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>رقم الطلب:</strong> #{{ $promotion->id }}</p>
                            <p><strong>نوع الترقية:</strong> 
                                @switch($promotion->type)
                                    @case('position')
                                        <span class="badge badge-info">ترقية منصب</span>
                                        @break
                                    @case('grade')
                                        <span class="badge badge-warning">ترقية درجة</span>
                                        @break
                                    @case('salary')
                                        <span class="badge badge-success">ترقية راتب</span>
                                        @break
                                    @case('comprehensive')
                                        <span class="badge badge-primary">ترقية شاملة</span>
                                        @break
                                @endswitch
                            </p>
                            <p><strong>تاريخ الطلب:</strong> {{ $promotion->created_at->format('Y-m-d H:i') }}</p>
                            <p><strong>تاريخ السريان:</strong> {{ $promotion->effective_date ? $promotion->effective_date->format('Y-m-d') : 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>الحالة:</strong> 
                                @switch($promotion->status)
                                    @case('pending')
                                        <span class="badge badge-warning">في الانتظار</span>
                                        @break
                                    @case('approved')
                                        <span class="badge badge-success">معتمد</span>
                                        @break
                                    @case('rejected')
                                        <span class="badge badge-danger">مرفوض</span>
                                        @break
                                    @case('completed')
                                        <span class="badge badge-info">مكتمل</span>
                                        @break
                                @endswitch
                            </p>
                            @if($promotion->performance_rating)
                                <p><strong>تقييم الأداء:</strong> 
                                    @switch($promotion->performance_rating)
                                        @case('excellent')
                                            <span class="badge badge-success">ممتاز</span>
                                            @break
                                        @case('very_good')
                                            <span class="badge badge-info">جيد جداً</span>
                                            @break
                                        @case('good')
                                            <span class="badge badge-primary">جيد</span>
                                            @break
                                        @case('satisfactory')
                                            <span class="badge badge-secondary">مرضي</span>
                                            @break
                                    @endswitch
                                </p>
                            @endif
                            @if($promotion->approved_at)
                                <p><strong>تاريخ الاعتماد:</strong> {{ $promotion->approved_at->format('Y-m-d H:i') }}</p>
                            @endif
                            @if($promotion->completed_at)
                                <p><strong>تاريخ التنفيذ:</strong> {{ $promotion->completed_at->format('Y-m-d H:i') }}</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Promotion Changes -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-exchange-alt"></i>
                        تفاصيل التغييرات
                    </h6>
                </div>
                <div class="card-body">
                    @if(in_array($promotion->type, ['position', 'comprehensive']))
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="card border-left-secondary">
                                    <div class="card-body">
                                        <h6 class="text-secondary">المنصب الحالي</h6>
                                        <p class="h5 mb-0">{{ $promotion->currentPosition->name ?? 'غير محدد' }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-left-primary">
                                    <div class="card-body">
                                        <h6 class="text-primary">المنصب الجديد</h6>
                                        <p class="h5 mb-0">{{ $promotion->newPosition->name ?? 'غير محدد' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(in_array($promotion->type, ['grade', 'comprehensive']))
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="card border-left-secondary">
                                    <div class="card-body">
                                        <h6 class="text-secondary">الدرجة الحالية</h6>
                                        <p class="h5 mb-0">{{ $promotion->current_grade ?? 'غير محدد' }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-left-primary">
                                    <div class="card-body">
                                        <h6 class="text-primary">الدرجة الجديدة</h6>
                                        <p class="h5 mb-0">{{ $promotion->new_grade ?? 'غير محدد' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(in_array($promotion->type, ['salary', 'comprehensive']))
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="card border-left-secondary">
                                    <div class="card-body">
                                        <h6 class="text-secondary">الراتب الحالي</h6>
                                        <p class="h5 mb-0">{{ number_format($promotion->current_salary ?? 0, 2) }} ج.م</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-left-primary">
                                    <div class="card-body">
                                        <h6 class="text-primary">الراتب الجديد</h6>
                                        <p class="h5 mb-0">{{ number_format($promotion->new_salary ?? 0, 2) }} ج.م</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if($promotion->salary_increase)
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-left-success">
                                        <div class="card-body">
                                            <h6 class="text-success">مقدار الزيادة</h6>
                                            <p class="h5 mb-0">{{ number_format($promotion->salary_increase, 2) }} ج.م</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-left-info">
                                        <div class="card-body">
                                            <h6 class="text-info">نسبة الزيادة</h6>
                                            <p class="h5 mb-0">{{ number_format($promotion->increase_percentage ?? 0, 2) }}%</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    @endif
                </div>
            </div>

            <!-- Justification -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-clipboard-list"></i>
                        مبررات الترقية
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ $promotion->justification ?? 'لا توجد مبررات مسجلة' }}</p>
                </div>
            </div>

            @if($promotion->notes)
            <!-- Notes -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-secondary">
                        <i class="fas fa-sticky-note"></i>
                        ملاحظات إضافية
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ $promotion->notes }}</p>
                </div>
            </div>
            @endif

            @if($promotion->rejection_reason)
            <!-- Rejection Reason -->
            <div class="card shadow mt-4 border-danger">
                <div class="card-header bg-danger text-white py-3">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-times-circle"></i>
                        سبب الرفض
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ $promotion->rejection_reason }}</p>
                    @if($promotion->rejected_at)
                        <hr>
                        <small class="text-muted">
                            تم الرفض في: {{ $promotion->rejected_at->format('Y-m-d H:i') }}
                            @if($promotion->rejectedBy)
                                بواسطة: {{ $promotion->rejectedBy->name }}
                            @endif
                        </small>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Employee Info -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user"></i>
                        معلومات الموظف
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <img src="{{ $promotion->employee->avatar ?? '/images/default-avatar.png' }}" 
                             class="rounded-circle" width="80" height="80" alt="صورة الموظف">
                    </div>
                    <p><strong>الاسم:</strong> {{ $promotion->employee->name }}</p>
                    <p><strong>الكود:</strong> {{ $promotion->employee->employee_code }}</p>
                    <p><strong>القسم:</strong> {{ $promotion->employee->department->name ?? 'غير محدد' }}</p>
                    <p><strong>الفرع:</strong> {{ $promotion->employee->branch->name ?? 'غير محدد' }}</p>
                    <p><strong>المنصب الحالي:</strong> {{ $promotion->employee->position->name ?? 'غير محدد' }}</p>
                    <p><strong>تاريخ التوظيف:</strong> {{ $promotion->employee->hire_date ? $promotion->employee->hire_date->format('Y-m-d') : 'غير محدد' }}</p>
                    
                    <div class="mt-3">
                        <a href="{{ route('employees.show', $promotion->employee->id) }}" class="btn btn-outline-primary btn-sm btn-block">
                            <i class="fas fa-eye"></i>
                            عرض ملف الموظف
                        </a>
                    </div>
                </div>
            </div>

            <!-- Promotion Timeline -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-history"></i>
                        مراحل الترقية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">تم إنشاء الطلب</h6>
                                <p class="timeline-text">{{ $promotion->created_at->format('Y-m-d H:i') }}</p>
                            </div>
                        </div>
                        
                        @if($promotion->approved_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">تم اعتماد الطلب</h6>
                                <p class="timeline-text">{{ $promotion->approved_at->format('Y-m-d H:i') }}</p>
                                @if($promotion->approvedBy)
                                    <small class="text-muted">بواسطة: {{ $promotion->approvedBy->name }}</small>
                                @endif
                            </div>
                        </div>
                        @endif
                        
                        @if($promotion->rejected_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">تم رفض الطلب</h6>
                                <p class="timeline-text">{{ $promotion->rejected_at->format('Y-m-d H:i') }}</p>
                                @if($promotion->rejectedBy)
                                    <small class="text-muted">بواسطة: {{ $promotion->rejectedBy->name }}</small>
                                @endif
                            </div>
                        </div>
                        @endif
                        
                        @if($promotion->completed_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">تم تنفيذ الترقية</h6>
                                <p class="timeline-text">{{ $promotion->completed_at->format('Y-m-d H:i') }}</p>
                                @if($promotion->completedBy)
                                    <small class="text-muted">بواسطة: {{ $promotion->completedBy->name }}</small>
                                @endif
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Promotion Summary -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-chart-line"></i>
                        ملخص الترقية
                    </h6>
                </div>
                <div class="card-body">
                    @if(in_array($promotion->type, ['salary', 'comprehensive']) && $promotion->salary_increase)
                        <div class="text-center mb-3">
                            <h4 class="text-success">{{ number_format($promotion->salary_increase, 2) }} ج.م</h4>
                            <small class="text-muted">زيادة شهرية</small>
                        </div>
                        <hr>
                        <div class="text-center mb-3">
                            <h5 class="text-info">{{ number_format($promotion->increase_percentage ?? 0, 2) }}%</h5>
                            <small class="text-muted">نسبة الزيادة</small>
                        </div>
                        <hr>
                        <div class="text-center">
                            <h5 class="text-primary">{{ number_format(($promotion->salary_increase ?? 0) * 12, 2) }} ج.م</h5>
                            <small class="text-muted">زيادة سنوية</small>
                        </div>
                    @else
                        <div class="text-center">
                            <i class="fas fa-trophy fa-3x text-warning mb-3"></i>
                            <p class="text-muted">ترقية غير مالية</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Approve Modal -->
<div class="modal fade" id="approveModal" tabindex="-1" role="dialog" aria-labelledby="approveModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="approveModalLabel">
                    <i class="fas fa-check"></i>
                    اعتماد طلب الترقية
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-promotions.approve', $promotion->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        سيتم اعتماد طلب الترقية وإرساله للتنفيذ.
                    </div>

                    <div class="form-group">
                        <label for="approval_notes">ملاحظات الاعتماد</label>
                        <textarea class="form-control" id="approval_notes" name="approval_notes" rows="3"
                                  placeholder="أي ملاحظات حول اعتماد الترقية (اختياري)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i>
                        تأكيد الاعتماد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" role="dialog" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="rejectModalLabel">
                    <i class="fas fa-times"></i>
                    رفض طلب الترقية
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-promotions.reject', $promotion->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        سيتم رفض طلب الترقية نهائياً. هذا الإجراء لا يمكن التراجع عنه.
                    </div>

                    <div class="form-group">
                        <label for="rejection_reason">سبب الرفض <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="4"
                                  placeholder="يرجى توضيح أسباب رفض طلب الترقية" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times"></i>
                        تأكيد الرفض
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Complete Modal -->
<div class="modal fade" id="completeModal" tabindex="-1" role="dialog" aria-labelledby="completeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="completeModalLabel">
                    <i class="fas fa-check-double"></i>
                    تأكيد تنفيذ الترقية
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-promotions.complete', $promotion->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-success">
                        <i class="fas fa-info-circle"></i>
                        سيتم تنفيذ الترقية وتحديث بيانات الموظف وفقاً للتغييرات المعتمدة.
                    </div>

                    <div class="form-group">
                        <label for="completion_date">تاريخ التنفيذ الفعلي</label>
                        <input type="date" class="form-control" id="completion_date" name="completion_date"
                               value="{{ date('Y-m-d') }}" max="{{ date('Y-m-d') }}">
                        <small class="text-muted">اتركه فارغاً لاستخدام التاريخ الحالي</small>
                    </div>

                    <div class="form-group">
                        <label for="completion_notes">ملاحظات التنفيذ</label>
                        <textarea class="form-control" id="completion_notes" name="completion_notes" rows="3"
                                  placeholder="أي ملاحظات حول تنفيذ الترقية (اختياري)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-check-double"></i>
                        تأكيد التنفيذ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
/* Timeline Styles */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e3e6f0;
}

.timeline-content {
    background: #f8f9fc;
    padding: 15px;
    border-radius: 5px;
    border-left: 3px solid #5a5c69;
}

.timeline-title {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
    color: #5a5c69;
}

.timeline-text {
    margin: 0;
    font-size: 13px;
    color: #858796;
}

/* Print Styles */
@media print {
    .btn, .modal, .breadcrumb, .card-header .btn {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }

    .card-header {
        background: #f8f9fc !important;
        color: #000 !important;
        border-bottom: 1px solid #000 !important;
    }

    .badge {
        border: 1px solid #000 !important;
        color: #000 !important;
    }

    .timeline-marker {
        box-shadow: none !important;
        border: 2px solid #000 !important;
    }

    .timeline::before {
        background: #000 !important;
    }

    .timeline-content {
        background: #fff !important;
        border-left: 3px solid #000 !important;
    }
}

/* Status Badge Colors */
.badge-warning {
    background-color: #f6c23e !important;
}

.badge-success {
    background-color: #1cc88a !important;
}

.badge-danger {
    background-color: #e74a3b !important;
}

.badge-info {
    background-color: #36b9cc !important;
}

.badge-primary {
    background-color: #4e73df !important;
}

.badge-secondary {
    background-color: #858796 !important;
}

/* Card Border Colors */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

.border-left-secondary {
    border-left: 0.25rem solid #858796 !important;
}
</style>
@endsection
