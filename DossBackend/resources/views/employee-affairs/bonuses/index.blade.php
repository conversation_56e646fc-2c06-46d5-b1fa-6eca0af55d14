@extends('layouts.app')

@section('title', 'إدارة المكافآت')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0 font-size-18">إدارة المكافآت</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                        <li class="breadcrumb-item active">المكافآت</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Card -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4 class="card-title">فلترة المكافآت</h4>
                        </div>
                        <div class="col-auto">
                            <a href="{{ route('employee-bonuses.create') }}" class="btn btn-primary">
                                <i class="bx bx-plus"></i> إضافة مكافأة جديدة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('employee-bonuses.index') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">الموظف</label>
                                    <select name="user_id" class="form-select">
                                        <option value="">جميع الموظفين</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                                {{ $user->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">نوع المكافأة</label>
                                    <select name="bonus_type" class="form-select">
                                        <option value="">جميع الأنواع</option>
                                        @foreach(\App\Models\EmployeeBonus::BONUS_TYPES as $key => $label)
                                            <option value="{{ $key }}" {{ request('bonus_type') == $key ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">حالة الموافقة</label>
                                    <select name="approval_status" class="form-select">
                                        <option value="">جميع الحالات</option>
                                        @foreach(\App\Models\EmployeeBonus::APPROVAL_STATUSES as $key => $label)
                                            <option value="{{ $key }}" {{ request('approval_status') == $key ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bx bx-search"></i> بحث
                                        </button>
                                        <a href="{{ route('employee-bonuses.index') }}" class="btn btn-secondary">
                                            <i class="bx bx-refresh"></i> إعادة تعيين
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bonuses Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">قائمة المكافآت ({{ $bonuses->total() }} مكافأة)</h4>
                </div>
                <div class="card-body">
                    @if($bonuses->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الموظف</th>
                                        <th>نوع المكافأة</th>
                                        <th>الفئة</th>
                                        <th>المبلغ</th>
                                        <th>تاريخ السريان</th>
                                        <th>حالة الموافقة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($bonuses as $bonus)
                                    <tr>
                                        <td>{{ $loop->iteration + ($bonuses->currentPage() - 1) * $bonuses->perPage() }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div>
                                                    <h6 class="mb-0">{{ $bonus->user->name }}</h6>
                                                    <small class="text-muted">{{ $bonus->user->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">{{ $bonus->bonus_type_label }}</span>
                                        </td>
                                        <td>{{ $bonus->bonus_category_label }}</td>
                                        <td>
                                            <strong class="text-success">{{ $bonus->formatted_amount }}</strong>
                                        </td>
                                        <td>{{ $bonus->effective_date }}</td>
                                        <td>
                                            @if($bonus->approval_status == 'pending')
                                                <span class="badge badge-warning">{{ $bonus->approval_status_label }}</span>
                                            @elseif($bonus->approval_status == 'approved')
                                                <span class="badge badge-success">{{ $bonus->approval_status_label }}</span>
                                            @else
                                                <span class="badge badge-danger">{{ $bonus->approval_status_label }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $bonus->created_at->format('Y-m-d') }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('employee-bonuses.show', $bonus) }}" 
                                                   class="btn btn-sm btn-info" title="عرض">
                                                    <i class="bx bx-show"></i>
                                                </a>
                                                
                                                @if($bonus->canBeApproved())
                                                    <a href="{{ route('employee-bonuses.edit', $bonus) }}" 
                                                       class="btn btn-sm btn-primary" title="تعديل">
                                                        <i class="bx bx-edit"></i>
                                                    </a>
                                                    
                                                    <button type="button" class="btn btn-sm btn-success" 
                                                            onclick="approveBonus({{ $bonus->id }})" title="موافقة">
                                                        <i class="bx bx-check"></i>
                                                    </button>
                                                    
                                                    <button type="button" class="btn btn-sm btn-danger" 
                                                            onclick="rejectBonus({{ $bonus->id }})" title="رفض">
                                                        <i class="bx bx-x"></i>
                                                    </button>
                                                @endif
                                                
                                                @if($bonus->isPending())
                                                    <button type="button" class="btn btn-sm btn-danger" 
                                                            onclick="deleteBonus({{ $bonus->id }})" title="حذف">
                                                        <i class="bx bx-trash"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <div class="row">
                            <div class="col-sm-12 col-md-5">
                                <div class="dataTables_info">
                                    عرض {{ $bonuses->firstItem() }} إلى {{ $bonuses->lastItem() }} من {{ $bonuses->total() }} مكافأة
                                </div>
                            </div>
                            <div class="col-sm-12 col-md-7">
                                {{ $bonuses->links() }}
                            </div>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="bx bx-gift font-size-48 text-muted"></i>
                            <h5 class="mt-3">لا توجد مكافآت</h5>
                            <p class="text-muted">لم يتم العثور على أي مكافآت تطابق معايير البحث</p>
                            <a href="{{ route('employee-bonuses.create') }}" class="btn btn-primary">
                                <i class="bx bx-plus"></i> إضافة مكافأة جديدة
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function approveBonus(bonusId) {
    if (confirm('هل أنت متأكد من الموافقة على هذه المكافأة؟')) {
        $.post(`/admin/employee-bonuses/${bonusId}/approve`, {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            location.reload();
        })
        .fail(function() {
            alert('حدث خطأ أثناء الموافقة على المكافأة');
        });
    }
}

function rejectBonus(bonusId) {
    if (confirm('هل أنت متأكد من رفض هذه المكافأة؟')) {
        $.post(`/admin/employee-bonuses/${bonusId}/reject`, {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            location.reload();
        })
        .fail(function() {
            alert('حدث خطأ أثناء رفض المكافأة');
        });
    }
}

function deleteBonus(bonusId) {
    if (confirm('هل أنت متأكد من حذف هذه المكافأة؟ لا يمكن التراجع عن هذا الإجراء.')) {
        $.ajax({
            url: `/admin/employee-bonuses/${bonusId}`,
            type: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            }
        })
        .done(function(response) {
            location.reload();
        })
        .fail(function() {
            alert('حدث خطأ أثناء حذف المكافأة');
        });
    }
}
</script>
@endpush
@endsection
