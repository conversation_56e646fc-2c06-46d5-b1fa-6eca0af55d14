@extends('layouts.app')

@section('title', 'تعديل الإنذار')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        تعديل الإنذار #{{ $warning->id }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-warnings.index') }}">الإنذارات</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-warnings.show', $warning->id) }}">إنذار #{{ $warning->id }}</a></li>
                            <li class="breadcrumb-item active">تعديل</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('employee-warnings.show', $warning->id) }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-right"></i>
                        رجوع
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Warning Status Alert -->
    @if($warning->status === 'acknowledged')
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <strong>تنبيه:</strong> هذا الإنذار تم الإقرار به من قبل الموظف. التعديلات ستتطلب إعادة إقرار.
        </div>
    @elseif($warning->status === 'escalated')
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>تحذير:</strong> هذا الإنذار تم تصعيده. التعديلات قد تؤثر على الإجراءات التأديبية.
        </div>
    @endif

    <form action="{{ route('employee-warnings.update', $warning->id) }}" method="POST" enctype="multipart/form-data" id="warning-form">
        @csrf
        @method('PUT')
        
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Employee Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-user"></i>
                            معلومات الموظف
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="employee_id">الموظف <span class="text-danger">*</span></label>
                            <select class="form-control select2" id="employee_id" name="employee_id" required 
                                    {{ $warning->status === 'acknowledged' ? 'disabled' : '' }}>
                                <option value="">اختر الموظف</option>
                                @foreach($employees as $employee)
                                    <option value="{{ $employee->id }}" 
                                            {{ $warning->employee_id == $employee->id ? 'selected' : '' }}
                                            data-department="{{ $employee->department->name ?? '' }}"
                                            data-position="{{ $employee->position->title ?? '' }}"
                                            data-avatar="{{ $employee->avatar ?? '' }}">
                                        {{ $employee->name }} - {{ $employee->employee_code }}
                                    </option>
                                @endforeach
                            </select>
                            @error('employee_id')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Employee Details Display -->
                        <div id="employee-details" class="mt-3" style="{{ $warning->employee_id ? '' : 'display: none;' }}">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>القسم</label>
                                        <input type="text" class="form-control" id="employee_department" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>المنصب</label>
                                        <input type="text" class="form-control" id="employee_position" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Warning Details -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            تفاصيل الإنذار
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="warning_type">نوع الإنذار <span class="text-danger">*</span></label>
                                    <select class="form-control" id="warning_type" name="warning_type" required>
                                        <option value="">اختر نوع الإنذار</option>
                                        <option value="attendance" {{ $warning->warning_type == 'attendance' ? 'selected' : '' }}>حضور وانصراف</option>
                                        <option value="performance" {{ $warning->warning_type == 'performance' ? 'selected' : '' }}>أداء وظيفي</option>
                                        <option value="conduct" {{ $warning->warning_type == 'conduct' ? 'selected' : '' }}>سلوك مهني</option>
                                        <option value="policy_violation" {{ $warning->warning_type == 'policy_violation' ? 'selected' : '' }}>مخالفة سياسة</option>
                                        <option value="safety" {{ $warning->warning_type == 'safety' ? 'selected' : '' }}>أمان وسلامة</option>
                                        <option value="other" {{ $warning->warning_type == 'other' ? 'selected' : '' }}>أخرى</option>
                                    </select>
                                    @error('warning_type')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="severity_level">مستوى الخطورة <span class="text-danger">*</span></label>
                                    <select class="form-control" id="severity_level" name="severity_level" required>
                                        <option value="">اختر مستوى الخطورة</option>
                                        <option value="minor" {{ $warning->severity_level == 'minor' ? 'selected' : '' }}>بسيط</option>
                                        <option value="moderate" {{ $warning->severity_level == 'moderate' ? 'selected' : '' }}>متوسط</option>
                                        <option value="major" {{ $warning->severity_level == 'major' ? 'selected' : '' }}>خطير</option>
                                        <option value="critical" {{ $warning->severity_level == 'critical' ? 'selected' : '' }}>حرج</option>
                                    </select>
                                    @error('severity_level')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="warning_date">تاريخ الإنذار <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="warning_date" name="warning_date" 
                                           value="{{ $warning->warning_date }}" required>
                                    @error('warning_date')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="incident_date">تاريخ الحادثة</label>
                                    <input type="date" class="form-control" id="incident_date" name="incident_date" 
                                           value="{{ $warning->incident_date }}">
                                    @error('incident_date')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="violation_description">وصف المخالفة <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="violation_description" name="violation_description" 
                                      rows="4" required placeholder="اكتب وصفاً مفصلاً للمخالفة...">{{ $warning->violation_description }}</textarea>
                            <small class="form-text text-muted">
                                <span id="violation-char-count">{{ strlen($warning->violation_description) }}</span> / 1000 حرف
                            </small>
                            @error('violation_description')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="corrective_action">الإجراء التصحيحي المطلوب</label>
                            <textarea class="form-control" id="corrective_action" name="corrective_action" 
                                      rows="3" placeholder="حدد الإجراءات المطلوبة من الموظف...">{{ $warning->corrective_action }}</textarea>
                            @error('corrective_action')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Follow-up and Escalation -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-info">
                            <i class="fas fa-calendar-check"></i>
                            المتابعة والتصعيد
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="follow_up_date">تاريخ المتابعة</label>
                                    <input type="date" class="form-control" id="follow_up_date" name="follow_up_date" 
                                           value="{{ $warning->follow_up_date }}">
                                    @error('follow_up_date')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="escalation_level">مستوى التصعيد</label>
                                    <select class="form-control" id="escalation_level" name="escalation_level">
                                        <option value="">لا يوجد تصعيد</option>
                                        <option value="supervisor" {{ $warning->escalation_level == 'supervisor' ? 'selected' : '' }}>المشرف المباشر</option>
                                        <option value="department_head" {{ $warning->escalation_level == 'department_head' ? 'selected' : '' }}>رئيس القسم</option>
                                        <option value="hr_manager" {{ $warning->escalation_level == 'hr_manager' ? 'selected' : '' }}>مدير الموارد البشرية</option>
                                        <option value="general_manager" {{ $warning->escalation_level == 'general_manager' ? 'selected' : '' }}>المدير العام</option>
                                    </select>
                                    @error('escalation_level')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="requires_acknowledgment" 
                                       name="requires_acknowledgment" value="1" 
                                       {{ $warning->requires_acknowledgment ? 'checked' : '' }}>
                                <label class="form-check-label" for="requires_acknowledgment">
                                    يتطلب إقرار من الموظف
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="affects_performance_review" 
                                       name="affects_performance_review" value="1" 
                                       {{ $warning->affects_performance_review ? 'checked' : '' }}>
                                <label class="form-check-label" for="affects_performance_review">
                                    يؤثر على تقييم الأداء
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Status and Actions -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-secondary">
                            <i class="fas fa-cog"></i>
                            الحالة والإجراءات
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="status">حالة الإنذار</label>
                            <select class="form-control" id="status" name="status">
                                <option value="draft" {{ $warning->status == 'draft' ? 'selected' : '' }}>مسودة</option>
                                <option value="issued" {{ $warning->status == 'issued' ? 'selected' : '' }}>صادر</option>
                                <option value="acknowledged" {{ $warning->status == 'acknowledged' ? 'selected' : '' }}>مُقر به</option>
                                <option value="escalated" {{ $warning->status == 'escalated' ? 'selected' : '' }}>مُصعد</option>
                                <option value="resolved" {{ $warning->status == 'resolved' ? 'selected' : '' }}>محلول</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="issued_by">صادر من</label>
                            <select class="form-control" id="issued_by" name="issued_by">
                                <option value="">اختر المصدر</option>
                                @foreach($managers as $manager)
                                    <option value="{{ $manager->id }}" 
                                            {{ $warning->issued_by == $manager->id ? 'selected' : '' }}>
                                        {{ $manager->name }} - {{ $manager->position->title ?? '' }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="notes">ملاحظات إضافية</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" 
                                      placeholder="أي ملاحظات إضافية...">{{ $warning->notes }}</textarea>
                        </div>

                        <hr>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-save"></i>
                                حفظ التعديلات
                            </button>
                            
                            @if($warning->status === 'draft')
                                <button type="button" class="btn btn-success btn-block" onclick="issueWarning()">
                                    <i class="fas fa-paper-plane"></i>
                                    إصدار الإنذار
                                </button>
                            @endif
                            
                            <a href="{{ route('employee-warnings.show', $warning->id) }}" class="btn btn-secondary btn-block">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Warning History -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-warning">
                            <i class="fas fa-history"></i>
                            تاريخ الإنذارات
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="warning-history">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin"></i>
                                جاري التحميل...
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Documents -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-info">
                            <i class="fas fa-paperclip"></i>
                            المرفقات
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="documents">إرفاق مستندات</label>
                            <input type="file" class="form-control-file" id="documents" name="documents[]" 
                                   multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                            <small class="form-text text-muted">
                                يمكن إرفاق ملفات PDF, Word, أو صور (حد أقصى 5 ملفات، 2MB لكل ملف)
                            </small>
                        </div>

                        <!-- Existing Documents -->
                        @if($warning->documents && count($warning->documents) > 0)
                            <div class="existing-documents">
                                <label>المستندات الحالية:</label>
                                @foreach($warning->documents as $document)
                                    <div class="document-item d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <i class="fas fa-file"></i>
                                            <a href="{{ Storage::url($document['path']) }}" target="_blank">
                                                {{ $document['name'] }}
                                            </a>
                                            <small class="text-muted">({{ $document['size'] }})</small>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="removeDocument('{{ $document['id'] }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@section('styles')
<style>
/* Warning Type Badges */
.warning-type-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.type-attendance { background: linear-gradient(45deg, #17a2b8, #138496); color: white; }
.type-performance { background: linear-gradient(45deg, #ffc107, #e0a800); color: #856404; }
.type-conduct { background: linear-gradient(45deg, #dc3545, #c82333); color: white; }
.type-policy_violation { background: linear-gradient(45deg, #fd7e14, #e55a00); color: white; }
.type-safety { background: linear-gradient(45deg, #6f42c1, #59359a); color: white; }
.type-other { background: linear-gradient(45deg, #6c757d, #545b62); color: white; }

/* Severity Level Indicators */
.severity-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.severity-minor { background-color: #28a745; }
.severity-moderate { background-color: #ffc107; }
.severity-major { background-color: #fd7e14; }
.severity-critical { background-color: #dc3545; }

/* Status Badges */
.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid transparent;
}

.status-draft {
    background: linear-gradient(45deg, #6c757d, #545b62);
    color: white;
    border-color: #6c757d;
}

.status-issued {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    border-color: #007bff;
}

.status-acknowledged {
    background: linear-gradient(45deg, #28a745, #1e7e34);
    color: white;
    border-color: #28a745;
}

.status-escalated {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    color: #856404;
    border-color: #ffc107;
}

.status-resolved {
    background: linear-gradient(45deg, #17a2b8, #138496);
    color: white;
    border-color: #17a2b8;
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    background: linear-gradient(45deg, #f8f9fc, #eaecf4);
    border-bottom: 1px solid #e3e6f0;
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

/* Form Enhancements */
.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-control.is-invalid:focus {
    border-color: #e74a3b;
    box-shadow: 0 0 0 0.2rem rgba(231, 74, 59, 0.25);
}

/* Select2 Customization */
.select2-container--default .select2-selection--single {
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 1.5;
    padding-left: 0;
    padding-right: 20px;
}

/* Button Enhancements */
.btn {
    border-radius: 0.35rem;
    font-weight: 600;
    transition: all 0.15s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.btn-primary {
    background: linear-gradient(45deg, #4e73df, #224abe);
    border-color: #4e73df;
}

.btn-success {
    background: linear-gradient(45deg, #1cc88a, #17a673);
    border-color: #1cc88a;
}

.btn-warning {
    background: linear-gradient(45deg, #f6c23e, #dda20a);
    border-color: #f6c23e;
    color: #856404;
}

.btn-danger {
    background: linear-gradient(45deg, #e74a3b, #c0392b);
    border-color: #e74a3b;
}

/* Character Counter */
.char-counter {
    font-size: 0.875rem;
    color: #6c757d;
    text-align: right;
}

.char-counter.warning {
    color: #ffc107;
}

.char-counter.danger {
    color: #dc3545;
}

/* Warning History Timeline */
.warning-history-item {
    position: relative;
    padding-left: 2rem;
    margin-bottom: 1rem;
    border-left: 2px solid #e3e6f0;
}

.warning-history-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 0.5rem;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #4e73df;
}

.warning-history-item:last-child {
    border-left: none;
}

.warning-history-date {
    font-size: 0.75rem;
    color: #6c757d;
    font-weight: 600;
}

.warning-history-content {
    margin-top: 0.25rem;
}

/* Document Items */
.document-item {
    padding: 0.5rem;
    background-color: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
}

.document-item:hover {
    background-color: #eaecf4;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 0.5rem;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid #4e73df;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .card-header h6 {
        font-size: 0.875rem;
    }

    .btn-block {
        margin-bottom: 0.5rem;
    }

    .warning-history-item {
        padding-left: 1.5rem;
    }
}

/* Print Styles */
@media print {
    .btn, .card-header, .breadcrumb {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }

    .form-control {
        border: none !important;
        border-bottom: 1px solid #000 !important;
        background: transparent !important;
    }
}

/* Animation for form validation */
.shake {
    animation: shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes shake {
    10%, 90% { transform: translate3d(-1px, 0, 0); }
    20%, 80% { transform: translate3d(2px, 0, 0); }
    30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
    40%, 60% { transform: translate3d(4px, 0, 0); }
}

/* Escalation Level Indicators */
.escalation-indicator {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

.escalation-supervisor { background-color: #17a2b8; color: white; }
.escalation-department_head { background-color: #ffc107; color: #856404; }
.escalation-hr_manager { background-color: #fd7e14; color: white; }
.escalation-general_manager { background-color: #dc3545; color: white; }
</style>
@endsection

@section('scripts')
<script>
let warningData = @json($warning);
let employeeWarnings = [];

$(document).ready(function() {
    // Initialize Select2
    initializeSelect2();

    // Load employee details if already selected
    if ($('#employee_id').val()) {
        loadEmployeeDetails($('#employee_id').val());
        loadEmployeeWarningHistory($('#employee_id').val());
    }

    // Initialize form validation
    initializeFormValidation();

    // Initialize character counters
    initializeCharacterCounters();

    // Initialize date validations
    initializeDateValidations();

    // Initialize auto-save
    initializeAutoSave();

    // Initialize tooltips
    $('[title]').tooltip();
});

// Initialize Select2
function initializeSelect2() {
    $('#employee_id').select2({
        placeholder: 'اختر الموظف',
        allowClear: true,
        templateResult: formatEmployee,
        templateSelection: formatEmployeeSelection
    });

    $('#issued_by').select2({
        placeholder: 'اختر المصدر',
        allowClear: true
    });
}

// Format employee in dropdown
function formatEmployee(employee) {
    if (!employee.id) return employee.text;

    const department = $(employee.element).data('department') || '';
    const position = $(employee.element).data('position') || '';
    const avatar = $(employee.element).data('avatar') || '/images/default-avatar.png';

    return $(`
        <div class="d-flex align-items-center">
            <img src="${avatar}" class="rounded-circle mr-2" width="24" height="24">
            <div>
                <div>${employee.text}</div>
                <small class="text-muted">${department} - ${position}</small>
            </div>
        </div>
    `);
}

// Format selected employee
function formatEmployeeSelection(employee) {
    return employee.text;
}

// Load employee details
function loadEmployeeDetails(employeeId) {
    if (!employeeId) {
        $('#employee-details').hide();
        return;
    }

    const selectedOption = $(`#employee_id option[value="${employeeId}"]`);
    const department = selectedOption.data('department') || 'غير محدد';
    const position = selectedOption.data('position') || 'غير محدد';

    $('#employee_department').val(department);
    $('#employee_position').val(position);
    $('#employee-details').show();
}

// Load employee warning history
function loadEmployeeWarningHistory(employeeId) {
    if (!employeeId) return;

    $('#warning-history').html(`
        <div class="text-center">
            <div class="loading-spinner"></div>
            <div class="mt-2">جاري التحميل...</div>
        </div>
    `);

    $.ajax({
        url: `{{ route("api.employee-warnings") }}/${employeeId}`,
        method: 'GET',
        success: function(response) {
            employeeWarnings = response;
            displayWarningHistory(response);
        },
        error: function() {
            $('#warning-history').html(`
                <div class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div>خطأ في تحميل البيانات</div>
                </div>
            `);
        }
    });
}

// Display warning history
function displayWarningHistory(warnings) {
    if (warnings.length === 0) {
        $('#warning-history').html(`
            <div class="text-center text-muted">
                <i class="fas fa-info-circle"></i>
                <div>لا توجد إنذارات سابقة</div>
            </div>
        `);
        return;
    }

    let historyHtml = '';
    warnings.forEach(function(warning, index) {
        if (warning.id === warningData.id) return; // Skip current warning

        const statusClass = `status-${warning.status}`;
        const typeClass = `type-${warning.warning_type}`;
        const severityClass = `severity-${warning.severity_level}`;

        historyHtml += `
            <div class="warning-history-item">
                <div class="warning-history-date">
                    ${moment(warning.warning_date).format('YYYY-MM-DD')}
                </div>
                <div class="warning-history-content">
                    <div class="d-flex align-items-center mb-1">
                        <span class="severity-indicator ${severityClass}"></span>
                        <span class="warning-type-badge ${typeClass}">
                            ${getWarningTypeText(warning.warning_type)}
                        </span>
                        <span class="status-badge ${statusClass} ml-2">
                            ${getStatusText(warning.status)}
                        </span>
                    </div>
                    <div class="text-muted small">
                        ${warning.violation_description.substring(0, 100)}...
                    </div>
                </div>
            </div>
        `;
    });

    $('#warning-history').html(historyHtml);

    // Show warning count alert
    if (warnings.length >= 3) {
        showWarningCountAlert(warnings.length);
    }
}

// Show warning count alert
function showWarningCountAlert(count) {
    const alertClass = count >= 5 ? 'alert-danger' : 'alert-warning';
    const alertText = count >= 5 ? 'تحذير: هذا الموظف لديه عدد كبير من الإنذارات!' : 'تنبيه: هذا الموظف لديه عدة إنذارات سابقة.';

    if ($('.warning-count-alert').length === 0) {
        $('.card-body').first().prepend(`
            <div class="alert ${alertClass} warning-count-alert">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>${alertText}</strong>
                <br>
                <small>إجمالي الإنذارات: ${count}</small>
            </div>
        `);
    }
}

// Get warning type text
function getWarningTypeText(type) {
    const types = {
        'attendance': 'حضور وانصراف',
        'performance': 'أداء وظيفي',
        'conduct': 'سلوك مهني',
        'policy_violation': 'مخالفة سياسة',
        'safety': 'أمان وسلامة',
        'other': 'أخرى'
    };
    return types[type] || type;
}

// Get status text
function getStatusText(status) {
    const statuses = {
        'draft': 'مسودة',
        'issued': 'صادر',
        'acknowledged': 'مُقر به',
        'escalated': 'مُصعد',
        'resolved': 'محلول'
    };
    return statuses[status] || status;
}

// Employee selection change handler
$('#employee_id').on('change', function() {
    const employeeId = $(this).val();
    loadEmployeeDetails(employeeId);

    if (employeeId) {
        loadEmployeeWarningHistory(employeeId);
    } else {
        $('#warning-history').html(`
            <div class="text-center text-muted">
                <i class="fas fa-info-circle"></i>
                <div>اختر موظفاً لعرض تاريخ الإنذارات</div>
            </div>
        `);
        $('.warning-count-alert').remove();
    }
});

// Initialize form validation
function initializeFormValidation() {
    $('#warning-form').on('submit', function(e) {
        let isValid = true;

        // Required field validation
        const requiredFields = ['employee_id', 'warning_type', 'severity_level', 'warning_date', 'violation_description'];

        requiredFields.forEach(function(field) {
            const element = $(`#${field}`);
            if (!element.val() || element.val().trim() === '') {
                element.addClass('is-invalid shake');
                isValid = false;

                setTimeout(function() {
                    element.removeClass('shake');
                }, 820);
            } else {
                element.removeClass('is-invalid');
            }
        });

        // Date validation
        if (!validateDates()) {
            isValid = false;
        }

        // Character limit validation
        if (!validateCharacterLimits()) {
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            showAlert('error', 'يرجى تصحيح الأخطاء المحددة في النموذج');
            $('html, body').animate({
                scrollTop: $('.is-invalid').first().offset().top - 100
            }, 500);
        }
    });
}

// Initialize character counters
function initializeCharacterCounters() {
    $('#violation_description').on('input', function() {
        updateCharacterCounter(this, 1000, 'violation-char-count');
    });

    // Initialize counters
    updateCharacterCounter($('#violation_description')[0], 1000, 'violation-char-count');
}

// Update character counter
function updateCharacterCounter(element, maxLength, counterId) {
    const currentLength = $(element).val().length;
    const counter = $(`#${counterId}`);

    counter.text(currentLength);

    if (currentLength > maxLength * 0.9) {
        counter.addClass('danger').removeClass('warning');
    } else if (currentLength > maxLength * 0.7) {
        counter.addClass('warning').removeClass('danger');
    } else {
        counter.removeClass('warning danger');
    }

    if (currentLength > maxLength) {
        $(element).addClass('is-invalid');
        return false;
    } else {
        $(element).removeClass('is-invalid');
        return true;
    }
}

// Initialize date validations
function initializeDateValidations() {
    $('#warning_date, #incident_date, #follow_up_date').on('change', function() {
        validateDates();
    });
}

// Validate dates
function validateDates() {
    let isValid = true;
    const warningDate = $('#warning_date').val();
    const incidentDate = $('#incident_date').val();
    const followUpDate = $('#follow_up_date').val();
    const today = moment().format('YYYY-MM-DD');

    // Warning date should not be in the future
    if (warningDate && warningDate > today) {
        $('#warning_date').addClass('is-invalid');
        showFieldError('#warning_date', 'تاريخ الإنذار لا يمكن أن يكون في المستقبل');
        isValid = false;
    } else {
        $('#warning_date').removeClass('is-invalid');
        hideFieldError('#warning_date');
    }

    // Incident date should be before or equal to warning date
    if (incidentDate && warningDate && incidentDate > warningDate) {
        $('#incident_date').addClass('is-invalid');
        showFieldError('#incident_date', 'تاريخ الحادثة يجب أن يكون قبل أو يساوي تاريخ الإنذار');
        isValid = false;
    } else {
        $('#incident_date').removeClass('is-invalid');
        hideFieldError('#incident_date');
    }

    // Follow-up date should be after warning date
    if (followUpDate && warningDate && followUpDate <= warningDate) {
        $('#follow_up_date').addClass('is-invalid');
        showFieldError('#follow_up_date', 'تاريخ المتابعة يجب أن يكون بعد تاريخ الإنذار');
        isValid = false;
    } else {
        $('#follow_up_date').removeClass('is-invalid');
        hideFieldError('#follow_up_date');
    }

    return isValid;
}

// Validate character limits
function validateCharacterLimits() {
    return updateCharacterCounter($('#violation_description')[0], 1000, 'violation-char-count');
}

// Show field error
function showFieldError(fieldSelector, message) {
    const field = $(fieldSelector);
    let errorDiv = field.siblings('.field-error');

    if (errorDiv.length === 0) {
        errorDiv = $(`<div class="field-error invalid-feedback d-block">${message}</div>`);
        field.after(errorDiv);
    } else {
        errorDiv.text(message);
    }
}

// Hide field error
function hideFieldError(fieldSelector) {
    $(fieldSelector).siblings('.field-error').remove();
}

// Initialize auto-save
function initializeAutoSave() {
    let autoSaveTimer;

    $('#warning-form input, #warning-form select, #warning-form textarea').on('input change', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(function() {
            autoSaveForm();
        }, 30000); // Auto-save after 30 seconds of inactivity
    });
}

// Auto-save form
function autoSaveForm() {
    const formData = new FormData($('#warning-form')[0]);
    formData.append('auto_save', '1');

    $.ajax({
        url: $('#warning-form').attr('action'),
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            showAlert('info', 'تم حفظ المسودة تلقائياً', 2000);
        },
        error: function() {
            // Silent fail for auto-save
        }
    });
}

// Issue warning function
function issueWarning() {
    Swal.fire({
        title: 'إصدار الإنذار',
        text: 'هل أنت متأكد من إصدار هذا الإنذار؟ لن يمكن التراجع عن هذا الإجراء.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، أصدر الإنذار',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            $('#status').val('issued');
            $('#warning-form').submit();
        }
    });
}

// Remove document function
function removeDocument(documentId) {
    Swal.fire({
        title: 'حذف المستند',
        text: 'هل أنت متأكد من حذف هذا المستند؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `{{ route("employee-warnings.remove-document", ":id") }}`.replace(':id', documentId),
                method: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    location.reload();
                },
                error: function() {
                    showAlert('error', 'حدث خطأ أثناء حذف المستند');
                }
            });
        }
    });
}

// Alert function
function showAlert(type, message, duration = 5000) {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    const alertIcon = type === 'success' ? 'fa-check-circle' :
                     type === 'error' ? 'fa-exclamation-triangle' :
                     type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';

    // Remove existing alerts
    $('.alert-floating').remove();

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show alert-floating"
             style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="fas ${alertIcon}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;

    $('body').append(alertHtml);

    // Auto-remove after specified duration
    setTimeout(function() {
        $('.alert-floating').fadeOut(function() {
            $(this).remove();
        });
    }, duration);
}

// Keyboard shortcuts
$(document).on('keydown', function(e) {
    // Ctrl+S for save
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        $('#warning-form').submit();
    }

    // Escape for cancel
    if (e.key === 'Escape') {
        window.location.href = '{{ route("employee-warnings.show", $warning->id) }}';
    }
});

// File upload validation
$('#documents').on('change', function() {
    const files = this.files;
    const maxFiles = 5;
    const maxSize = 2 * 1024 * 1024; // 2MB
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/jpg', 'image/png'];

    if (files.length > maxFiles) {
        showAlert('error', `يمكن رفع ${maxFiles} ملفات كحد أقصى`);
        this.value = '';
        return;
    }

    for (let i = 0; i < files.length; i++) {
        const file = files[i];

        if (file.size > maxSize) {
            showAlert('error', `حجم الملف ${file.name} يتجاوز 2MB`);
            this.value = '';
            return;
        }

        if (!allowedTypes.includes(file.type)) {
            showAlert('error', `نوع الملف ${file.name} غير مدعوم`);
            this.value = '';
            return;
        }
    }
});
</script>
@endsection
