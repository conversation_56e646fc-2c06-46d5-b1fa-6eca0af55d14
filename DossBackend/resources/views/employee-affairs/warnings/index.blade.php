@extends('layouts.app')

@section('title', 'إدارة الإنذارات')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        إدارة الإنذارات
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item active">الإنذارات</li>
                        </ol>
                    </nav>
                </div>
                <div class="btn-group">
                    <a href="{{ route('employee-warnings.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إنذار جديد
                    </a>
                    <button type="button" class="btn btn-info" data-toggle="modal" data-target="#exportModal">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                    <button type="button" class="btn btn-secondary" data-toggle="modal" data-target="#reportsModal">
                        <i class="fas fa-chart-bar"></i>
                        التقارير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الإنذارات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-warnings">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                إنذارات نشطة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-warnings">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                إنذارات مُصعدة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="escalated-warnings">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-level-up-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                معدل الحل
                            </div>
                            <div class="row no-gutters align-items-center">
                                <div class="col-auto">
                                    <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800" id="resolution-rate">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="progress progress-sm mr-2">
                                        <div class="progress-bar bg-success" id="resolution-progress" role="progressbar" 
                                             style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter"></i>
                البحث والتصفية
            </h6>
        </div>
        <div class="card-body">
            <form id="filter-form">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="employee_filter">الموظف:</label>
                            <select class="form-control select2" id="employee_filter" name="employee_id">
                                <option value="">جميع الموظفين</option>
                                @foreach($employees as $employee)
                                    <option value="{{ $employee->id }}">
                                        {{ $employee->name }} - {{ $employee->employee_code }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="department_filter">القسم:</label>
                            <select class="form-control" id="department_filter" name="department_id">
                                <option value="">جميع الأقسام</option>
                                @foreach($departments as $department)
                                    <option value="{{ $department->id }}">{{ $department->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="warning_type_filter">نوع الإنذار:</label>
                            <select class="form-control" id="warning_type_filter" name="warning_type">
                                <option value="">جميع الأنواع</option>
                                <option value="attendance">حضور وانصراف</option>
                                <option value="performance">أداء وظيفي</option>
                                <option value="conduct">سلوك مهني</option>
                                <option value="policy_violation">مخالفة سياسة</option>
                                <option value="safety">أمان وسلامة</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="severity_filter">مستوى الخطورة:</label>
                            <select class="form-control" id="severity_filter" name="severity_level">
                                <option value="">جميع المستويات</option>
                                <option value="minor">بسيط</option>
                                <option value="moderate">متوسط</option>
                                <option value="major">كبير</option>
                                <option value="critical">حرج</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="status_filter">الحالة:</label>
                            <select class="form-control" id="status_filter" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="draft">مسودة</option>
                                <option value="issued">صادر</option>
                                <option value="acknowledged">مُقر به</option>
                                <option value="escalated">مُصعد</option>
                                <option value="resolved">محلول</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="escalation_filter">مستوى التصعيد:</label>
                            <select class="form-control" id="escalation_filter" name="escalation_level">
                                <option value="">جميع المستويات</option>
                                <option value="supervisor">المشرف المباشر</option>
                                <option value="department_head">رئيس القسم</option>
                                <option value="hr_manager">مدير الموارد البشرية</option>
                                <option value="general_manager">المدير العام</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_from">من تاريخ:</label>
                            <input type="date" class="form-control" id="date_from" name="date_from">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_to">إلى تاريخ:</label>
                            <input type="date" class="form-control" id="date_to" name="date_to">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="search_query">البحث في النص:</label>
                            <input type="text" class="form-control" id="search_query" name="search" 
                                   placeholder="ابحث في وصف المخالفة أو الملاحظات...">
                        </div>
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <div class="form-group w-100">
                            <button type="button" class="btn btn-primary" onclick="applyFilters()">
                                <i class="fas fa-search"></i>
                                بحث
                            </button>
                            <button type="button" class="btn btn-secondary ml-2" onclick="clearFilters()">
                                <i class="fas fa-times"></i>
                                مسح
                            </button>
                            <button type="button" class="btn btn-info ml-2" onclick="saveFilters()">
                                <i class="fas fa-save"></i>
                                حفظ المرشح
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Quick Actions Panel -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">
                <i class="fas fa-bolt"></i>
                إجراءات سريعة
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-12">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="bulkAction('issue')">
                            <i class="fas fa-paper-plane"></i>
                            إصدار المحدد
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="bulkAction('escalate')">
                            <i class="fas fa-level-up-alt"></i>
                            تصعيد المحدد
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="bulkAction('resolve')">
                            <i class="fas fa-check-double"></i>
                            حل المحدد
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="bulkExport()">
                            <i class="fas fa-download"></i>
                            تصدير المحدد
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="bulkPrint()">
                            <i class="fas fa-print"></i>
                            طباعة المحدد
                        </button>
                    </div>
                    <div class="float-right">
                        <span class="text-muted">المحدد: </span>
                        <span id="selected-count" class="badge badge-primary">0</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Warnings Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-table"></i>
                قائمة الإنذارات
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="warnings-table" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th width="30">
                                <input type="checkbox" id="select-all">
                            </th>
                            <th>رقم الإنذار</th>
                            <th>الموظف</th>
                            <th>القسم</th>
                            <th>نوع الإنذار</th>
                            <th>مستوى الخطورة</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنذار</th>
                            <th>المتابعة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded via DataTables -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-download"></i>
                    تصدير الإنذارات
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="export-form">
                    <div class="form-group">
                        <label for="export_format">تنسيق التصدير:</label>
                        <select class="form-control" id="export_format" name="format">
                            <option value="excel">Excel (.xlsx)</option>
                            <option value="csv">CSV</option>
                            <option value="pdf">PDF</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="export_columns">الأعمدة المطلوبة:</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="col_employee" name="columns[]" value="employee" checked>
                                    <label class="form-check-label" for="col_employee">الموظف</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="col_department" name="columns[]" value="department" checked>
                                    <label class="form-check-label" for="col_department">القسم</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="col_warning_type" name="columns[]" value="warning_type" checked>
                                    <label class="form-check-label" for="col_warning_type">نوع الإنذار</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="col_severity" name="columns[]" value="severity_level" checked>
                                    <label class="form-check-label" for="col_severity">مستوى الخطورة</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="col_status" name="columns[]" value="status" checked>
                                    <label class="form-check-label" for="col_status">الحالة</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="col_warning_date" name="columns[]" value="warning_date" checked>
                                    <label class="form-check-label" for="col_warning_date">تاريخ الإنذار</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="col_violation" name="columns[]" value="violation_description">
                                    <label class="form-check-label" for="col_violation">وصف المخالفة</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="col_escalation" name="columns[]" value="escalation_level">
                                    <label class="form-check-label" for="col_escalation">مستوى التصعيد</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="apply_current_filters" name="apply_filters" checked>
                            <label class="form-check-label" for="apply_current_filters">
                                تطبيق المرشحات الحالية
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="performExport()">
                    <i class="fas fa-download"></i>
                    تصدير
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Reports Modal -->
<div class="modal fade" id="reportsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-bar"></i>
                    تقارير الإنذارات
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0">تقرير شامل</h6>
                            </div>
                            <div class="card-body">
                                <p>تقرير شامل لجميع الإنذارات مع الإحصائيات والتحليلات</p>
                                <button type="button" class="btn btn-primary btn-sm" onclick="generateReport('comprehensive')">
                                    <i class="fas fa-file-alt"></i>
                                    إنشاء التقرير
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0">تقرير الأقسام</h6>
                            </div>
                            <div class="card-body">
                                <p>تقرير الإنذارات مجمعة حسب الأقسام</p>
                                <button type="button" class="btn btn-info btn-sm" onclick="generateReport('departments')">
                                    <i class="fas fa-building"></i>
                                    إنشاء التقرير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0">تقرير الموظفين</h6>
                            </div>
                            <div class="card-body">
                                <p>تقرير الموظفين الأكثر حصولاً على إنذارات</p>
                                <button type="button" class="btn btn-warning btn-sm" onclick="generateReport('employees')">
                                    <i class="fas fa-users"></i>
                                    إنشاء التقرير
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="m-0">تقرير الاتجاهات</h6>
                            </div>
                            <div class="card-body">
                                <p>تقرير اتجاهات الإنذارات عبر الزمن</p>
                                <button type="button" class="btn btn-success btn-sm" onclick="generateReport('trends')">
                                    <i class="fas fa-chart-line"></i>
                                    إنشاء التقرير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkActionTitle">
                    <i class="fas fa-tasks"></i>
                    إجراء جماعي
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="bulkActionContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmBulkAction">
                    تأكيد
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
/* Statistics Cards */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    background: linear-gradient(45deg, #f8f9fc, #eaecf4);
    border-bottom: 1px solid #e3e6f0;
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-draft {
    background: linear-gradient(45deg, #6c757d, #545b62);
    color: white;
}

.status-issued {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
}

.status-acknowledged {
    background: linear-gradient(45deg, #28a745, #1e7e34);
    color: white;
}

.status-escalated {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    color: #856404;
}

.status-resolved {
    background: linear-gradient(45deg, #17a2b8, #138496);
    color: white;
}

/* Warning Type Badges */
.warning-type-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.type-attendance { background: linear-gradient(45deg, #17a2b8, #138496); color: white; }
.type-performance { background: linear-gradient(45deg, #ffc107, #e0a800); color: #856404; }
.type-conduct { background: linear-gradient(45deg, #dc3545, #c82333); color: white; }
.type-policy_violation { background: linear-gradient(45deg, #fd7e14, #e55a00); color: white; }
.type-safety { background: linear-gradient(45deg, #6f42c1, #59359a); color: white; }
.type-other { background: linear-gradient(45deg, #6c757d, #545b62); color: white; }

/* Severity Indicators */
.severity-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.severity-minor { background-color: #28a745; }
.severity-moderate { background-color: #ffc107; }
.severity-major { background-color: #fd7e14; }
.severity-critical { background-color: #dc3545; }

/* Escalation Indicators */
.escalation-indicator {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.escalation-supervisor { background-color: #17a2b8; color: white; }
.escalation-department_head { background-color: #ffc107; color: #856404; }
.escalation-hr_manager { background-color: #fd7e14; color: white; }
.escalation-general_manager { background-color: #dc3545; color: white; }

/* Button Enhancements */
.btn {
    border-radius: 0.35rem;
    font-weight: 600;
    transition: all 0.15s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.btn-primary {
    background: linear-gradient(45deg, #4e73df, #224abe);
    border-color: #4e73df;
}

.btn-success {
    background: linear-gradient(45deg, #1cc88a, #17a673);
    border-color: #1cc88a;
}

.btn-warning {
    background: linear-gradient(45deg, #f6c23e, #dda20a);
    border-color: #f6c23e;
    color: #856404;
}

.btn-info {
    background: linear-gradient(45deg, #36b9cc, #2c9faf);
    border-color: #36b9cc;
}

.btn-danger {
    background: linear-gradient(45deg, #e74a3b, #c0392b);
    border-color: #e74a3b;
}

/* DataTables Customization */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin-bottom: 1rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.375rem 0.75rem;
    margin-left: 0.125rem;
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    background: #fff;
    color: #5a5c69;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: #eaecf4;
    border-color: #d1d3e2;
    color: #5a5c69;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #4e73df;
    border-color: #4e73df;
    color: white;
}

/* Table Enhancements */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background: linear-gradient(45deg, #f8f9fc, #eaecf4);
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(78, 115, 223, 0.05);
    transform: scale(1.01);
}

.table tbody td {
    vertical-align: middle;
    border-top: 1px solid #e3e6f0;
}

/* Employee Avatar */
.employee-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

/* Follow-up Indicators */
.followup-overdue {
    color: #e74a3b;
    font-weight: 600;
}

.followup-today {
    color: #f6c23e;
    font-weight: 600;
}

.followup-upcoming {
    color: #1cc88a;
    font-weight: 600;
}

/* Action Buttons */
.action-btn {
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.action-view {
    background: linear-gradient(45deg, #36b9cc, #2c9faf);
    color: white;
}

.action-edit {
    background: linear-gradient(45deg, #f6c23e, #dda20a);
    color: #856404;
}

.action-delete {
    background: linear-gradient(45deg, #e74a3b, #c0392b);
    color: white;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 0.5rem;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid #4e73df;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Statistics Animation */
.stat-number {
    transition: all 0.3s ease;
}

.stat-number.loading {
    opacity: 0.5;
}

.stat-number.updated {
    animation: bounce 0.6s ease;
}

@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    80% {
        transform: translateY(-5px);
    }
}

/* Progress Bar Enhancements */
.progress {
    height: 0.5rem;
    border-radius: 0.25rem;
    background-color: #eaecf4;
}

.progress-bar {
    border-radius: 0.25rem;
    transition: width 0.6s ease;
}

/* Filter Form Enhancements */
.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.select2-container--default .select2-selection--single {
    height: calc(1.5em + 0.75rem + 2px);
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
}

.select2-container--default .select2-selection--single:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Quick Actions Panel */
.quick-actions {
    background: linear-gradient(45deg, #f8f9fc, #eaecf4);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* Bulk Selection */
.bulk-selected {
    background-color: rgba(78, 115, 223, 0.1) !important;
}

.selected-count {
    background: linear-gradient(45deg, #4e73df, #224abe);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 600;
    animation: pulse 2s infinite;
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    background: linear-gradient(45deg, #f8f9fc, #eaecf4);
    border-bottom: 1px solid #e3e6f0;
    border-radius: 0.5rem 0.5rem 0 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .btn-group .btn {
        margin-bottom: 0.5rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .action-btn {
        padding: 0.125rem 0.25rem;
        font-size: 0.625rem;
    }

    .employee-avatar {
        width: 24px;
        height: 24px;
    }

    .status-badge,
    .warning-type-badge {
        font-size: 0.625rem;
        padding: 0.125rem 0.25rem;
    }
}

/* Print Styles */
@media print {
    .btn, .btn-group, .card-header, .breadcrumb, .modal {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        margin-bottom: 1rem !important;
    }

    .table {
        font-size: 0.75rem;
    }

    .status-badge,
    .warning-type-badge,
    .escalation-indicator {
        border: 1px solid #000 !important;
        background: white !important;
        color: #000 !important;
    }
}

/* Animation for new rows */
@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.table tbody tr.new-row {
    animation: slideInUp 0.5s ease-out;
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(45deg, #4e73df, #224abe);
    color: white;
    border: none;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
    font-size: 1.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
}

/* Tooltip Enhancements */
.tooltip-inner {
    background-color: #5a5c69;
    border-radius: 0.35rem;
    font-size: 0.75rem;
}

.tooltip.bs-tooltip-top .arrow::before {
    border-top-color: #5a5c69;
}

.tooltip.bs-tooltip-bottom .arrow::before {
    border-bottom-color: #5a5c69;
}

.tooltip.bs-tooltip-left .arrow::before {
    border-left-color: #5a5c69;
}

.tooltip.bs-tooltip-right .arrow::before {
    border-right-color: #5a5c69;
}
</style>
@endsection

@section('scripts')
<script>
let warningsTable;
let selectedRows = [];
let currentFilters = {};
let statisticsData = {};

$(document).ready(function() {
    // Initialize DataTable
    initializeDataTable();

    // Load statistics
    loadStatistics();

    // Initialize Select2
    $('.select2').select2({
        placeholder: 'اختر...',
        allowClear: true,
        language: 'ar'
    });

    // Initialize tooltips
    $('[title]').tooltip();

    // Load saved filters
    loadSavedFilters();

    // Auto-refresh statistics every 5 minutes
    setInterval(loadStatistics, 300000);

    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();

    // Initialize floating action button
    initializeFloatingActionButton();
});

// Initialize DataTable
function initializeDataTable() {
    warningsTable = $('#warnings-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("employee-warnings.data") }}',
            data: function(d) {
                // Add filter parameters
                d.employee_id = $('#employee_filter').val();
                d.department_id = $('#department_filter').val();
                d.warning_type = $('#warning_type_filter').val();
                d.severity_level = $('#severity_filter').val();
                d.status = $('#status_filter').val();
                d.escalation_level = $('#escalation_filter').val();
                d.date_from = $('#date_from').val();
                d.date_to = $('#date_to').val();
                d.search_query = $('#search_query').val();

                // Store current filters
                currentFilters = {
                    employee_id: d.employee_id,
                    department_id: d.department_id,
                    warning_type: d.warning_type,
                    severity_level: d.severity_level,
                    status: d.status,
                    escalation_level: d.escalation_level,
                    date_from: d.date_from,
                    date_to: d.date_to,
                    search_query: d.search_query
                };
            }
        },
        columns: [
            {
                data: 'id',
                name: 'id',
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    return `<input type="checkbox" class="row-checkbox" value="${data}">`;
                }
            },
            {
                data: 'id',
                name: 'id',
                render: function(data, type, row) {
                    return `#${data}`;
                }
            },
            {
                data: 'employee',
                name: 'employee.name',
                render: function(data, type, row) {
                    const avatar = data.avatar || '/images/default-avatar.png';
                    return `
                        <div class="d-flex align-items-center">
                            <img src="${avatar}" class="employee-avatar" alt="Avatar">
                            <div>
                                <div class="font-weight-bold">${data.name}</div>
                                <div class="text-muted small">${data.employee_code}</div>
                            </div>
                        </div>
                    `;
                }
            },
            {
                data: 'department',
                name: 'employee.department.name',
                render: function(data, type, row) {
                    return data ? data.name : '-';
                }
            },
            {
                data: 'warning_type',
                name: 'warning_type',
                render: function(data, type, row) {
                    const types = {
                        'attendance': 'حضور وانصراف',
                        'performance': 'أداء وظيفي',
                        'conduct': 'سلوك مهني',
                        'policy_violation': 'مخالفة سياسة',
                        'safety': 'أمان وسلامة',
                        'other': 'أخرى'
                    };
                    const typeText = types[data] || data;
                    return `<span class="warning-type-badge type-${data}">${typeText}</span>`;
                }
            },
            {
                data: 'severity_level',
                name: 'severity_level',
                render: function(data, type, row) {
                    const severities = {
                        'minor': 'بسيط',
                        'moderate': 'متوسط',
                        'major': 'كبير',
                        'critical': 'حرج'
                    };
                    const severityText = severities[data] || data;
                    return `
                        <div class="d-flex align-items-center">
                            <span class="severity-indicator severity-${data}"></span>
                            ${severityText}
                        </div>
                    `;
                }
            },
            {
                data: 'status',
                name: 'status',
                render: function(data, type, row) {
                    const statuses = {
                        'draft': 'مسودة',
                        'issued': 'صادر',
                        'acknowledged': 'مُقر به',
                        'escalated': 'مُصعد',
                        'resolved': 'محلول'
                    };
                    const statusText = statuses[data] || data;
                    return `<span class="status-badge status-${data}">${statusText}</span>`;
                }
            },
            {
                data: 'warning_date',
                name: 'warning_date',
                render: function(data, type, row) {
                    return moment(data).format('YYYY-MM-DD');
                }
            },
            {
                data: 'follow_up_date',
                name: 'follow_up_date',
                render: function(data, type, row) {
                    if (!data) return '-';

                    const followUpDate = moment(data);
                    const today = moment();
                    const diffDays = followUpDate.diff(today, 'days');

                    let className = 'followup-upcoming';
                    let icon = 'fa-clock';

                    if (diffDays < 0) {
                        className = 'followup-overdue';
                        icon = 'fa-exclamation-triangle';
                    } else if (diffDays === 0) {
                        className = 'followup-today';
                        icon = 'fa-bell';
                    }

                    return `
                        <div class="${className}">
                            <i class="fas ${icon}"></i>
                            ${followUpDate.format('YYYY-MM-DD')}
                            <br>
                            <small>(${followUpDate.fromNow()})</small>
                        </div>
                    `;
                }
            },
            {
                data: 'id',
                name: 'actions',
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    let actions = `
                        <button type="button" class="action-btn action-view"
                                onclick="viewWarning(${data})" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                    `;

                    if (row.status === 'draft') {
                        actions += `
                            <button type="button" class="action-btn action-edit"
                                    onclick="editWarning(${data})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="action-btn action-view"
                                    onclick="issueWarning(${data})" title="إصدار">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        `;
                    }

                    if (row.status === 'issued' && row.requires_acknowledgment && !row.acknowledged_at) {
                        actions += `
                            <button type="button" class="action-btn action-edit"
                                    onclick="acknowledgeWarning(${data})" title="إقرار">
                                <i class="fas fa-signature"></i>
                            </button>
                        `;
                    }

                    if (['issued', 'acknowledged'].includes(row.status)) {
                        actions += `
                            <button type="button" class="action-btn action-edit"
                                    onclick="escalateWarning(${data})" title="تصعيد">
                                <i class="fas fa-level-up-alt"></i>
                            </button>
                        `;
                    }

                    if (row.status !== 'resolved') {
                        actions += `
                            <button type="button" class="action-btn action-view"
                                    onclick="resolveWarning(${data})" title="حل">
                                <i class="fas fa-check-double"></i>
                            </button>
                        `;
                    }

                    actions += `
                        <button type="button" class="action-btn action-view"
                                onclick="printWarning(${data})" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button type="button" class="action-btn action-delete"
                                onclick="deleteWarning(${data})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    `;

                    return actions;
                }
            }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        order: [[1, 'desc']],
        pageLength: 25,
        responsive: true,
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        drawCallback: function(settings) {
            // Initialize tooltips for new elements
            $('[title]').tooltip();

            // Update selected count
            updateSelectedCount();

            // Add animation to new rows
            $('#warnings-table tbody tr').addClass('new-row');
            setTimeout(function() {
                $('#warnings-table tbody tr').removeClass('new-row');
            }, 500);
        }
    });

    // Handle select all checkbox
    $('#select-all').on('change', function() {
        const isChecked = $(this).is(':checked');
        $('.row-checkbox').prop('checked', isChecked);
        updateSelectedRows();
    });

    // Handle individual row checkboxes
    $(document).on('change', '.row-checkbox', function() {
        updateSelectedRows();

        // Update select all checkbox
        const totalCheckboxes = $('.row-checkbox').length;
        const checkedCheckboxes = $('.row-checkbox:checked').length;

        $('#select-all').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
        $('#select-all').prop('checked', checkedCheckboxes === totalCheckboxes);
    });
}

// Load statistics
function loadStatistics() {
    // Add loading animation
    $('.stat-number').addClass('loading').html('<i class="fas fa-spinner fa-spin"></i>');

    $.ajax({
        url: '{{ route("employee-warnings.statistics") }}',
        method: 'GET',
        data: currentFilters,
        success: function(response) {
            statisticsData = response;
            updateStatistics(response);
        },
        error: function() {
            $('.stat-number').removeClass('loading').html('<i class="fas fa-exclamation-triangle text-danger"></i>');
        }
    });
}

// Update statistics display
function updateStatistics(stats) {
    // Animate numbers
    animateNumber('#total-warnings', stats.total_warnings);
    animateNumber('#active-warnings', stats.active_warnings);
    animateNumber('#escalated-warnings', stats.escalated_warnings);

    // Update resolution rate
    const resolutionRate = stats.total_warnings > 0 ?
        Math.round((stats.resolved_warnings / stats.total_warnings) * 100) : 0;

    animateNumber('#resolution-rate', resolutionRate, '%');

    // Update progress bar
    $('#resolution-progress').css('width', resolutionRate + '%').attr('aria-valuenow', resolutionRate);

    // Remove loading class
    $('.stat-number').removeClass('loading').addClass('updated');
    setTimeout(function() {
        $('.stat-number').removeClass('updated');
    }, 600);
}

// Animate number counting
function animateNumber(selector, targetValue, suffix = '') {
    const element = $(selector);
    const startValue = 0;
    const duration = 1000;
    const startTime = Date.now();

    function updateNumber() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);

        element.text(currentValue + suffix);

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }

    updateNumber();
}

// Apply filters
function applyFilters() {
    warningsTable.ajax.reload();
    loadStatistics();
}

// Clear filters
function clearFilters() {
    $('#filter-form')[0].reset();
    $('.select2').val(null).trigger('change');
    applyFilters();
}

// Save filters
function saveFilters() {
    const filters = {
        employee_id: $('#employee_filter').val(),
        department_id: $('#department_filter').val(),
        warning_type: $('#warning_type_filter').val(),
        severity_level: $('#severity_filter').val(),
        status: $('#status_filter').val(),
        escalation_level: $('#escalation_filter').val(),
        date_from: $('#date_from').val(),
        date_to: $('#date_to').val(),
        search_query: $('#search_query').val()
    };

    localStorage.setItem('warning_filters', JSON.stringify(filters));

    showAlert('success', 'تم حفظ المرشحات بنجاح');
}

// Load saved filters
function loadSavedFilters() {
    const savedFilters = localStorage.getItem('warning_filters');
    if (savedFilters) {
        const filters = JSON.parse(savedFilters);

        Object.keys(filters).forEach(key => {
            const element = $(`#${key}_filter, #${key}`);
            if (element.length > 0) {
                if (element.hasClass('select2')) {
                    element.val(filters[key]).trigger('change');
                } else {
                    element.val(filters[key]);
                }
            }
        });
    }
}

// Update selected rows
function updateSelectedRows() {
    selectedRows = [];
    $('.row-checkbox:checked').each(function() {
        selectedRows.push($(this).val());
    });
    updateSelectedCount();
}

// Update selected count
function updateSelectedCount() {
    $('#selected-count').text(selectedRows.length);

    if (selectedRows.length > 0) {
        $('#selected-count').addClass('selected-count');
    } else {
        $('#selected-count').removeClass('selected-count');
    }
}

// View warning
function viewWarning(id) {
    window.location.href = `{{ route('employee-warnings.show', ':id') }}`.replace(':id', id);
}

// Edit warning
function editWarning(id) {
    window.location.href = `{{ route('employee-warnings.edit', ':id') }}`.replace(':id', id);
}

// Issue warning
function issueWarning(id) {
    Swal.fire({
        title: 'إصدار الإنذار',
        text: 'هل أنت متأكد من إصدار هذا الإنذار؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، أصدر الإنذار',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            performWarningAction(id, 'issue', 'إصدار الإنذار');
        }
    });
}

// Acknowledge warning
function acknowledgeWarning(id) {
    Swal.fire({
        title: 'إقرار الإنذار',
        html: `
            <div class="text-left">
                <label for="acknowledgment_notes">ملاحظات الموظف (اختيارية):</label>
                <textarea id="acknowledgment_notes" class="form-control" rows="3"
                          placeholder="أي ملاحظات أو تعليقات من الموظف..."></textarea>
            </div>
        `,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#17a2b8',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'تأكيد الإقرار',
        cancelButtonText: 'إلغاء',
        preConfirm: () => {
            return {
                notes: document.getElementById('acknowledgment_notes').value
            };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            performWarningAction(id, 'acknowledge', 'إقرار الإنذار', result.value);
        }
    });
}

// Escalate warning
function escalateWarning(id) {
    Swal.fire({
        title: 'تصعيد الإنذار',
        html: `
            <div class="text-left">
                <label for="escalation_level">مستوى التصعيد:</label>
                <select id="escalation_level" class="form-control">
                    <option value="supervisor">المشرف المباشر</option>
                    <option value="department_head">رئيس القسم</option>
                    <option value="hr_manager">مدير الموارد البشرية</option>
                    <option value="general_manager">المدير العام</option>
                </select>
                <br>
                <label for="escalation_reason">سبب التصعيد:</label>
                <textarea id="escalation_reason" class="form-control" rows="3"
                          placeholder="اكتب سبب التصعيد..." required></textarea>
            </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ffc107',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'تصعيد الإنذار',
        cancelButtonText: 'إلغاء',
        preConfirm: () => {
            const level = document.getElementById('escalation_level').value;
            const reason = document.getElementById('escalation_reason').value;

            if (!reason.trim()) {
                Swal.showValidationMessage('يرجى كتابة سبب التصعيد');
                return false;
            }

            return {
                level: level,
                reason: reason
            };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            performWarningAction(id, 'escalate', 'تصعيد الإنذار', result.value);
        }
    });
}

// Resolve warning
function resolveWarning(id) {
    Swal.fire({
        title: 'حل الإنذار',
        html: `
            <div class="text-left">
                <label for="resolution_notes">ملاحظات الحل:</label>
                <textarea id="resolution_notes" class="form-control" rows="3"
                          placeholder="اكتب تفاصيل حل الإنذار..." required></textarea>
            </div>
        `,
        icon: 'success',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'حل الإنذار',
        cancelButtonText: 'إلغاء',
        preConfirm: () => {
            const notes = document.getElementById('resolution_notes').value;

            if (!notes.trim()) {
                Swal.showValidationMessage('يرجى كتابة ملاحظات الحل');
                return false;
            }

            return {
                notes: notes
            };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            performWarningAction(id, 'resolve', 'حل الإنذار', result.value);
        }
    });
}

// Print warning
function printWarning(id) {
    window.open(`{{ route('employee-warnings.print', ':id') }}`.replace(':id', id), '_blank');
}

// Delete warning
function deleteWarning(id) {
    Swal.fire({
        title: 'حذف الإنذار',
        text: 'هل أنت متأكد من حذف هذا الإنذار؟ لا يمكن التراجع عن هذا الإجراء.',
        icon: 'error',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `{{ route('employee-warnings.destroy', ':id') }}`.replace(':id', id),
                method: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    Swal.fire({
                        title: 'تم الحذف!',
                        text: 'تم حذف الإنذار بنجاح',
                        icon: 'success',
                        confirmButtonColor: '#28a745',
                        confirmButtonText: 'موافق'
                    });

                    warningsTable.ajax.reload();
                    loadStatistics();
                },
                error: function(xhr) {
                    let errorMessage = 'حدث خطأ أثناء الحذف';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }

                    Swal.fire({
                        title: 'خطأ!',
                        text: errorMessage,
                        icon: 'error',
                        confirmButtonColor: '#dc3545',
                        confirmButtonText: 'موافق'
                    });
                }
            });
        }
    });
}

// Perform warning action
function performWarningAction(id, action, actionName, data = {}) {
    // Show loading
    Swal.fire({
        title: 'جاري المعالجة...',
        text: `جاري ${actionName}...`,
        icon: 'info',
        allowOutsideClick: false,
        showConfirmButton: false,
        willOpen: () => {
            Swal.showLoading();
        }
    });

    $.ajax({
        url: `{{ route("employee-warnings.action", ":id") }}`.replace(':id', id),
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            action: action,
            ...data
        },
        success: function(response) {
            Swal.fire({
                title: 'تم بنجاح!',
                text: `تم ${actionName} بنجاح`,
                icon: 'success',
                confirmButtonColor: '#28a745',
                confirmButtonText: 'موافق'
            });

            warningsTable.ajax.reload();
            loadStatistics();
        },
        error: function(xhr) {
            let errorMessage = 'حدث خطأ أثناء العملية';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            Swal.fire({
                title: 'خطأ!',
                text: errorMessage,
                icon: 'error',
                confirmButtonColor: '#dc3545',
                confirmButtonText: 'موافق'
            });
        }
    });
}

// Bulk actions
function bulkAction(action) {
    if (selectedRows.length === 0) {
        showAlert('warning', 'يرجى تحديد إنذار واحد على الأقل');
        return;
    }

    let title, text, confirmText, color;

    switch (action) {
        case 'issue':
            title = 'إصدار الإنذارات المحددة';
            text = `هل أنت متأكد من إصدار ${selectedRows.length} إنذار؟`;
            confirmText = 'نعم، أصدر الإنذارات';
            color = '#28a745';
            break;
        case 'escalate':
            title = 'تصعيد الإنذارات المحددة';
            text = `هل أنت متأكد من تصعيد ${selectedRows.length} إنذار؟`;
            confirmText = 'نعم، صعد الإنذارات';
            color = '#ffc107';
            break;
        case 'resolve':
            title = 'حل الإنذارات المحددة';
            text = `هل أنت متأكد من حل ${selectedRows.length} إنذار؟`;
            confirmText = 'نعم، احلل الإنذارات';
            color = '#17a2b8';
            break;
    }

    Swal.fire({
        title: title,
        text: text,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: color,
        cancelButtonColor: '#6c757d',
        confirmButtonText: confirmText,
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            performBulkAction(action);
        }
    });
}

// Perform bulk action
function performBulkAction(action) {
    // Show loading
    Swal.fire({
        title: 'جاري المعالجة...',
        text: 'جاري تنفيذ الإجراء الجماعي...',
        icon: 'info',
        allowOutsideClick: false,
        showConfirmButton: false,
        willOpen: () => {
            Swal.showLoading();
        }
    });

    $.ajax({
        url: '{{ route("employee-warnings.bulk-action") }}',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            action: action,
            ids: selectedRows
        },
        success: function(response) {
            Swal.fire({
                title: 'تم بنجاح!',
                text: `تم تنفيذ الإجراء على ${response.processed} إنذار`,
                icon: 'success',
                confirmButtonColor: '#28a745',
                confirmButtonText: 'موافق'
            });

            // Clear selection
            selectedRows = [];
            $('#select-all').prop('checked', false);
            $('.row-checkbox').prop('checked', false);
            updateSelectedCount();

            warningsTable.ajax.reload();
            loadStatistics();
        },
        error: function(xhr) {
            let errorMessage = 'حدث خطأ أثناء تنفيذ الإجراء الجماعي';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            Swal.fire({
                title: 'خطأ!',
                text: errorMessage,
                icon: 'error',
                confirmButtonColor: '#dc3545',
                confirmButtonText: 'موافق'
            });
        }
    });
}

// Bulk export
function bulkExport() {
    if (selectedRows.length === 0) {
        showAlert('warning', 'يرجى تحديد إنذار واحد على الأقل للتصدير');
        return;
    }

    // Set export modal for selected items
    $('#apply_current_filters').prop('checked', false);
    $('#exportModal').modal('show');
}

// Bulk print
function bulkPrint() {
    if (selectedRows.length === 0) {
        showAlert('warning', 'يرجى تحديد إنذار واحد على الأقل للطباعة');
        return;
    }

    const printUrl = '{{ route("employee-warnings.bulk-print") }}?' +
                    'ids=' + selectedRows.join(',');

    window.open(printUrl, '_blank');
}

// Export functionality
function performExport() {
    const format = $('#export_format').val();
    const columns = [];

    $('input[name="columns[]"]:checked').each(function() {
        columns.push($(this).val());
    });

    if (columns.length === 0) {
        showAlert('warning', 'يرجى تحديد عمود واحد على الأقل للتصدير');
        return;
    }

    let exportData = {
        format: format,
        columns: columns
    };

    // Add filters if checkbox is checked
    if ($('#apply_current_filters').is(':checked')) {
        exportData = { ...exportData, ...currentFilters };
    }

    // Add selected IDs if bulk export
    if (selectedRows.length > 0 && !$('#apply_current_filters').is(':checked')) {
        exportData.ids = selectedRows;
    }

    // Show loading
    const exportButton = $('#exportModal .btn-primary');
    const originalText = exportButton.html();
    exportButton.html('<i class="fas fa-spinner fa-spin"></i> جاري التصدير...').prop('disabled', true);

    $.ajax({
        url: '{{ route("employee-warnings.export") }}',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            ...exportData
        },
        xhrFields: {
            responseType: 'blob'
        },
        success: function(data, status, xhr) {
            // Get filename from response header
            const disposition = xhr.getResponseHeader('Content-Disposition');
            let filename = 'warnings_export.' + format;

            if (disposition && disposition.indexOf('filename=') !== -1) {
                filename = disposition.split('filename=')[1].replace(/"/g, '');
            }

            // Create download link
            const blob = new Blob([data]);
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            $('#exportModal').modal('hide');
            showAlert('success', 'تم تصدير البيانات بنجاح');
        },
        error: function(xhr) {
            let errorMessage = 'حدث خطأ أثناء التصدير';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            showAlert('error', errorMessage);
        },
        complete: function() {
            exportButton.html(originalText).prop('disabled', false);
        }
    });
}

// Generate reports
function generateReport(type) {
    // Show loading
    Swal.fire({
        title: 'جاري إنشاء التقرير...',
        text: 'يرجى الانتظار...',
        icon: 'info',
        allowOutsideClick: false,
        showConfirmButton: false,
        willOpen: () => {
            Swal.showLoading();
        }
    });

    $.ajax({
        url: '{{ route("employee-warnings.report") }}',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            type: type,
            ...currentFilters
        },
        xhrFields: {
            responseType: 'blob'
        },
        success: function(data, status, xhr) {
            // Get filename from response header
            const disposition = xhr.getResponseHeader('Content-Disposition');
            let filename = `warnings_report_${type}.pdf`;

            if (disposition && disposition.indexOf('filename=') !== -1) {
                filename = disposition.split('filename=')[1].replace(/"/g, '');
            }

            // Create download link
            const blob = new Blob([data], { type: 'application/pdf' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            $('#reportsModal').modal('hide');

            Swal.fire({
                title: 'تم بنجاح!',
                text: 'تم إنشاء التقرير وتحميله بنجاح',
                icon: 'success',
                confirmButtonColor: '#28a745',
                confirmButtonText: 'موافق'
            });
        },
        error: function(xhr) {
            let errorMessage = 'حدث خطأ أثناء إنشاء التقرير';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            Swal.fire({
                title: 'خطأ!',
                text: errorMessage,
                icon: 'error',
                confirmButtonColor: '#dc3545',
                confirmButtonText: 'موافق'
            });
        }
    });
}

// Initialize keyboard shortcuts
function initializeKeyboardShortcuts() {
    $(document).keydown(function(e) {
        // Ctrl+N: New warning
        if (e.ctrlKey && e.keyCode === 78) {
            e.preventDefault();
            window.location.href = '{{ route("employee-warnings.create") }}';
        }

        // Ctrl+F: Focus search
        if (e.ctrlKey && e.keyCode === 70) {
            e.preventDefault();
            $('#search_query').focus();
        }

        // Ctrl+R: Refresh table
        if (e.ctrlKey && e.keyCode === 82) {
            e.preventDefault();
            warningsTable.ajax.reload();
            loadStatistics();
        }

        // Ctrl+E: Export
        if (e.ctrlKey && e.keyCode === 69) {
            e.preventDefault();
            $('#exportModal').modal('show');
        }

        // Ctrl+A: Select all (in table context)
        if (e.ctrlKey && e.keyCode === 65 && $(e.target).closest('#warnings-table').length > 0) {
            e.preventDefault();
            $('#select-all').trigger('click');
        }

        // Delete: Delete selected
        if (e.keyCode === 46 && selectedRows.length > 0) {
            e.preventDefault();
            bulkAction('delete');
        }

        // Escape: Clear selection
        if (e.keyCode === 27) {
            selectedRows = [];
            $('#select-all').prop('checked', false);
            $('.row-checkbox').prop('checked', false);
            updateSelectedCount();
        }
    });
}

// Initialize floating action button
function initializeFloatingActionButton() {
    // Create floating action button
    const fab = $(`
        <button class="fab" title="إنذار جديد" onclick="window.location.href='{{ route('employee-warnings.create') }}'">
            <i class="fas fa-plus"></i>
        </button>
    `);

    $('body').append(fab);

    // Hide/show on scroll
    let lastScrollTop = 0;
    $(window).scroll(function() {
        const scrollTop = $(this).scrollTop();

        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // Scrolling down
            $('.fab').css('transform', 'translateY(100px)');
        } else {
            // Scrolling up
            $('.fab').css('transform', 'translateY(0)');
        }

        lastScrollTop = scrollTop;
    });
}

// Show alert
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'warning' ? 'alert-warning' :
                      type === 'error' ? 'alert-danger' : 'alert-info';

    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert"
             style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `);

    $('body').append(alert);

    // Auto dismiss after 5 seconds
    setTimeout(function() {
        alert.alert('close');
    }, 5000);
}

// Auto-save filter preferences
$(document).on('change', '#filter-form input, #filter-form select', function() {
    // Debounce the save operation
    clearTimeout(window.filterSaveTimeout);
    window.filterSaveTimeout = setTimeout(function() {
        const filters = {};
        $('#filter-form').find('input, select').each(function() {
            const name = $(this).attr('name');
            const value = $(this).val();
            if (name && value) {
                filters[name] = value;
            }
        });

        localStorage.setItem('warning_auto_filters', JSON.stringify(filters));
    }, 1000);
});

// Load auto-saved filters on page load
$(document).ready(function() {
    const autoFilters = localStorage.getItem('warning_auto_filters');
    if (autoFilters) {
        const filters = JSON.parse(autoFilters);

        Object.keys(filters).forEach(key => {
            const element = $(`[name="${key}"]`);
            if (element.length > 0) {
                if (element.hasClass('select2')) {
                    element.val(filters[key]).trigger('change');
                } else {
                    element.val(filters[key]);
                }
            }
        });
    }
});

// Print functionality
function printTable() {
    window.print();
}

// Real-time notifications (if WebSocket is available)
if (typeof io !== 'undefined') {
    const socket = io();

    socket.on('warning-updated', function(data) {
        // Refresh table if the updated warning is visible
        warningsTable.ajax.reload(null, false);
        loadStatistics();

        // Show notification
        showAlert('info', `تم تحديث الإنذار #${data.warning_id}`);
    });

    socket.on('warning-created', function(data) {
        // Refresh table and statistics
        warningsTable.ajax.reload(null, false);
        loadStatistics();

        // Show notification
        showAlert('success', `تم إنشاء إنذار جديد #${data.warning_id}`);
    });
}
</script>
@endsection
