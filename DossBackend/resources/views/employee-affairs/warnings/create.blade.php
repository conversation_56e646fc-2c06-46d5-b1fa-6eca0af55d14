@extends('layouts.app')

@section('title', 'إصدار إنذار')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-exclamation-triangle text-danger"></i>
                        إصدار إنذار تأديبي
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-warnings.index') }}">الإنذارات</a></li>
                            <li class="breadcrumb-item active">إنذار جديد</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('employee-warnings.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-plus"></i>
                        بيانات الإنذار التأديبي
                    </h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('employee-warnings.store') }}" method="POST" id="warning-form" enctype="multipart/form-data">
                        @csrf
                        
                        <!-- Employee Selection -->
                        <div class="form-group mb-3">
                            <label for="employee_id" class="form-label required">الموظف</label>
                            <select class="form-control select2" id="employee_id" name="employee_id" required>
                                <option value="">اختر الموظف</option>
                                @foreach($employees as $employee)
                                    <option value="{{ $employee->id }}" {{ old('employee_id') == $employee->id ? 'selected' : '' }}>
                                        {{ $employee->name }} - {{ $employee->employee_code }}
                                    </option>
                                @endforeach
                            </select>
                            @error('employee_id')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Warning Type -->
                        <div class="form-group mb-3">
                            <label for="type" class="form-label required">نوع الإنذار</label>
                            <select class="form-control" id="type" name="type" required>
                                <option value="">اختر النوع</option>
                                <option value="verbal" {{ old('type') == 'verbal' ? 'selected' : '' }}>إنذار شفهي</option>
                                <option value="written_first" {{ old('type') == 'written_first' ? 'selected' : '' }}>إنذار كتابي أول</option>
                                <option value="written_second" {{ old('type') == 'written_second' ? 'selected' : '' }}>إنذار كتابي ثاني</option>
                                <option value="final" {{ old('type') == 'final' ? 'selected' : '' }}>إنذار نهائي</option>
                                <option value="suspension" {{ old('type') == 'suspension' ? 'selected' : '' }}>إيقاف عن العمل</option>
                            </select>
                            @error('type')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Violation Category -->
                        <div class="form-group mb-3">
                            <label for="violation_category" class="form-label required">فئة المخالفة</label>
                            <select class="form-control" id="violation_category" name="violation_category" required>
                                <option value="">اختر الفئة</option>
                                <option value="attendance" {{ old('violation_category') == 'attendance' ? 'selected' : '' }}>الحضور والانصراف</option>
                                <option value="performance" {{ old('violation_category') == 'performance' ? 'selected' : '' }}>الأداء الوظيفي</option>
                                <option value="conduct" {{ old('violation_category') == 'conduct' ? 'selected' : '' }}>السلوك المهني</option>
                                <option value="safety" {{ old('violation_category') == 'safety' ? 'selected' : '' }}>السلامة والأمان</option>
                                <option value="policy" {{ old('violation_category') == 'policy' ? 'selected' : '' }}>مخالفة السياسات</option>
                                <option value="confidentiality" {{ old('violation_category') == 'confidentiality' ? 'selected' : '' }}>السرية</option>
                                <option value="harassment" {{ old('violation_category') == 'harassment' ? 'selected' : '' }}>التحرش</option>
                                <option value="insubordination" {{ old('violation_category') == 'insubordination' ? 'selected' : '' }}>عدم الطاعة</option>
                                <option value="theft" {{ old('violation_category') == 'theft' ? 'selected' : '' }}>السرقة</option>
                                <option value="other" {{ old('violation_category') == 'other' ? 'selected' : '' }}>أخرى</option>
                            </select>
                            @error('violation_category')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Violation Description -->
                        <div class="form-group mb-3">
                            <label for="violation_description" class="form-label required">وصف المخالفة</label>
                            <textarea class="form-control" id="violation_description" name="violation_description" rows="4" 
                                      placeholder="وصف تفصيلي للمخالفة المرتكبة" required>{{ old('violation_description') }}</textarea>
                            @error('violation_description')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Incident Date -->
                        <div class="form-group mb-3">
                            <label for="incident_date" class="form-label required">تاريخ المخالفة</label>
                            <input type="date" class="form-control" id="incident_date" name="incident_date" 
                                   value="{{ old('incident_date') }}" required>
                            @error('incident_date')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Warning Date -->
                        <div class="form-group mb-3">
                            <label for="warning_date" class="form-label required">تاريخ الإنذار</label>
                            <input type="date" class="form-control" id="warning_date" name="warning_date" 
                                   value="{{ old('warning_date', date('Y-m-d')) }}" required>
                            @error('warning_date')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Severity Level -->
                        <div class="form-group mb-3">
                            <label for="severity" class="form-label required">درجة الخطورة</label>
                            <select class="form-control" id="severity" name="severity" required>
                                <option value="">اختر الدرجة</option>
                                <option value="minor" {{ old('severity') == 'minor' ? 'selected' : '' }}>بسيطة</option>
                                <option value="moderate" {{ old('severity') == 'moderate' ? 'selected' : '' }}>متوسطة</option>
                                <option value="major" {{ old('severity') == 'major' ? 'selected' : '' }}>كبيرة</option>
                                <option value="critical" {{ old('severity') == 'critical' ? 'selected' : '' }}>حرجة</option>
                            </select>
                            @error('severity')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Witnesses -->
                        <div class="form-group mb-3">
                            <label for="witnesses" class="form-label">الشهود</label>
                            <textarea class="form-control" id="witnesses" name="witnesses" rows="2" 
                                      placeholder="أسماء الشهود على المخالفة">{{ old('witnesses') }}</textarea>
                            @error('witnesses')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Corrective Actions -->
                        <div class="form-group mb-3">
                            <label for="corrective_actions" class="form-label required">الإجراءات التصحيحية المطلوبة</label>
                            <textarea class="form-control" id="corrective_actions" name="corrective_actions" rows="3" 
                                      placeholder="الإجراءات التي يجب على الموظف اتخاذها لتصحيح السلوك" required>{{ old('corrective_actions') }}</textarea>
                            @error('corrective_actions')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Improvement Deadline -->
                        <div class="form-group mb-3">
                            <label for="improvement_deadline" class="form-label">موعد التحسن المطلوب</label>
                            <input type="date" class="form-control" id="improvement_deadline" name="improvement_deadline" 
                                   value="{{ old('improvement_deadline') }}">
                            @error('improvement_deadline')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Consequences -->
                        <div class="form-group mb-3">
                            <label for="consequences" class="form-label">العواقب في حالة التكرار</label>
                            <textarea class="form-control" id="consequences" name="consequences" rows="2" 
                                      placeholder="ما سيحدث في حالة تكرار المخالفة">{{ old('consequences') }}</textarea>
                            @error('consequences')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Suspension Details (if applicable) -->
                        <div class="form-group mb-3" id="suspension-details" style="display: none;">
                            <label for="suspension_days" class="form-label">عدد أيام الإيقاف</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="suspension_days" name="suspension_days" 
                                       value="{{ old('suspension_days') }}" min="1" max="30">
                                <div class="input-group-append">
                                    <span class="input-group-text">يوم</span>
                                </div>
                            </div>
                            @error('suspension_days')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Manager/Issuer -->
                        <div class="form-group mb-3">
                            <label for="issued_by" class="form-label required">صادر من</label>
                            <select class="form-control" id="issued_by" name="issued_by" required>
                                <option value="">اختر المدير</option>
                                @foreach($managers as $manager)
                                    <option value="{{ $manager->id }}" {{ old('issued_by') == $manager->id ? 'selected' : '' }}>
                                        {{ $manager->name }} - {{ $manager->position }}
                                    </option>
                                @endforeach
                            </select>
                            @error('issued_by')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Evidence Attachments -->
                        <div class="form-group mb-3">
                            <label for="evidence" class="form-label">الأدلة والمرفقات</label>
                            <input type="file" class="form-control-file" id="evidence" name="evidence[]" multiple 
                                   accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.mp4">
                            <small class="form-text text-muted">
                                يمكنك إرفاق أدلة داعمة للإنذار (مستندات، صور، فيديوهات)
                            </small>
                            @error('evidence')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Notes -->
                        <div class="form-group mb-3">
                            <label for="notes" class="form-label">ملاحظات إضافية</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2" 
                                      placeholder="أي ملاحظات أو معلومات إضافية">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Submit Buttons -->
                        <div class="form-group">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-save"></i>
                                إصدار الإنذار
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                            <a href="{{ route('employee-warnings.index') }}" class="btn btn-light">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Info Panel -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i>
                        سياسة الإنذارات التأديبية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-list-ol"></i> تدرج الإنذارات:</h6>
                        <ol class="mb-0">
                            <li><strong>إنذار شفهي</strong> - للمخالفات البسيطة</li>
                            <li><strong>إنذار كتابي أول</strong> - للمخالفات المتوسطة</li>
                            <li><strong>إنذار كتابي ثاني</strong> - للمخالفات المتكررة</li>
                            <li><strong>إنذار نهائي</strong> - قبل الفصل</li>
                            <li><strong>إيقاف عن العمل</strong> - للمخالفات الجسيمة</li>
                        </ol>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-clock"></i> مدة صلاحية الإنذارات:</h6>
                        <ul class="mb-0">
                            <li><strong>شفهي:</strong> 3 أشهر</li>
                            <li><strong>كتابي أول:</strong> 6 أشهر</li>
                            <li><strong>كتابي ثاني:</strong> 12 شهر</li>
                            <li><strong>نهائي:</strong> 24 شهر</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Employee Warning History -->
            <div class="card shadow mt-3" id="warning-history" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-history"></i>
                        تاريخ الإنذارات
                    </h6>
                </div>
                <div class="card-body" id="warning-history-details">
                    <!-- Warning history will be loaded here -->
                </div>
            </div>

            <!-- Employee Current Info -->
            <div class="card shadow mt-3" id="employee-current-info" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user"></i>
                        معلومات الموظف
                    </h6>
                </div>
                <div class="card-body" id="employee-current-details">
                    <!-- Current employee details will be loaded here -->
                </div>
            </div>

            <!-- Warning Guidelines -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        إرشادات مهمة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-gavel"></i> قبل إصدار الإنذار:</h6>
                        <ul class="mb-0">
                            <li>تأكد من صحة المعلومات</li>
                            <li>اجمع الأدلة الكافية</li>
                            <li>استمع لوجهة نظر الموظف</li>
                            <li>راجع السياسات المعمول بها</li>
                        </ul>
                    </div>

                    <div class="alert alert-success">
                        <h6><i class="fas fa-handshake"></i> الهدف من الإنذار:</h6>
                        <ul class="mb-0">
                            <li>تصحيح السلوك</li>
                            <li>تحسين الأداء</li>
                            <li>منع تكرار المخالفة</li>
                            <li>الحفاظ على بيئة العمل</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        placeholder: 'اختر الموظف',
        allowClear: true
    });

    // Handle employee selection
    $('#employee_id').change(function() {
        const employeeId = $(this).val();
        if (employeeId) {
            loadEmployeeInfo(employeeId);
            loadWarningHistory(employeeId);
        } else {
            $('#employee-current-info').hide();
            $('#warning-history').hide();
        }
    });

    // Handle warning type change
    $('#type').change(function() {
        const type = $(this).val();
        if (type === 'suspension') {
            $('#suspension-details').show();
            $('#suspension_days').prop('required', true);
        } else {
            $('#suspension-details').hide();
            $('#suspension_days').prop('required', false);
        }
    });

    // Set minimum date for improvement deadline
    $('#warning_date').change(function() {
        const warningDate = $(this).val();
        if (warningDate) {
            $('#improvement_deadline').attr('min', warningDate);
        }
    });

    // Form validation
    $('#warning-form').on('submit', function(e) {
        const violationDescription = $('#violation_description').val().trim();
        const correctiveActions = $('#corrective_actions').val().trim();
        const incidentDate = new Date($('#incident_date').val());
        const warningDate = new Date($('#warning_date').val());
        
        if (!violationDescription || violationDescription.length < 20) {
            e.preventDefault();
            alert('يجب كتابة وصف تفصيلي للمخالفة (20 حرف على الأقل)');
            return false;
        }
        
        if (!correctiveActions || correctiveActions.length < 10) {
            e.preventDefault();
            alert('يجب تحديد الإجراءات التصحيحية المطلوبة');
            return false;
        }
        
        if (warningDate < incidentDate) {
            e.preventDefault();
            alert('تاريخ الإنذار لا يمكن أن يكون قبل تاريخ المخالفة');
            return false;
        }
        
        // Confirm submission
        if (!confirm('هل أنت متأكد من إصدار هذا الإنذار التأديبي؟ هذا الإجراء سيؤثر على سجل الموظف.')) {
            e.preventDefault();
            return false;
        }
    });
});

function loadEmployeeInfo(employeeId) {
    $('#employee-current-details').html(`
        <div class="text-center">
            <div class="spinner-border spinner-border-sm" role="status">
                <span class="sr-only">جاري التحميل...</span>
            </div>
        </div>
    `);
    
    $('#employee-current-info').show();
    
    $.get(`/employees/${employeeId}/info`)
        .done(function(response) {
            if (response.success) {
                const employee = response.data;
                $('#employee-current-details').html(`
                    <p><strong>الاسم:</strong> ${employee.name}</p>
                    <p><strong>الكود:</strong> ${employee.employee_code}</p>
                    <p><strong>القسم:</strong> ${employee.department}</p>
                    <p><strong>المنصب:</strong> ${employee.position}</p>
                    <p><strong>المدير المباشر:</strong> ${employee.manager || 'غير محدد'}</p>
                    <p><strong>تاريخ التعيين:</strong> ${employee.hire_date}</p>
                `);
            }
        })
        .fail(function() {
            $('#employee-current-details').html(`
                <div class="alert alert-danger">
                    حدث خطأ في تحميل بيانات الموظف
                </div>
            `);
        });
}

function loadWarningHistory(employeeId) {
    $('#warning-history-details').html(`
        <div class="text-center">
            <div class="spinner-border spinner-border-sm" role="status">
                <span class="sr-only">جاري التحميل...</span>
            </div>
        </div>
    `);
    
    $('#warning-history').show();
    
    $.get(`/employees/${employeeId}/warnings`)
        .done(function(response) {
            if (response.success && response.data.length > 0) {
                let historyHtml = '<div class="timeline">';
                response.data.forEach(function(warning) {
                    const badgeClass = getWarningBadgeClass(warning.type);
                    historyHtml += `
                        <div class="timeline-item">
                            <span class="badge ${badgeClass}">${getWarningTypeText(warning.type)}</span>
                            <p><strong>${warning.violation_category}</strong></p>
                            <small class="text-muted">${warning.warning_date}</small>
                        </div>
                    `;
                });
                historyHtml += '</div>';
                
                $('#warning-history-details').html(historyHtml);
            } else {
                $('#warning-history-details').html(`
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        لا توجد إنذارات سابقة
                    </div>
                `);
            }
        })
        .fail(function() {
            $('#warning-history-details').html(`
                <div class="alert alert-danger">
                    حدث خطأ في تحميل تاريخ الإنذارات
                </div>
            `);
        });
}

function getWarningBadgeClass(type) {
    const classes = {
        'verbal': 'badge-info',
        'written_first': 'badge-warning',
        'written_second': 'badge-warning',
        'final': 'badge-danger',
        'suspension': 'badge-dark'
    };
    return classes[type] || 'badge-secondary';
}

function getWarningTypeText(type) {
    const texts = {
        'verbal': 'شفهي',
        'written_first': 'كتابي أول',
        'written_second': 'كتابي ثاني',
        'final': 'نهائي',
        'suspension': 'إيقاف'
    };
    return texts[type] || type;
}

function resetForm() {
    $('#warning-form')[0].reset();
    $('#employee-current-info').hide();
    $('#warning-history').hide();
    $('#suspension-details').hide();
    $('#suspension_days').prop('required', false);
    $('.select2').val(null).trigger('change');
}
</script>
@endsection
