@extends('layouts.app')

@section('title', 'تعديل الاقتراح')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-lightbulb text-warning"></i>
                        تعديل الاقتراح #{{ $suggestion->id }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-suggestions.index') }}">الاقتراحات</a></li>
                            <li class="breadcrumb-item active">تعديل الاقتراح</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('employee-suggestions.show', $suggestion->id) }}" class="btn btn-info btn-sm">
                        <i class="fas fa-eye"></i>
                        عرض التفاصيل
                    </a>
                    <a href="{{ route('employee-suggestions.index') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-right"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Warning -->
    @if($suggestion->status !== 'pending')
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                @if($suggestion->status === 'under_review')
                    هذا الاقتراح قيد المراجعة حالياً. يمكن تعديل بعض الحقول فقط.
                @elseif($suggestion->status === 'approved')
                    هذا الاقتراح تم اعتماده. لا يمكن تعديل المحتوى الأساسي.
                @elseif($suggestion->status === 'rejected')
                    هذا الاقتراح تم رفضه. يمكنك إنشاء اقتراح جديد بدلاً من ذلك.
                @elseif($suggestion->status === 'implemented')
                    هذا الاقتراح تم تنفيذه بالكامل ولا يمكن تعديله.
                @endif
            </div>
        </div>
    </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <!-- Main Form -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit"></i>
                        تعديل بيانات الاقتراح
                    </h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('employee-suggestions.update', $suggestion->id) }}" method="POST" id="suggestion-form">
                        @csrf
                        @method('PUT')
                        
                        <!-- Employee Info (Read-only) -->
                        <div class="form-group">
                            <label>معلومات مقدم الاقتراح</label>
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <div class="d-flex align-items-center">
                                        <img src="{{ $suggestion->employee->avatar ?? '/images/default-avatar.png' }}" 
                                             class="rounded-circle mr-3" width="40" height="40" alt="صورة الموظف">
                                        <div>
                                            <strong>{{ $suggestion->employee->name }}</strong><br>
                                            <small class="text-muted">{{ $suggestion->employee->employee_code }} - {{ $suggestion->employee->department->name ?? 'غير محدد' }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Title -->
                        <div class="form-group">
                            <label for="title">عنوان الاقتراح <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title', $suggestion->title) }}" 
                                   {{ in_array($suggestion->status, ['approved', 'implemented']) ? 'readonly' : '' }}
                                   placeholder="عنوان مختصر وواضح للاقتراح" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Category -->
                        <div class="form-group">
                            <label for="category">فئة الاقتراح <span class="text-danger">*</span></label>
                            <select class="form-control @error('category') is-invalid @enderror" 
                                    id="category" name="category" 
                                    {{ in_array($suggestion->status, ['approved', 'implemented']) ? 'disabled' : '' }}
                                    required>
                                <option value="">اختر فئة الاقتراح</option>
                                <option value="process_improvement" {{ old('category', $suggestion->category) == 'process_improvement' ? 'selected' : '' }}>تحسين العمليات</option>
                                <option value="cost_reduction" {{ old('category', $suggestion->category) == 'cost_reduction' ? 'selected' : '' }}>تقليل التكاليف</option>
                                <option value="quality_improvement" {{ old('category', $suggestion->category) == 'quality_improvement' ? 'selected' : '' }}>تحسين الجودة</option>
                                <option value="customer_service" {{ old('category', $suggestion->category) == 'customer_service' ? 'selected' : '' }}>خدمة العملاء</option>
                                <option value="safety" {{ old('category', $suggestion->category) == 'safety' ? 'selected' : '' }}>السلامة والأمان</option>
                                <option value="technology" {{ old('category', $suggestion->category) == 'technology' ? 'selected' : '' }}>التكنولوجيا</option>
                                <option value="environment" {{ old('category', $suggestion->category) == 'environment' ? 'selected' : '' }}>البيئة</option>
                                <option value="other" {{ old('category', $suggestion->category) == 'other' ? 'selected' : '' }}>أخرى</option>
                            </select>
                            @error('category')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Priority -->
                        <div class="form-group">
                            <label for="priority">أولوية التنفيذ <span class="text-danger">*</span></label>
                            <select class="form-control @error('priority') is-invalid @enderror" 
                                    id="priority" name="priority" required>
                                <option value="">اختر أولوية التنفيذ</option>
                                <option value="low" {{ old('priority', $suggestion->priority) == 'low' ? 'selected' : '' }}>منخفضة</option>
                                <option value="medium" {{ old('priority', $suggestion->priority) == 'medium' ? 'selected' : '' }}>متوسطة</option>
                                <option value="high" {{ old('priority', $suggestion->priority) == 'high' ? 'selected' : '' }}>عالية</option>
                                <option value="urgent" {{ old('priority', $suggestion->priority) == 'urgent' ? 'selected' : '' }}>عاجلة</option>
                            </select>
                            @error('priority')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="form-group">
                            <label for="description">وصف الاقتراح <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="5" 
                                      {{ in_array($suggestion->status, ['approved', 'implemented']) ? 'readonly' : '' }}
                                      placeholder="وصف تفصيلي للاقتراح والمشكلة التي يحلها" required>{{ old('description', $suggestion->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Current Situation -->
                        <div class="form-group">
                            <label for="current_situation">الوضع الحالي</label>
                            <textarea class="form-control @error('current_situation') is-invalid @enderror" 
                                      id="current_situation" name="current_situation" rows="3" 
                                      {{ in_array($suggestion->status, ['approved', 'implemented']) ? 'readonly' : '' }}
                                      placeholder="وصف الوضع الحالي والمشاكل الموجودة">{{ old('current_situation', $suggestion->current_situation) }}</textarea>
                            @error('current_situation')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Proposed Solution -->
                        <div class="form-group">
                            <label for="proposed_solution">الحل المقترح <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('proposed_solution') is-invalid @enderror" 
                                      id="proposed_solution" name="proposed_solution" rows="4" 
                                      {{ in_array($suggestion->status, ['approved', 'implemented']) ? 'readonly' : '' }}
                                      placeholder="تفاصيل الحل المقترح وكيفية تنفيذه" required>{{ old('proposed_solution', $suggestion->proposed_solution) }}</textarea>
                            @error('proposed_solution')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Expected Benefits -->
                        <div class="form-group">
                            <label for="expected_benefits">الفوائد المتوقعة</label>
                            <textarea class="form-control @error('expected_benefits') is-invalid @enderror" 
                                      id="expected_benefits" name="expected_benefits" rows="3" 
                                      placeholder="الفوائد والتحسينات المتوقعة من تطبيق الاقتراح">{{ old('expected_benefits', $suggestion->expected_benefits) }}</textarea>
                            @error('expected_benefits')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Implementation Timeline -->
                        <div class="form-group">
                            <label for="implementation_timeline">الجدول الزمني للتنفيذ</label>
                            <select class="form-control @error('implementation_timeline') is-invalid @enderror" 
                                    id="implementation_timeline" name="implementation_timeline">
                                <option value="">اختر الجدول الزمني المتوقع</option>
                                <option value="immediate" {{ old('implementation_timeline', $suggestion->implementation_timeline) == 'immediate' ? 'selected' : '' }}>فوري (أقل من أسبوع)</option>
                                <option value="short_term" {{ old('implementation_timeline', $suggestion->implementation_timeline) == 'short_term' ? 'selected' : '' }}>قصير المدى (1-4 أسابيع)</option>
                                <option value="medium_term" {{ old('implementation_timeline', $suggestion->implementation_timeline) == 'medium_term' ? 'selected' : '' }}>متوسط المدى (1-3 أشهر)</option>
                                <option value="long_term" {{ old('implementation_timeline', $suggestion->implementation_timeline) == 'long_term' ? 'selected' : '' }}>طويل المدى (أكثر من 3 أشهر)</option>
                            </select>
                            @error('implementation_timeline')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- ROI Calculation -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="estimated_cost">التكلفة المقدرة (ج.م)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control @error('estimated_cost') is-invalid @enderror" 
                                               id="estimated_cost" name="estimated_cost" step="0.01" min="0" 
                                               value="{{ old('estimated_cost', $suggestion->estimated_cost) }}" 
                                               placeholder="0.00" onchange="calculateROI()">
                                        <div class="input-group-append">
                                            <span class="input-group-text">ج.م</span>
                                        </div>
                                    </div>
                                    @error('estimated_cost')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="estimated_savings">الوفورات المقدرة سنوياً (ج.م)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control @error('estimated_savings') is-invalid @enderror" 
                                               id="estimated_savings" name="estimated_savings" step="0.01" min="0" 
                                               value="{{ old('estimated_savings', $suggestion->estimated_savings) }}" 
                                               placeholder="0.00" onchange="calculateROI()">
                                        <div class="input-group-append">
                                            <span class="input-group-text">ج.م</span>
                                        </div>
                                    </div>
                                    @error('estimated_savings')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- ROI Display -->
                        <div class="form-group">
                            <label>العائد على الاستثمار المتوقع</label>
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <div class="row text-center">
                                        <div class="col-md-4">
                                            <h6 class="text-primary">العائد السنوي</h6>
                                            <span id="annual-roi" class="h5 text-success">0%</span>
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="text-info">فترة الاسترداد</h6>
                                            <span id="payback-period" class="h5 text-warning">0 شهر</span>
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="text-success">صافي الوفورات</h6>
                                            <span id="net-savings" class="h5 text-primary">0 ج.م</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Notes -->
                        <div class="form-group">
                            <label for="additional_notes">ملاحظات إضافية</label>
                            <textarea class="form-control @error('additional_notes') is-invalid @enderror" 
                                      id="additional_notes" name="additional_notes" rows="3" 
                                      placeholder="أي ملاحظات أو معلومات إضافية">{{ old('additional_notes', $suggestion->additional_notes) }}</textarea>
                            @error('additional_notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Form Actions -->
                        <div class="form-group">
                            @if(!in_array($suggestion->status, ['approved', 'implemented']))
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    حفظ التعديلات
                                </button>
                            @endif
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                            <a href="{{ route('employee-suggestions.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Current Status -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i>
                        حالة الاقتراح
                    </h6>
                </div>
                <div class="card-body">
                    <p><strong>الحالة الحالية:</strong> 
                        @switch($suggestion->status)
                            @case('pending')
                                <span class="badge badge-warning">في الانتظار</span>
                                @break
                            @case('under_review')
                                <span class="badge badge-info">قيد المراجعة</span>
                                @break
                            @case('approved')
                                <span class="badge badge-success">معتمد</span>
                                @break
                            @case('rejected')
                                <span class="badge badge-danger">مرفوض</span>
                                @break
                            @case('implemented')
                                <span class="badge badge-primary">منفذ</span>
                                @break
                        @endswitch
                    </p>
                    <p><strong>تاريخ الإنشاء:</strong> {{ $suggestion->created_at->format('Y-m-d H:i') }}</p>
                    @if($suggestion->reviewed_at)
                        <p><strong>تاريخ المراجعة:</strong> {{ $suggestion->reviewed_at->format('Y-m-d H:i') }}</p>
                    @endif
                    @if($suggestion->implemented_at)
                        <p><strong>تاريخ التنفيذ:</strong> {{ $suggestion->implemented_at->format('Y-m-d H:i') }}</p>
                    @endif
                </div>
            </div>

            <!-- Edit Guidelines -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        إرشادات التعديل
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            يمكن تعديل الأولوية والملاحظات في أي وقت
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            يمكن تحديث التكاليف والوفورات المقدرة
                        </li>
                        @if($suggestion->status === 'pending')
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i>
                                يمكن تعديل جميع الحقول للاقتراحات المعلقة
                            </li>
                        @endif
                        @if(in_array($suggestion->status, ['approved', 'implemented']))
                            <li class="mb-2">
                                <i class="fas fa-times text-danger"></i>
                                لا يمكن تعديل المحتوى الأساسي للاقتراحات المعتمدة
                            </li>
                        @endif
                    </ul>
                </div>
            </div>

            <!-- ROI Calculator -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-calculator"></i>
                        حاسبة العائد على الاستثمار
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <h6 class="text-muted">العائد المتوقع</h6>
                            <h4 id="roi-percentage" class="text-success">0%</h4>
                        </div>
                        <div class="mb-3">
                            <h6 class="text-muted">فترة الاسترداد</h6>
                            <h5 id="payback-months" class="text-warning">0 شهر</h5>
                        </div>
                        <div>
                            <h6 class="text-muted">الوفورات الصافية (سنوياً)</h6>
                            <h5 id="net-annual-savings" class="text-primary">0 ج.م</h5>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Calculate ROI on page load
    calculateROI();
});

function calculateROI() {
    const cost = parseFloat($('#estimated_cost').val()) || 0;
    const savings = parseFloat($('#estimated_savings').val()) || 0;

    // Calculate ROI percentage
    let roiPercentage = 0;
    if (cost > 0) {
        roiPercentage = ((savings - cost) / cost) * 100;
    }

    // Calculate payback period in months
    let paybackMonths = 0;
    if (savings > 0) {
        paybackMonths = (cost / (savings / 12));
    }

    // Calculate net savings
    const netSavings = savings - cost;

    // Update main ROI display
    $('#annual-roi').text(roiPercentage.toFixed(1) + '%');
    $('#payback-period').text(paybackMonths.toFixed(1) + ' شهر');
    $('#net-savings').text(netSavings.toLocaleString('ar-EG') + ' ج.م');

    // Update side panel calculator
    $('#roi-percentage').text(roiPercentage.toFixed(1) + '%');
    $('#payback-months').text(paybackMonths.toFixed(1) + ' شهر');
    $('#net-annual-savings').text(netSavings.toLocaleString('ar-EG') + ' ج.م');

    // Color coding for ROI
    const roiElement = $('#roi-percentage');
    const annualRoiElement = $('#annual-roi');

    if (roiPercentage >= 100) {
        roiElement.removeClass().addClass('text-success');
        annualRoiElement.removeClass().addClass('text-success');
    } else if (roiPercentage >= 50) {
        roiElement.removeClass().addClass('text-info');
        annualRoiElement.removeClass().addClass('text-info');
    } else if (roiPercentage >= 0) {
        roiElement.removeClass().addClass('text-warning');
        annualRoiElement.removeClass().addClass('text-warning');
    } else {
        roiElement.removeClass().addClass('text-danger');
        annualRoiElement.removeClass().addClass('text-danger');
    }

    // Color coding for payback period
    const paybackElement = $('#payback-months');
    const paybackPeriodElement = $('#payback-period');

    if (paybackMonths <= 6) {
        paybackElement.removeClass().addClass('text-success');
        paybackPeriodElement.removeClass().addClass('text-success');
    } else if (paybackMonths <= 12) {
        paybackElement.removeClass().addClass('text-warning');
        paybackPeriodElement.removeClass().addClass('text-warning');
    } else {
        paybackElement.removeClass().addClass('text-danger');
        paybackPeriodElement.removeClass().addClass('text-danger');
    }
}

function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع التغييرات غير المحفوظة.')) {
        document.getElementById('suggestion-form').reset();
        calculateROI();
    }
}

// Form validation
$('#suggestion-form').on('submit', function(e) {
    let isValid = true;
    let errorMessage = '';

    // Check required fields
    const requiredFields = ['title', 'category', 'priority', 'description', 'proposed_solution'];
    requiredFields.forEach(function(field) {
        const value = $('#' + field).val().trim();
        if (!value) {
            isValid = false;
            $('#' + field).addClass('is-invalid');
        } else {
            $('#' + field).removeClass('is-invalid');
        }
    });

    // Validate cost and savings
    const cost = parseFloat($('#estimated_cost').val()) || 0;
    const savings = parseFloat($('#estimated_savings').val()) || 0;

    if (cost < 0) {
        isValid = false;
        errorMessage += 'التكلفة المقدرة يجب أن تكون أكبر من أو تساوي صفر.\n';
        $('#estimated_cost').addClass('is-invalid');
    }

    if (savings < 0) {
        isValid = false;
        errorMessage += 'الوفورات المقدرة يجب أن تكون أكبر من أو تساوي صفر.\n';
        $('#estimated_savings').addClass('is-invalid');
    }

    if (!isValid) {
        e.preventDefault();
        if (errorMessage) {
            alert(errorMessage);
        } else {
            alert('يرجى ملء جميع الحقول المطلوبة بشكل صحيح.');
        }
        return false;
    }

    return true;
});

// Real-time validation
$('#title, #description, #proposed_solution').on('input', function() {
    if ($(this).val().trim()) {
        $(this).removeClass('is-invalid');
    }
});

$('#category, #priority').on('change', function() {
    if ($(this).val()) {
        $(this).removeClass('is-invalid');
    }
});

$('#estimated_cost, #estimated_savings').on('input', function() {
    const value = parseFloat($(this).val()) || 0;
    if (value >= 0) {
        $(this).removeClass('is-invalid');
    }
});
</script>
@endsection
