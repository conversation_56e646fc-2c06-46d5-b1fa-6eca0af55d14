@extends('layouts.app')

@section('title', 'تفاصيل الاقتراح')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-lightbulb text-warning"></i>
                        تفاصيل الاقتراح #{{ $suggestion->id }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-suggestions.index') }}">الاقتراحات</a></li>
                            <li class="breadcrumb-item active">تفاصيل الاقتراح</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    @if($suggestion->status === 'pending')
                        <a href="{{ route('employee-suggestions.edit', $suggestion->id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </a>
                    @endif
                    <button type="button" class="btn btn-info btn-sm" onclick="window.print()">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                    <a href="{{ route('employee-suggestions.index') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-right"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Actions -->
    @if($suggestion->status === 'pending')
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning shadow">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <button type="button" class="btn btn-info btn-block" data-toggle="modal" data-target="#reviewModal">
                                <i class="fas fa-search"></i>
                                بدء المراجعة
                            </button>
                        </div>
                        <div class="col-md-4">
                            <button type="button" class="btn btn-success btn-block" data-toggle="modal" data-target="#approveModal">
                                <i class="fas fa-check"></i>
                                اعتماد الاقتراح
                            </button>
                        </div>
                        <div class="col-md-4">
                            <button type="button" class="btn btn-danger btn-block" data-toggle="modal" data-target="#rejectModal">
                                <i class="fas fa-times"></i>
                                رفض الاقتراح
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @elseif($suggestion->status === 'under_review')
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info shadow">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-success btn-block" data-toggle="modal" data-target="#approveModal">
                                <i class="fas fa-check"></i>
                                اعتماد الاقتراح
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-danger btn-block" data-toggle="modal" data-target="#rejectModal">
                                <i class="fas fa-times"></i>
                                رفض الاقتراح
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @elseif($suggestion->status === 'approved')
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-success shadow">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-primary btn-block" data-toggle="modal" data-target="#implementModal">
                                <i class="fas fa-cogs"></i>
                                تسجيل التنفيذ
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-warning btn-block" data-toggle="modal" data-target="#rewardModal">
                                <i class="fas fa-gift"></i>
                                تسجيل المكافأة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <!-- Main Information -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle"></i>
                        معلومات الاقتراح
                    </h6>
                </div>
                <div class="card-body">
                    <!-- Title and Category -->
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <h4 class="text-primary">{{ $suggestion->title }}</h4>
                        </div>
                        <div class="col-md-4 text-right">
                            @switch($suggestion->category)
                                @case('process_improvement')
                                    <span class="badge badge-info badge-lg">تحسين العمليات</span>
                                    @break
                                @case('cost_reduction')
                                    <span class="badge badge-success badge-lg">تقليل التكاليف</span>
                                    @break
                                @case('quality_improvement')
                                    <span class="badge badge-primary badge-lg">تحسين الجودة</span>
                                    @break
                                @case('customer_service')
                                    <span class="badge badge-warning badge-lg">خدمة العملاء</span>
                                    @break
                                @case('safety')
                                    <span class="badge badge-danger badge-lg">السلامة والأمان</span>
                                    @break
                                @case('technology')
                                    <span class="badge badge-dark badge-lg">التكنولوجيا</span>
                                    @break
                                @case('environment')
                                    <span class="badge badge-success badge-lg">البيئة</span>
                                    @break
                                @default
                                    <span class="badge badge-secondary badge-lg">أخرى</span>
                            @endswitch
                        </div>
                    </div>

                    <!-- Priority -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <strong>أولوية التنفيذ: </strong>
                            @switch($suggestion->priority)
                                @case('urgent')
                                    <span class="badge badge-danger">عاجلة</span>
                                    @break
                                @case('high')
                                    <span class="badge badge-warning">عالية</span>
                                    @break
                                @case('medium')
                                    <span class="badge badge-info">متوسطة</span>
                                    @break
                                @case('low')
                                    <span class="badge badge-secondary">منخفضة</span>
                                    @break
                            @endswitch
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="mb-4">
                        <h6 class="text-primary">وصف الاقتراح:</h6>
                        <p class="text-justify">{{ $suggestion->description }}</p>
                    </div>

                    <!-- Current Situation -->
                    @if($suggestion->current_situation)
                    <div class="mb-4">
                        <h6 class="text-warning">الوضع الحالي:</h6>
                        <p class="text-justify">{{ $suggestion->current_situation }}</p>
                    </div>
                    @endif

                    <!-- Proposed Solution -->
                    <div class="mb-4">
                        <h6 class="text-success">الحل المقترح:</h6>
                        <p class="text-justify">{{ $suggestion->proposed_solution }}</p>
                    </div>

                    <!-- Expected Benefits -->
                    @if($suggestion->expected_benefits)
                    <div class="mb-4">
                        <h6 class="text-info">الفوائد المتوقعة:</h6>
                        <p class="text-justify">{{ $suggestion->expected_benefits }}</p>
                    </div>
                    @endif

                    <!-- Implementation Timeline -->
                    @if($suggestion->implementation_timeline)
                    <div class="mb-4">
                        <h6 class="text-primary">الجدول الزمني للتنفيذ:</h6>
                        <p>
                            @switch($suggestion->implementation_timeline)
                                @case('immediate')
                                    <span class="badge badge-danger">فوري (أقل من أسبوع)</span>
                                    @break
                                @case('short_term')
                                    <span class="badge badge-warning">قصير المدى (1-4 أسابيع)</span>
                                    @break
                                @case('medium_term')
                                    <span class="badge badge-info">متوسط المدى (1-3 أشهر)</span>
                                    @break
                                @case('long_term')
                                    <span class="badge badge-secondary">طويل المدى (أكثر من 3 أشهر)</span>
                                    @break
                            @endswitch
                        </p>
                    </div>
                    @endif

                    <!-- Additional Notes -->
                    @if($suggestion->additional_notes)
                    <div class="mb-4">
                        <h6 class="text-secondary">ملاحظات إضافية:</h6>
                        <p class="text-justify">{{ $suggestion->additional_notes }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Financial Analysis -->
            @if($suggestion->estimated_cost || $suggestion->estimated_savings)
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-chart-line"></i>
                        التحليل المالي
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-danger">التكلفة المقدرة</h6>
                                <h4 class="text-primary">{{ number_format($suggestion->estimated_cost ?? 0, 2) }} ج.م</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-success">الوفورات السنوية</h6>
                                <h4 class="text-success">{{ number_format($suggestion->estimated_savings ?? 0, 2) }} ج.م</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-info">العائد على الاستثمار</h6>
                                @php
                                    $roi = 0;
                                    if ($suggestion->estimated_cost > 0) {
                                        $roi = (($suggestion->estimated_savings - $suggestion->estimated_cost) / $suggestion->estimated_cost) * 100;
                                    }
                                @endphp
                                <h4 class="{{ $roi >= 0 ? 'text-success' : 'text-danger' }}">{{ number_format($roi, 1) }}%</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-warning">فترة الاسترداد</h6>
                                @php
                                    $payback = 0;
                                    if ($suggestion->estimated_savings > 0) {
                                        $payback = ($suggestion->estimated_cost / ($suggestion->estimated_savings / 12));
                                    }
                                @endphp
                                <h4 class="text-warning">{{ number_format($payback, 1) }} شهر</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Review History -->
            @if($suggestion->review_notes || $suggestion->rejection_reason || $suggestion->implementation_notes)
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-history"></i>
                        تاريخ المراجعة والتنفيذ
                    </h6>
                </div>
                <div class="card-body">
                    @if($suggestion->review_notes)
                    <div class="mb-3">
                        <h6 class="text-info">ملاحظات المراجعة:</h6>
                        <p class="text-justify">{{ $suggestion->review_notes }}</p>
                        @if($suggestion->reviewed_at)
                            <small class="text-muted">تاريخ المراجعة: {{ $suggestion->reviewed_at->format('Y-m-d H:i') }}</small>
                        @endif
                    </div>
                    @endif

                    @if($suggestion->rejection_reason)
                    <div class="mb-3">
                        <h6 class="text-danger">سبب الرفض:</h6>
                        <p class="text-justify">{{ $suggestion->rejection_reason }}</p>
                    </div>
                    @endif

                    @if($suggestion->implementation_notes)
                    <div class="mb-3">
                        <h6 class="text-success">ملاحظات التنفيذ:</h6>
                        <p class="text-justify">{{ $suggestion->implementation_notes }}</p>
                        @if($suggestion->implemented_at)
                            <small class="text-muted">تاريخ التنفيذ: {{ $suggestion->implemented_at->format('Y-m-d H:i') }}</small>
                        @endif
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Employee Information -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user"></i>
                        معلومات مقدم الاقتراح
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <img src="{{ $suggestion->employee->avatar ?? '/images/default-avatar.png' }}" 
                             class="rounded-circle" width="80" height="80" alt="صورة الموظف">
                    </div>
                    <div class="text-center">
                        <h5 class="mb-1">{{ $suggestion->employee->name }}</h5>
                        <p class="text-muted mb-2">{{ $suggestion->employee->employee_code }}</p>
                        <p class="text-muted mb-2">{{ $suggestion->employee->department->name ?? 'غير محدد' }}</p>
                        <p class="text-muted mb-0">{{ $suggestion->employee->position->title ?? 'غير محدد' }}</p>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted">تاريخ التوظيف</small>
                            <p class="mb-0">{{ $suggestion->employee->hire_date ? $suggestion->employee->hire_date->format('Y-m-d') : 'غير محدد' }}</p>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">سنوات الخبرة</small>
                            <p class="mb-0">
                                @if($suggestion->employee->hire_date)
                                    {{ $suggestion->employee->hire_date->diffInYears(now()) }} سنة
                                @else
                                    غير محدد
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Information -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i>
                        حالة الاقتراح
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>الحالة الحالية:</strong><br>
                        @switch($suggestion->status)
                            @case('pending')
                                <span class="badge badge-warning badge-lg">في الانتظار</span>
                                @break
                            @case('under_review')
                                <span class="badge badge-info badge-lg">قيد المراجعة</span>
                                @break
                            @case('approved')
                                <span class="badge badge-success badge-lg">معتمد</span>
                                @break
                            @case('rejected')
                                <span class="badge badge-danger badge-lg">مرفوض</span>
                                @break
                            @case('implemented')
                                <span class="badge badge-primary badge-lg">منفذ</span>
                                @break
                        @endswitch
                    </div>
                    
                    <div class="mb-2">
                        <strong>تاريخ الإنشاء:</strong><br>
                        <small>{{ $suggestion->created_at->format('Y-m-d H:i') }}</small>
                    </div>
                    
                    @if($suggestion->reviewed_at)
                    <div class="mb-2">
                        <strong>تاريخ المراجعة:</strong><br>
                        <small>{{ $suggestion->reviewed_at->format('Y-m-d H:i') }}</small>
                    </div>
                    @endif
                    
                    @if($suggestion->implemented_at)
                    <div class="mb-2">
                        <strong>تاريخ التنفيذ:</strong><br>
                        <small>{{ $suggestion->implemented_at->format('Y-m-d H:i') }}</small>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Reward Information -->
            @if($suggestion->reward_amount || $suggestion->reward_type)
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-gift"></i>
                        معلومات المكافأة
                    </h6>
                </div>
                <div class="card-body">
                    @if($suggestion->reward_type)
                    <div class="mb-2">
                        <strong>نوع المكافأة:</strong><br>
                        @switch($suggestion->reward_type)
                            @case('monetary')
                                <span class="badge badge-success">مكافأة مالية</span>
                                @break
                            @case('certificate')
                                <span class="badge badge-info">شهادة تقدير</span>
                                @break
                            @case('promotion_consideration')
                                <span class="badge badge-warning">اعتبار للترقية</span>
                                @break
                            @case('public_recognition')
                                <span class="badge badge-primary">تقدير علني</span>
                                @break
                        @endswitch
                    </div>
                    @endif
                    
                    @if($suggestion->reward_amount)
                    <div class="mb-2">
                        <strong>قيمة المكافأة:</strong><br>
                        <span class="text-success h5">{{ number_format($suggestion->reward_amount, 2) }} ج.م</span>
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Review Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1" role="dialog" aria-labelledby="reviewModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reviewModalLabel">بدء مراجعة الاقتراح</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-suggestions.review', $suggestion->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="review_notes">ملاحظات المراجعة</label>
                        <textarea class="form-control" id="review_notes" name="review_notes" rows="4"
                                  placeholder="أدخل ملاحظاتك حول الاقتراح..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-search"></i>
                        بدء المراجعة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Approve Modal -->
<div class="modal fade" id="approveModal" tabindex="-1" role="dialog" aria-labelledby="approveModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approveModalLabel">اعتماد الاقتراح</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-suggestions.approve', $suggestion->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        هل أنت متأكد من اعتماد هذا الاقتراح؟
                    </div>
                    <div class="form-group">
                        <label for="approval_notes">ملاحظات الاعتماد</label>
                        <textarea class="form-control" id="approval_notes" name="approval_notes" rows="3"
                                  placeholder="ملاحظات حول اعتماد الاقتراح (اختياري)..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i>
                        اعتماد الاقتراح
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" role="dialog" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectModalLabel">رفض الاقتراح</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-suggestions.reject', $suggestion->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        هل أنت متأكد من رفض هذا الاقتراح؟
                    </div>
                    <div class="form-group">
                        <label for="rejection_reason">سبب الرفض <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="4"
                                  placeholder="يرجى توضيح سبب رفض الاقتراح..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times"></i>
                        رفض الاقتراح
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Implement Modal -->
<div class="modal fade" id="implementModal" tabindex="-1" role="dialog" aria-labelledby="implementModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="implementModalLabel">تسجيل تنفيذ الاقتراح</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-suggestions.implement', $suggestion->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="implementation_date">تاريخ التنفيذ الفعلي</label>
                        <input type="date" class="form-control" id="implementation_date" name="implementation_date"
                               value="{{ date('Y-m-d') }}" required>
                    </div>
                    <div class="form-group">
                        <label for="implementation_notes">ملاحظات التنفيذ</label>
                        <textarea class="form-control" id="implementation_notes" name="implementation_notes" rows="4"
                                  placeholder="تفاصيل حول تنفيذ الاقتراح والنتائج المحققة..."></textarea>
                    </div>
                    <div class="form-group">
                        <label for="actual_cost">التكلفة الفعلية (ج.م)</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="actual_cost" name="actual_cost"
                                   step="0.01" min="0" placeholder="0.00">
                            <div class="input-group-append">
                                <span class="input-group-text">ج.م</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="actual_savings">الوفورات الفعلية (ج.م)</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="actual_savings" name="actual_savings"
                                   step="0.01" min="0" placeholder="0.00">
                            <div class="input-group-append">
                                <span class="input-group-text">ج.م</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-cogs"></i>
                        تسجيل التنفيذ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reward Modal -->
<div class="modal fade" id="rewardModal" tabindex="-1" role="dialog" aria-labelledby="rewardModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rewardModalLabel">تسجيل مكافأة الاقتراح</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-suggestions.reward', $suggestion->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="reward_type">نوع المكافأة <span class="text-danger">*</span></label>
                        <select class="form-control" id="reward_type" name="reward_type" required>
                            <option value="">اختر نوع المكافأة</option>
                            <option value="monetary">مكافأة مالية</option>
                            <option value="certificate">شهادة تقدير</option>
                            <option value="promotion_consideration">اعتبار للترقية</option>
                            <option value="public_recognition">تقدير علني</option>
                        </select>
                    </div>
                    <div class="form-group" id="reward_amount_group" style="display: none;">
                        <label for="reward_amount">قيمة المكافأة (ج.م)</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="reward_amount" name="reward_amount"
                                   step="0.01" min="0" placeholder="0.00">
                            <div class="input-group-append">
                                <span class="input-group-text">ج.م</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="reward_notes">ملاحظات المكافأة</label>
                        <textarea class="form-control" id="reward_notes" name="reward_notes" rows="3"
                                  placeholder="ملاحظات حول المكافأة..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-gift"></i>
                        تسجيل المكافأة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Show/hide reward amount based on reward type
    $('#reward_type').change(function() {
        if ($(this).val() === 'monetary') {
            $('#reward_amount_group').show();
            $('#reward_amount').attr('required', true);
        } else {
            $('#reward_amount_group').hide();
            $('#reward_amount').attr('required', false);
        }
    });
});

// Print styles
const printStyles = `
    <style>
        @media print {
            .btn, .modal, .breadcrumb, .card-header .btn {
                display: none !important;
            }
            .card {
                border: 1px solid #ddd !important;
                box-shadow: none !important;
            }
            .card-body {
                padding: 15px !important;
            }
            body {
                font-size: 12px !important;
            }
            h1, h2, h3, h4, h5, h6 {
                color: #000 !important;
            }
            .badge {
                border: 1px solid #000 !important;
                color: #000 !important;
                background: #fff !important;
            }
        }
    </style>
`;

document.head.insertAdjacentHTML('beforeend', printStyles);
</script>
@endsection
