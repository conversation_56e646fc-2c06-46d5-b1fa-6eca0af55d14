@extends('layouts.app')

@section('title', 'تعديل طلب النقل')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-edit text-primary"></i>
                        تعديل طلب النقل #{{ $transfer->id }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-transfers.index') }}">النقل والتحويلات</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-transfers.show', $transfer->id) }}">تفاصيل النقل</a></li>
                            <li class="breadcrumb-item active">تعديل</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('employee-transfers.show', $transfer->id) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i>
                        العودة للتفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Check -->
    @if($transfer->status !== 'pending')
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                @if($transfer->status === 'approved')
                    هذا الطلب تم اعتماده ولا يمكن تعديله. يمكنك فقط عرض التفاصيل.
                @elseif($transfer->status === 'rejected')
                    هذا الطلب تم رفضه. يمكنك إنشاء طلب جديد إذا لزم الأمر.
                @elseif($transfer->status === 'completed')
                    هذا النقل تم تنفيذه بالكامل ولا يمكن تعديله.
                @endif
                <div class="mt-2">
                    <a href="{{ route('employee-transfers.show', $transfer->id) }}" class="btn btn-sm btn-outline-primary">
                        عرض التفاصيل
                    </a>
                    <a href="{{ route('employee-transfers.create') }}" class="btn btn-sm btn-outline-success">
                        طلب نقل جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
    @endif

    @if($transfer->status === 'pending')
    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit"></i>
                        تعديل بيانات النقل
                    </h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('employee-transfers.update', $transfer->id) }}" method="POST" id="transfer-form">
                        @csrf
                        @method('PUT')

                        <!-- Employee Info (Read-only) -->
                        <div class="form-group">
                            <label>الموظف</label>
                            <div class="form-control-plaintext border rounded p-2 bg-light">
                                <div class="d-flex align-items-center">
                                    <img src="{{ $transfer->employee->avatar ?? '/images/default-avatar.png' }}" 
                                         class="rounded-circle mr-2" width="30" height="30" alt="صورة الموظف">
                                    <div>
                                        <strong>{{ $transfer->employee->name }}</strong>
                                        <br><small class="text-muted">{{ $transfer->employee->employee_code }}</small>
                                    </div>
                                </div>
                            </div>
                            <small class="text-muted">لا يمكن تغيير الموظف بعد إنشاء الطلب</small>
                        </div>

                        <!-- Transfer Type -->
                        <div class="form-group">
                            <label for="type">نوع النقل <span class="text-danger">*</span></label>
                            <select class="form-control @error('type') is-invalid @enderror" id="type" name="type" required>
                                <option value="">اختر نوع النقل</option>
                                <option value="department" {{ old('type', $transfer->type) == 'department' ? 'selected' : '' }}>نقل قسم</option>
                                <option value="branch" {{ old('type', $transfer->type) == 'branch' ? 'selected' : '' }}>نقل فرع</option>
                                <option value="position" {{ old('type', $transfer->type) == 'position' ? 'selected' : '' }}>نقل منصب</option>
                                <option value="location" {{ old('type', $transfer->type) == 'location' ? 'selected' : '' }}>نقل موقع</option>
                                <option value="temporary" {{ old('type', $transfer->type) == 'temporary' ? 'selected' : '' }}>نقل مؤقت</option>
                            </select>
                            @error('type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Transfer Details (Dynamic based on type) -->
                        <div id="transfer-details">
                            <!-- Department Transfer -->
                            <div id="department-section" class="transfer-section" style="display: none;">
                                <div class="form-group">
                                    <label for="from_department_id">من القسم</label>
                                    <select class="form-control" id="from_department_id" name="from_department_id">
                                        <option value="">اختر القسم الحالي</option>
                                        {{-- Department options will be loaded via AJAX --}}
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="to_department_id">إلى القسم <span class="text-danger">*</span></label>
                                    <select class="form-control" id="to_department_id" name="to_department_id">
                                        <option value="">اختر القسم الجديد</option>
                                        {{-- Department options will be loaded via AJAX --}}
                                    </select>
                                </div>
                            </div>

                            <!-- Branch Transfer -->
                            <div id="branch-section" class="transfer-section" style="display: none;">
                                <div class="form-group">
                                    <label for="from_branch_id">من الفرع</label>
                                    <select class="form-control" id="from_branch_id" name="from_branch_id">
                                        <option value="">اختر الفرع الحالي</option>
                                        {{-- Branch options will be loaded via AJAX --}}
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="to_branch_id">إلى الفرع <span class="text-danger">*</span></label>
                                    <select class="form-control" id="to_branch_id" name="to_branch_id">
                                        <option value="">اختر الفرع الجديد</option>
                                        {{-- Branch options will be loaded via AJAX --}}
                                    </select>
                                </div>
                            </div>

                            <!-- Position Transfer -->
                            <div id="position-section" class="transfer-section" style="display: none;">
                                <div class="form-group">
                                    <label for="from_position_id">من المنصب</label>
                                    <select class="form-control" id="from_position_id" name="from_position_id">
                                        <option value="">اختر المنصب الحالي</option>
                                        {{-- Position options will be loaded via AJAX --}}
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="to_position_id">إلى المنصب <span class="text-danger">*</span></label>
                                    <select class="form-control" id="to_position_id" name="to_position_id">
                                        <option value="">اختر المنصب الجديد</option>
                                        {{-- Position options will be loaded via AJAX --}}
                                    </select>
                                </div>
                            </div>

                            <!-- Location Transfer -->
                            <div id="location-section" class="transfer-section" style="display: none;">
                                <div class="form-group">
                                    <label for="from_location">من الموقع</label>
                                    <input type="text" class="form-control" id="from_location" name="from_location" 
                                           value="{{ old('from_location', $transfer->from_location) }}" placeholder="الموقع الحالي">
                                </div>
                                <div class="form-group">
                                    <label for="to_location">إلى الموقع <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="to_location" name="to_location" 
                                           value="{{ old('to_location', $transfer->to_location) }}" placeholder="الموقع الجديد">
                                </div>
                            </div>

                            <!-- Temporary Transfer -->
                            <div id="temporary-section" class="transfer-section" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="temporary_start_date">تاريخ البداية <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="temporary_start_date" name="temporary_start_date" 
                                                   value="{{ old('temporary_start_date', $transfer->temporary_start_date ? $transfer->temporary_start_date->format('Y-m-d') : '') }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="temporary_end_date">تاريخ النهاية <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="temporary_end_date" name="temporary_end_date" 
                                                   value="{{ old('temporary_end_date', $transfer->temporary_end_date ? $transfer->temporary_end_date->format('Y-m-d') : '') }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="temporary_reason">سبب النقل المؤقت</label>
                                    <textarea class="form-control" id="temporary_reason" name="temporary_reason" rows="3" 
                                              placeholder="اذكر سبب النقل المؤقت">{{ old('temporary_reason', $transfer->temporary_reason) }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Transfer Date -->
                        <div class="form-group">
                            <label for="transfer_date">تاريخ النقل المطلوب <span class="text-danger">*</span></label>
                            <input type="date" class="form-control @error('transfer_date') is-invalid @enderror" 
                                   id="transfer_date" name="transfer_date" 
                                   value="{{ old('transfer_date', $transfer->transfer_date ? $transfer->transfer_date->format('Y-m-d') : '') }}" required>
                            @error('transfer_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Reason -->
                        <div class="form-group">
                            <label for="reason">سبب النقل <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('reason') is-invalid @enderror" 
                                      id="reason" name="reason" rows="4" required 
                                      placeholder="اذكر الأسباب التفصيلية لطلب النقل">{{ old('reason', $transfer->reason) }}</textarea>
                            @error('reason')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Notes -->
                        <div class="form-group">
                            <label for="notes">ملاحظات إضافية</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                      id="notes" name="notes" rows="3" 
                                      placeholder="أي ملاحظات أو تفاصيل إضافية">{{ old('notes', $transfer->notes) }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Priority -->
                        <div class="form-group">
                            <label for="priority">أولوية الطلب</label>
                            <select class="form-control @error('priority') is-invalid @enderror" id="priority" name="priority">
                                <option value="normal" {{ old('priority', $transfer->priority) == 'normal' ? 'selected' : '' }}>عادي</option>
                                <option value="high" {{ old('priority', $transfer->priority) == 'high' ? 'selected' : '' }}>عالي</option>
                                <option value="urgent" {{ old('priority', $transfer->priority) == 'urgent' ? 'selected' : '' }}>عاجل</option>
                            </select>
                            @error('priority')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Form Actions -->
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ التعديلات
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                            <a href="{{ route('employee-transfers.show', $transfer->id) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Current Transfer Info -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i>
                        معلومات النقل الحالية
                    </h6>
                </div>
                <div class="card-body">
                    <p><strong>رقم الطلب:</strong> #{{ $transfer->id }}</p>
                    <p><strong>نوع النقل:</strong> 
                        @switch($transfer->type)
                            @case('department')
                                نقل قسم
                                @break
                            @case('branch')
                                نقل فرع
                                @break
                            @case('position')
                                نقل منصب
                                @break
                            @case('location')
                                نقل موقع
                                @break
                            @case('temporary')
                                نقل مؤقت
                                @break
                        @endswitch
                    </p>
                    <p><strong>تاريخ الطلب:</strong> {{ $transfer->created_at->format('Y-m-d H:i') }}</p>
                    <p><strong>الحالة:</strong> 
                        <span class="badge badge-warning">في الانتظار</span>
                    </p>
                    @if($transfer->transfer_date)
                        <p><strong>تاريخ النقل المطلوب:</strong> {{ $transfer->transfer_date->format('Y-m-d') }}</p>
                    @endif
                </div>
            </div>

            <!-- Employee Info -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user"></i>
                        معلومات الموظف
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <img src="{{ $transfer->employee->avatar ?? '/images/default-avatar.png' }}" 
                             class="rounded-circle" width="80" height="80" alt="صورة الموظف">
                    </div>
                    <p><strong>الاسم:</strong> {{ $transfer->employee->name }}</p>
                    <p><strong>الكود:</strong> {{ $transfer->employee->employee_code }}</p>
                    <p><strong>القسم الحالي:</strong> {{ $transfer->employee->department->name ?? 'غير محدد' }}</p>
                    <p><strong>المنصب الحالي:</strong> {{ $transfer->employee->position->name ?? 'غير محدد' }}</p>
                    <p><strong>الفرع الحالي:</strong> {{ $transfer->employee->branch->name ?? 'غير محدد' }}</p>
                    
                    <div class="mt-3">
                        <a href="{{ route('employees.show', $transfer->employee->id) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i>
                            عرض ملف الموظف
                        </a>
                    </div>
                </div>
            </div>

            <!-- Edit Guidelines -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-lightbulb"></i>
                        إرشادات التعديل
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            يمكن تعديل جميع بيانات النقل
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-times text-danger"></i>
                            لا يمكن تغيير الموظف
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-info text-info"></i>
                            تأكد من صحة التواريخ
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-exclamation text-warning"></i>
                            سيتم إعادة تقييم الطلب بعد التعديل
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize form based on current transfer type
    const currentType = $('#type').val();
    if (currentType) {
        showTransferSection(currentType);
        loadTransferData(currentType);
    }

    // Handle transfer type change
    $('#type').change(function() {
        const type = $(this).val();
        showTransferSection(type);
        if (type) {
            loadTransferData(type);
        }
    });

    // Set minimum date for transfer date
    const today = new Date().toISOString().split('T')[0];
    $('#transfer_date').attr('min', today);
    $('#temporary_start_date').attr('min', today);
    
    // Handle temporary transfer date validation
    $('#temporary_start_date').change(function() {
        const startDate = $(this).val();
        if (startDate) {
            $('#temporary_end_date').attr('min', startDate);
        }
    });
});

function showTransferSection(type) {
    // Hide all sections
    $('.transfer-section').hide();
    
    // Show relevant section
    if (type) {
        $('#' + type + '-section').show();
    }
}

function loadTransferData(type) {
    // Load current employee data and populate from fields
    const employeeId = {{ $transfer->employee->id }};
    
    $.get(`/api/employees/${employeeId}/transfer-data`)
        .done(function(response) {
            if (response.success) {
                const employee = response.data;
                
                // Populate current data
                if (type === 'department') {
                    loadDepartments();
                    if (employee.department_id) {
                        $('#from_department_id').val(employee.department_id);
                    }
                } else if (type === 'branch') {
                    loadBranches();
                    if (employee.branch_id) {
                        $('#from_branch_id').val(employee.branch_id);
                    }
                } else if (type === 'position') {
                    loadPositions();
                    if (employee.position_id) {
                        $('#from_position_id').val(employee.position_id);
                    }
                }
            }
        })
        .fail(function() {
            console.error('Failed to load employee transfer data');
        });
}

function loadDepartments() {
    $.get('/api/departments')
        .done(function(response) {
            if (response.success) {
                const departments = response.data;
                const fromSelect = $('#from_department_id');
                const toSelect = $('#to_department_id');
                
                fromSelect.empty().append('<option value="">اختر القسم الحالي</option>');
                toSelect.empty().append('<option value="">اختر القسم الجديد</option>');
                
                departments.forEach(function(dept) {
                    const option = `<option value="${dept.id}">${dept.name}</option>`;
                    fromSelect.append(option);
                    toSelect.append(option);
                });

                // Set current values if editing
                @if($transfer->from_department_id)
                    fromSelect.val({{ $transfer->from_department_id }});
                @endif
                @if($transfer->to_department_id)
                    toSelect.val({{ $transfer->to_department_id }});
                @endif
            }
        });
}

function loadBranches() {
    $.get('/api/branches')
        .done(function(response) {
            if (response.success) {
                const branches = response.data;
                const fromSelect = $('#from_branch_id');
                const toSelect = $('#to_branch_id');
                
                fromSelect.empty().append('<option value="">اختر الفرع الحالي</option>');
                toSelect.empty().append('<option value="">اختر الفرع الجديد</option>');
                
                branches.forEach(function(branch) {
                    const option = `<option value="${branch.id}">${branch.name}</option>`;
                    fromSelect.append(option);
                    toSelect.append(option);
                });

                // Set current values if editing
                @if($transfer->from_branch_id)
                    fromSelect.val({{ $transfer->from_branch_id }});
                @endif
                @if($transfer->to_branch_id)
                    toSelect.val({{ $transfer->to_branch_id }});
                @endif
            }
        });
}

function loadPositions() {
    $.get('/api/positions')
        .done(function(response) {
            if (response.success) {
                const positions = response.data;
                const fromSelect = $('#from_position_id');
                const toSelect = $('#to_position_id');
                
                fromSelect.empty().append('<option value="">اختر المنصب الحالي</option>');
                toSelect.empty().append('<option value="">اختر المنصب الجديد</option>');
                
                positions.forEach(function(position) {
                    const option = `<option value="${position.id}">${position.name}</option>`;
                    fromSelect.append(option);
                    toSelect.append(option);
                });

                // Set current values if editing
                @if($transfer->from_position_id)
                    fromSelect.val({{ $transfer->from_position_id }});
                @endif
                @if($transfer->to_position_id)
                    toSelect.val({{ $transfer->to_position_id }});
                @endif
            }
        });
}

function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع التغييرات غير المحفوظة.')) {
        document.getElementById('transfer-form').reset();
        const currentType = $('#type').val();
        if (currentType) {
            showTransferSection(currentType);
        }
    }
}
</script>
@endsection
