@extends('layouts.app')

@section('title', 'النقل والتحويلات')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-exchange-alt text-primary"></i>
                        النقل والتحويلات
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item active">النقل والتحويلات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button type="button" class="btn btn-info btn-sm" onclick="loadStatistics()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث الإحصائيات
                    </button>
                    <a href="{{ route('employee-transfers.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        طلب نقل جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي طلبات النقل
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-transfers">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                في الانتظار
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="pending-transfers">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                معتمدة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="approved-transfers">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                مكتملة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="completed-transfers">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-double fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt"></i>
                        إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-warning h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <i class="fas fa-clock fa-2x text-warning"></i>
                                        </div>
                                        <div>
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                طلبات معلقة
                                            </div>
                                            <div class="h6 mb-0 font-weight-bold text-gray-800" id="pending-count">0</div>
                                            <a href="#" class="btn btn-warning btn-sm mt-2" onclick="filterByStatus('pending')">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-danger h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                                        </div>
                                        <div>
                                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                                عاجلة
                                            </div>
                                            <div class="h6 mb-0 font-weight-bold text-gray-800" id="urgent-count">0</div>
                                            <a href="#" class="btn btn-danger btn-sm mt-2" onclick="filterByPriority('urgent')">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-info h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <i class="fas fa-calendar-alt fa-2x text-info"></i>
                                        </div>
                                        <div>
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                مستحقة اليوم
                                            </div>
                                            <div class="h6 mb-0 font-weight-bold text-gray-800" id="due-today">0</div>
                                            <a href="#" class="btn btn-info btn-sm mt-2" onclick="filterByDueDate('today')">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card border-left-success h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <i class="fas fa-file-export fa-2x text-success"></i>
                                        </div>
                                        <div>
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                تقرير شامل
                                            </div>
                                            <div class="h6 mb-0 font-weight-bold text-gray-800">تصدير</div>
                                            <button type="button" class="btn btn-success btn-sm mt-2" onclick="exportTransfers()">
                                                <i class="fas fa-download"></i>
                                                Excel
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-filter"></i>
                        فلترة النتائج
                    </h6>
                </div>
                <div class="card-body">
                    <form id="filter-form">
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <label for="employee_filter">الموظف</label>
                                <select class="form-control select2" id="employee_filter" name="employee_id">
                                    <option value="">جميع الموظفين</option>
                                    {{-- Employee options will be loaded via AJAX --}}
                                </select>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <label for="status_filter">الحالة</label>
                                <select class="form-control" id="status_filter" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending">في الانتظار</option>
                                    <option value="approved">معتمد</option>
                                    <option value="rejected">مرفوض</option>
                                    <option value="completed">مكتمل</option>
                                </select>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <label for="type_filter">نوع النقل</label>
                                <select class="form-control" id="type_filter" name="type">
                                    <option value="">جميع الأنواع</option>
                                    <option value="department">نقل قسم</option>
                                    <option value="branch">نقل فرع</option>
                                    <option value="position">نقل منصب</option>
                                    <option value="location">نقل موقع</option>
                                    <option value="temporary">نقل مؤقت</option>
                                </select>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <label for="priority_filter">الأولوية</label>
                                <select class="form-control" id="priority_filter" name="priority">
                                    <option value="">جميع الأولويات</option>
                                    <option value="normal">عادي</option>
                                    <option value="high">عالي</option>
                                    <option value="urgent">عاجل</option>
                                </select>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <label for="date_from">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from">
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <label for="date_to">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to">
                            </div>

                            <div class="col-lg-6 col-md-12 mb-3 d-flex align-items-end">
                                <button type="button" class="btn btn-primary mr-2" onclick="applyFilters()">
                                    <i class="fas fa-search"></i>
                                    بحث
                                </button>
                                <button type="button" class="btn btn-secondary mr-2" onclick="resetFilters()">
                                    <i class="fas fa-undo"></i>
                                    إعادة تعيين
                                </button>
                                <button type="button" class="btn btn-success" onclick="exportFiltered()">
                                    <i class="fas fa-file-excel"></i>
                                    تصدير النتائج
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfers Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list"></i>
                        قائمة طلبات النقل
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="transfers-table" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>الموظف</th>
                                    <th>نوع النقل</th>
                                    <th>من</th>
                                    <th>إلى</th>
                                    <th>تاريخ النقل</th>
                                    <th>الأولوية</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الطلب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{-- Data will be loaded via DataTables AJAX --}}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize DataTable
    initializeDataTable();
    
    // Load statistics
    loadStatistics();
    
    // Load employees for filter
    loadEmployees();
    
    // Initialize Select2
    $('.select2').select2({
        placeholder: 'اختر موظف...',
        allowClear: true,
        language: 'ar'
    });
});

function initializeDataTable() {
    $('#transfers-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("employee-transfers.data") }}',
            data: function(d) {
                d.employee_id = $('#employee_filter').val();
                d.status = $('#status_filter').val();
                d.type = $('#type_filter').val();
                d.priority = $('#priority_filter').val();
                d.date_from = $('#date_from').val();
                d.date_to = $('#date_to').val();
            }
        },
        columns: [
            { data: 'id', name: 'id' },
            { 
                data: 'employee', 
                name: 'employee.name',
                render: function(data, type, row) {
                    return `
                        <div class="d-flex align-items-center">
                            <img src="${data.avatar || '/images/default-avatar.png'}" 
                                 class="rounded-circle mr-2" width="30" height="30">
                            <div>
                                <strong>${data.name}</strong><br>
                                <small class="text-muted">${data.employee_code}</small>
                            </div>
                        </div>
                    `;
                }
            },
            { 
                data: 'type', 
                name: 'type',
                render: function(data) {
                    const types = {
                        'department': '<span class="badge badge-info">نقل قسم</span>',
                        'branch': '<span class="badge badge-warning">نقل فرع</span>',
                        'position': '<span class="badge badge-success">نقل منصب</span>',
                        'location': '<span class="badge badge-secondary">نقل موقع</span>',
                        'temporary': '<span class="badge badge-primary">نقل مؤقت</span>'
                    };
                    return types[data] || data;
                }
            },
            { data: 'from_location', name: 'from_location' },
            { data: 'to_location', name: 'to_location' },
            { data: 'transfer_date', name: 'transfer_date' },
            { 
                data: 'priority', 
                name: 'priority',
                render: function(data) {
                    const priorities = {
                        'normal': '<span class="badge badge-secondary">عادي</span>',
                        'high': '<span class="badge badge-warning">عالي</span>',
                        'urgent': '<span class="badge badge-danger">عاجل</span>'
                    };
                    return priorities[data] || data;
                }
            },
            { 
                data: 'status', 
                name: 'status',
                render: function(data) {
                    const statuses = {
                        'pending': '<span class="badge badge-warning">في الانتظار</span>',
                        'approved': '<span class="badge badge-success">معتمد</span>',
                        'rejected': '<span class="badge badge-danger">مرفوض</span>',
                        'completed': '<span class="badge badge-info">مكتمل</span>'
                    };
                    return statuses[data] || data;
                }
            },
            { data: 'created_at', name: 'created_at' },
            { 
                data: 'actions', 
                name: 'actions', 
                orderable: false, 
                searchable: false,
                render: function(data, type, row) {
                    let actions = `
                        <div class="btn-group" role="group">
                            <a href="/employee-transfers/${row.id}" class="btn btn-info btn-sm">
                                <i class="fas fa-eye"></i>
                            </a>
                    `;
                    
                    if (row.status === 'pending') {
                        actions += `
                            <a href="/employee-transfers/${row.id}/edit" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit"></i>
                            </a>
                        `;
                    }
                    
                    actions += `</div>`;
                    return actions;
                }
            }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        order: [[0, 'desc']],
        pageLength: 25,
        responsive: true
    });
}

function loadStatistics() {
    // Show loading spinners
    $('#total-transfers, #pending-transfers, #approved-transfers, #completed-transfers').html('<div class="spinner-border spinner-border-sm" role="status"></div>');
    
    // Simulate loading statistics (replace with actual AJAX call)
    setTimeout(function() {
        $('#total-transfers').text('0');
        $('#pending-transfers').text('0');
        $('#approved-transfers').text('0');
        $('#completed-transfers').text('0');
        $('#pending-count').text('0');
        $('#urgent-count').text('0');
        $('#due-today').text('0');
    }, 1000);
}

function loadEmployees() {
    $.get('/api/employees')
        .done(function(response) {
            if (response.success) {
                const employees = response.data;
                const select = $('#employee_filter');
                
                employees.forEach(function(employee) {
                    select.append(`<option value="${employee.id}">${employee.name} - ${employee.employee_code}</option>`);
                });
            }
        })
        .fail(function() {
            console.error('Failed to load employees');
        });
}

function applyFilters() {
    $('#transfers-table').DataTable().ajax.reload();
}

function resetFilters() {
    $('#filter-form')[0].reset();
    $('.select2').val(null).trigger('change');
    $('#transfers-table').DataTable().ajax.reload();
}

function filterByStatus(status) {
    $('#status_filter').val(status);
    applyFilters();
}

function filterByPriority(priority) {
    $('#priority_filter').val(priority);
    applyFilters();
}

function filterByDueDate(period) {
    const today = new Date().toISOString().split('T')[0];
    if (period === 'today') {
        $('#date_from').val(today);
        $('#date_to').val(today);
    }
    applyFilters();
}

function exportTransfers() {
    window.location.href = '{{ route("employee-transfers.export") }}';
}

function exportFiltered() {
    const params = new URLSearchParams();
    
    if ($('#employee_filter').val()) params.append('employee_id', $('#employee_filter').val());
    if ($('#status_filter').val()) params.append('status', $('#status_filter').val());
    if ($('#type_filter').val()) params.append('type', $('#type_filter').val());
    if ($('#priority_filter').val()) params.append('priority', $('#priority_filter').val());
    if ($('#date_from').val()) params.append('date_from', $('#date_from').val());
    if ($('#date_to').val()) params.append('date_to', $('#date_to').val());
    
    window.location.href = `{{ route("employee-transfers.export") }}?${params.toString()}`;
}
</script>
@endsection
