@extends('layouts.app')

@section('title', 'تفاصيل طلب النقل')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-eye text-info"></i>
                        تفاصيل طلب النقل #{{ $transfer->id }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-transfers.index') }}">النقل والتحويلات</a></li>
                            <li class="breadcrumb-item active">تفاصيل النقل</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <div class="btn-group" role="group">
                        @if($transfer->status === 'pending')
                            <a href="{{ route('employee-transfers.edit', $transfer->id) }}" class="btn btn-primary">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </a>
                        @endif
                        <button type="button" class="btn btn-success" onclick="printTransfer()">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                        <a href="{{ route('employee-transfers.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Actions -->
    @if($transfer->status === 'pending')
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-warning mb-1">
                                <i class="fas fa-clock"></i>
                                هذا الطلب في انتظار الموافقة
                            </h6>
                            <small class="text-muted">يمكنك اعتماد أو رفض طلب النقل</small>
                        </div>
                        <div>
                            <button type="button" class="btn btn-success btn-sm" onclick="approveTransfer()">
                                <i class="fas fa-check"></i>
                                اعتماد
                            </button>
                            <button type="button" class="btn btn-danger btn-sm" onclick="rejectTransfer()">
                                <i class="fas fa-times"></i>
                                رفض
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Main Content -->
    <div class="row">
        <!-- Transfer Details -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle"></i>
                        تفاصيل النقل
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>رقم الطلب:</strong> #{{ $transfer->id }}</p>
                            <p><strong>نوع النقل:</strong> 
                                @switch($transfer->type)
                                    @case('department')
                                        <span class="badge badge-info">نقل قسم</span>
                                        @break
                                    @case('branch')
                                        <span class="badge badge-warning">نقل فرع</span>
                                        @break
                                    @case('position')
                                        <span class="badge badge-success">نقل منصب</span>
                                        @break
                                    @case('location')
                                        <span class="badge badge-secondary">نقل موقع</span>
                                        @break
                                    @case('temporary')
                                        <span class="badge badge-primary">نقل مؤقت</span>
                                        @break
                                @endswitch
                            </p>
                            <p><strong>الأولوية:</strong> 
                                @switch($transfer->priority)
                                    @case('normal')
                                        <span class="badge badge-secondary">عادي</span>
                                        @break
                                    @case('high')
                                        <span class="badge badge-warning">عالي</span>
                                        @break
                                    @case('urgent')
                                        <span class="badge badge-danger">عاجل</span>
                                        @break
                                @endswitch
                            </p>
                            <p><strong>تاريخ الطلب:</strong> {{ $transfer->created_at->format('Y-m-d H:i') }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>الحالة:</strong> 
                                @switch($transfer->status)
                                    @case('pending')
                                        <span class="badge badge-warning">في الانتظار</span>
                                        @break
                                    @case('approved')
                                        <span class="badge badge-success">معتمد</span>
                                        @break
                                    @case('rejected')
                                        <span class="badge badge-danger">مرفوض</span>
                                        @break
                                    @case('completed')
                                        <span class="badge badge-info">مكتمل</span>
                                        @break
                                @endswitch
                            </p>
                            @if($transfer->transfer_date)
                                <p><strong>تاريخ النقل المطلوب:</strong> {{ $transfer->transfer_date->format('Y-m-d') }}</p>
                            @endif
                            @if($transfer->approved_at)
                                <p><strong>تاريخ الاعتماد:</strong> {{ $transfer->approved_at->format('Y-m-d H:i') }}</p>
                            @endif
                            @if($transfer->approved_by)
                                <p><strong>معتمد من:</strong> {{ $transfer->approver->name ?? 'غير محدد' }}</p>
                            @endif
                        </div>
                    </div>

                    <hr>

                    <!-- Transfer Details Based on Type -->
                    @if($transfer->type === 'department')
                        <div class="row">
                            <div class="col-md-6">
                                <h6><strong>من القسم:</strong></h6>
                                <p class="text-muted">{{ $transfer->fromDepartment->name ?? 'غير محدد' }}</p>
                            </div>
                            <div class="col-md-6">
                                <h6><strong>إلى القسم:</strong></h6>
                                <p class="text-primary font-weight-bold">{{ $transfer->toDepartment->name ?? 'غير محدد' }}</p>
                            </div>
                        </div>
                    @elseif($transfer->type === 'branch')
                        <div class="row">
                            <div class="col-md-6">
                                <h6><strong>من الفرع:</strong></h6>
                                <p class="text-muted">{{ $transfer->fromBranch->name ?? 'غير محدد' }}</p>
                            </div>
                            <div class="col-md-6">
                                <h6><strong>إلى الفرع:</strong></h6>
                                <p class="text-primary font-weight-bold">{{ $transfer->toBranch->name ?? 'غير محدد' }}</p>
                            </div>
                        </div>
                    @elseif($transfer->type === 'position')
                        <div class="row">
                            <div class="col-md-6">
                                <h6><strong>من المنصب:</strong></h6>
                                <p class="text-muted">{{ $transfer->fromPosition->name ?? 'غير محدد' }}</p>
                            </div>
                            <div class="col-md-6">
                                <h6><strong>إلى المنصب:</strong></h6>
                                <p class="text-primary font-weight-bold">{{ $transfer->toPosition->name ?? 'غير محدد' }}</p>
                            </div>
                        </div>
                    @elseif($transfer->type === 'location')
                        <div class="row">
                            <div class="col-md-6">
                                <h6><strong>من الموقع:</strong></h6>
                                <p class="text-muted">{{ $transfer->from_location ?? 'غير محدد' }}</p>
                            </div>
                            <div class="col-md-6">
                                <h6><strong>إلى الموقع:</strong></h6>
                                <p class="text-primary font-weight-bold">{{ $transfer->to_location ?? 'غير محدد' }}</p>
                            </div>
                        </div>
                    @elseif($transfer->type === 'temporary')
                        <div class="row">
                            <div class="col-md-4">
                                <h6><strong>تاريخ البداية:</strong></h6>
                                <p class="text-info">{{ $transfer->temporary_start_date ? $transfer->temporary_start_date->format('Y-m-d') : 'غير محدد' }}</p>
                            </div>
                            <div class="col-md-4">
                                <h6><strong>تاريخ النهاية:</strong></h6>
                                <p class="text-warning">{{ $transfer->temporary_end_date ? $transfer->temporary_end_date->format('Y-m-d') : 'غير محدد' }}</p>
                            </div>
                            <div class="col-md-4">
                                <h6><strong>المدة:</strong></h6>
                                @if($transfer->temporary_start_date && $transfer->temporary_end_date)
                                    <p class="text-primary">{{ $transfer->temporary_start_date->diffInDays($transfer->temporary_end_date) }} يوم</p>
                                @else
                                    <p class="text-muted">غير محدد</p>
                                @endif
                            </div>
                        </div>
                        @if($transfer->temporary_reason)
                            <div class="row">
                                <div class="col-12">
                                    <h6><strong>سبب النقل المؤقت:</strong></h6>
                                    <p class="text-muted">{{ $transfer->temporary_reason }}</p>
                                </div>
                            </div>
                        @endif
                    @endif

                    <hr>

                    <div class="row">
                        <div class="col-12">
                            <h6><strong>سبب النقل:</strong></h6>
                            <p class="text-muted">{{ $transfer->reason }}</p>
                            
                            @if($transfer->notes)
                                <h6><strong>ملاحظات:</strong></h6>
                                <p class="text-muted">{{ $transfer->notes }}</p>
                            @endif

                            @if($transfer->rejection_reason)
                                <h6><strong>سبب الرفض:</strong></h6>
                                <div class="alert alert-danger">
                                    {{ $transfer->rejection_reason }}
                                </div>
                            @endif

                            @if($transfer->approval_notes)
                                <h6><strong>ملاحظات الاعتماد:</strong></h6>
                                <div class="alert alert-success">
                                    {{ $transfer->approval_notes }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transfer Timeline -->
            @if($transfer->status !== 'pending')
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-history"></i>
                        تاريخ النقل
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">تم إنشاء الطلب</h6>
                                <p class="timeline-text">{{ $transfer->created_at->format('Y-m-d H:i') }}</p>
                            </div>
                        </div>
                        
                        @if($transfer->approved_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">تم اعتماد الطلب</h6>
                                <p class="timeline-text">{{ $transfer->approved_at->format('Y-m-d H:i') }}</p>
                                @if($transfer->approver)
                                    <small class="text-muted">بواسطة: {{ $transfer->approver->name }}</small>
                                @endif
                            </div>
                        </div>
                        @endif

                        @if($transfer->status === 'rejected')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">تم رفض الطلب</h6>
                                <p class="timeline-text">{{ $transfer->updated_at->format('Y-m-d H:i') }}</p>
                            </div>
                        </div>
                        @endif

                        @if($transfer->status === 'completed')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">تم تنفيذ النقل</h6>
                                <p class="timeline-text">{{ $transfer->completed_at ? $transfer->completed_at->format('Y-m-d H:i') : 'غير محدد' }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Employee Info -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user"></i>
                        معلومات الموظف
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <img src="{{ $transfer->employee->avatar ?? '/images/default-avatar.png' }}" 
                             class="rounded-circle" width="80" height="80" alt="صورة الموظف">
                    </div>
                    <p><strong>الاسم:</strong> {{ $transfer->employee->name }}</p>
                    <p><strong>الكود:</strong> {{ $transfer->employee->employee_code }}</p>
                    <p><strong>القسم الحالي:</strong> {{ $transfer->employee->department->name ?? 'غير محدد' }}</p>
                    <p><strong>المنصب الحالي:</strong> {{ $transfer->employee->position->name ?? 'غير محدد' }}</p>
                    <p><strong>الفرع الحالي:</strong> {{ $transfer->employee->branch->name ?? 'غير محدد' }}</p>
                    
                    <div class="mt-3">
                        <a href="{{ route('employees.show', $transfer->employee->id) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i>
                            عرض ملف الموظف
                        </a>
                    </div>
                </div>
            </div>

            <!-- Transfer Summary -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-chart-pie"></i>
                        ملخص النقل
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <h4 class="text-primary">{{ $transfer->type }}</h4>
                            <small class="text-muted">نوع النقل</small>
                        </div>
                    </div>
                    
                    @if($transfer->transfer_date)
                        <hr>
                        <div class="row text-center">
                            <div class="col-12">
                                <h5 class="text-info">{{ $transfer->transfer_date->format('Y-m-d') }}</h5>
                                <small class="text-muted">تاريخ النقل المطلوب</small>
                            </div>
                        </div>
                    @endif

                    @if($transfer->type === 'temporary' && $transfer->temporary_start_date && $transfer->temporary_end_date)
                        <hr>
                        <div class="alert alert-info">
                            <h6><i class="fas fa-calendar-alt"></i> النقل المؤقت:</h6>
                            <p class="mb-1">من: {{ $transfer->temporary_start_date->format('Y-m-d') }}</p>
                            <p class="mb-1">إلى: {{ $transfer->temporary_end_date->format('Y-m-d') }}</p>
                            <p class="mb-0">المدة: {{ $transfer->temporary_start_date->diffInDays($transfer->temporary_end_date) }} يوم</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-cogs"></i>
                        إجراءات
                    </h6>
                </div>
                <div class="card-body">
                    @if($transfer->status === 'pending')
                        <button type="button" class="btn btn-success btn-block mb-2" onclick="approveTransfer()">
                            <i class="fas fa-check"></i>
                            اعتماد النقل
                        </button>
                        <button type="button" class="btn btn-danger btn-block mb-2" onclick="rejectTransfer()">
                            <i class="fas fa-times"></i>
                            رفض النقل
                        </button>
                        <a href="{{ route('employee-transfers.edit', $transfer->id) }}" class="btn btn-primary btn-block mb-2">
                            <i class="fas fa-edit"></i>
                            تعديل النقل
                        </a>
                    @endif
                    
                    @if($transfer->status === 'approved')
                        <button type="button" class="btn btn-info btn-block mb-2" onclick="completeTransfer()">
                            <i class="fas fa-check-double"></i>
                            تأكيد تنفيذ النقل
                        </button>
                    @endif
                    
                    <button type="button" class="btn btn-secondary btn-block mb-2" onclick="printTransfer()">
                        <i class="fas fa-print"></i>
                        طباعة التفاصيل
                    </button>
                    
                    <a href="{{ route('employee-transfers.create') }}" class="btn btn-outline-primary btn-block">
                        <i class="fas fa-plus"></i>
                        طلب نقل جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">اعتماد طلب النقل</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-transfers.approve', $transfer->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <p>هل أنت متأكد من اعتماد طلب النقل هذا؟</p>
                    <div class="form-group">
                        <label for="approval_notes">ملاحظات الاعتماد (اختياري)</label>
                        <textarea class="form-control" id="approval_notes" name="approval_notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i>
                        اعتماد
                    </button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div class="modal fade" id="rejectionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفض طلب النقل</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-transfers.reject', $transfer->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <p>هل أنت متأكد من رفض طلب النقل هذا؟</p>
                    <div class="form-group">
                        <label for="rejection_reason">سبب الرفض <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times"></i>
                        رفض
                    </button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Complete Transfer Modal -->
<div class="modal fade" id="completeModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد تنفيذ النقل</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-transfers.complete', $transfer->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <p>هل تم تنفيذ النقل فعلياً؟ هذا الإجراء سيحدث بيانات الموظف.</p>
                    <div class="form-group">
                        <label for="completion_date">تاريخ التنفيذ</label>
                        <input type="date" class="form-control" id="completion_date" name="completion_date" 
                               value="{{ date('Y-m-d') }}" required>
                    </div>
                    <div class="form-group">
                        <label for="completion_notes">ملاحظات التنفيذ</label>
                        <textarea class="form-control" id="completion_notes" name="completion_notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-check-double"></i>
                        تأكيد التنفيذ
                    </button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@endsection

@section('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -29px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #e3e6f0;
}

.timeline-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
}

.timeline-text {
    font-size: 13px;
    color: #6c757d;
    margin-bottom: 0;
}

@media print {
    .btn, .modal, .card-header .btn-group {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }

    .card-body {
        padding: 15px !important;
    }
}
</style>
@endsection

@section('scripts')
<script>
function approveTransfer() {
    $('#approvalModal').modal('show');
}

function rejectTransfer() {
    $('#rejectionModal').modal('show');
}

function completeTransfer() {
    $('#completeModal').modal('show');
}

function printTransfer() {
    window.print();
}

// Set max date for completion date to today
$(document).ready(function() {
    const today = new Date().toISOString().split('T')[0];
    $('#completion_date').attr('max', today);
});
</script>
