@extends('layouts.app')

@section('title', 'إدارة الاستقالات')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-user-times text-danger"></i>
                        إدارة الاستقالات
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item active">الاستقالات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button type="button" class="btn btn-info btn-sm" data-toggle="modal" data-target="#exportModal">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                    <a href="{{ route('employee-resignations.create') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i>
                        استقالة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                في الانتظار
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="pending-count">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                موافق عليها
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="approved-count">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                مرفوضة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="rejected-count">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                مكتملة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="completed-count">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-flag-checkered fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter"></i>
                البحث والتصفية
            </h6>
        </div>
        <div class="card-body">
            <form id="filter-form">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="employee_search">البحث عن موظف</label>
                            <input type="text" class="form-control" id="employee_search" name="employee_search" 
                                   placeholder="اسم الموظف أو رقم الموظف">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="status_filter">الحالة</label>
                            <select class="form-control" id="status_filter" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="pending">في الانتظار</option>
                                <option value="approved">موافق عليها</option>
                                <option value="rejected">مرفوضة</option>
                                <option value="completed">مكتملة</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="resignation_type_filter">نوع الاستقالة</label>
                            <select class="form-control" id="resignation_type_filter" name="resignation_type">
                                <option value="">جميع الأنواع</option>
                                <option value="voluntary">طوعية</option>
                                <option value="involuntary">غير طوعية</option>
                                <option value="retirement">تقاعد</option>
                                <option value="contract_end">انتهاء عقد</option>
                                <option value="mutual_agreement">اتفاق متبادل</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="department_filter">القسم</label>
                            <select class="form-control" id="department_filter" name="department_id">
                                <option value="">جميع الأقسام</option>
                                <!-- Will be populated via AJAX -->
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_from">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_to">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="notice_period_filter">فترة الإشعار</label>
                            <select class="form-control" id="notice_period_filter" name="notice_period">
                                <option value="">جميع الفترات</option>
                                <option value="short">قصيرة (أقل من 30 يوم)</option>
                                <option value="normal">عادية (30-60 يوم)</option>
                                <option value="long">طويلة (أكثر من 60 يوم)</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-flex">
                                <button type="button" class="btn btn-primary btn-sm mr-2" onclick="applyFilters()">
                                    <i class="fas fa-search"></i>
                                    بحث
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm" onclick="clearFilters()">
                                    <i class="fas fa-times"></i>
                                    مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Resignations Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-table"></i>
                قائمة الاستقالات
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="resignations-table" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>رقم الاستقالة</th>
                            <th>الموظف</th>
                            <th>القسم</th>
                            <th>نوع الاستقالة</th>
                            <th>تاريخ التقديم</th>
                            <th>آخر يوم عمل</th>
                            <th>فترة الإشعار</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded via DataTables AJAX -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Quick Actions Panel -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-bolt"></i>
                        إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <button type="button" class="btn btn-warning btn-block btn-sm" onclick="showPendingResignations()">
                                <i class="fas fa-clock"></i>
                                الاستقالات المعلقة
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-info btn-block btn-sm" onclick="showExpiringNotices()">
                                <i class="fas fa-calendar-times"></i>
                                فترات إشعار منتهية
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-success btn-block btn-sm" onclick="showHandoverPending()">
                                <i class="fas fa-handshake"></i>
                                تسليم معلق
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-secondary btn-block btn-sm" data-toggle="modal" data-target="#reportsModal">
                                <i class="fas fa-chart-bar"></i>
                                التقارير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-download text-info"></i>
                    تصدير الاستقالات
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-resignations.export') }}" method="GET" target="_blank">
                <div class="modal-body">
                    <div class="form-group">
                        <label>تنسيق التصدير</label>
                        <select class="form-control" name="format" required>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                            <option value="pdf">PDF</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>الحالة</label>
                        <select class="form-control" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="pending">في الانتظار</option>
                            <option value="approved">موافق عليها</option>
                            <option value="rejected">مرفوضة</option>
                            <option value="completed">مكتملة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>الفترة الزمنية</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="date" class="form-control" name="date_from" placeholder="من تاريخ">
                            </div>
                            <div class="col-6">
                                <input type="date" class="form-control" name="date_to" placeholder="إلى تاريخ">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>الحقول المراد تصديرها</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="fields[]" value="employee_info" checked>
                            <label class="form-check-label">معلومات الموظف</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="fields[]" value="resignation_details" checked>
                            <label class="form-check-label">تفاصيل الاستقالة</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="fields[]" value="handover_info">
                            <label class="form-check-label">معلومات التسليم</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="fields[]" value="exit_interview">
                            <label class="form-check-label">مقابلة الخروج</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reports Modal -->
<div class="modal fade" id="reportsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-bar text-secondary"></i>
                    تقارير الاستقالات
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-pie fa-3x text-primary mb-3"></i>
                                <h5>تقرير الاستقالات حسب النوع</h5>
                                <p class="text-muted">إحصائيات أنواع الاستقالات</p>
                                <a href="{{ route('employee-resignations.report', 'by-type') }}" target="_blank" class="btn btn-primary btn-sm">
                                    عرض التقرير
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-3x text-success mb-3"></i>
                                <h5>تقرير الاستقالات الشهرية</h5>
                                <p class="text-muted">اتجاهات الاستقالات عبر الوقت</p>
                                <a href="{{ route('employee-resignations.report', 'monthly') }}" target="_blank" class="btn btn-success btn-sm">
                                    عرض التقرير
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-building fa-3x text-warning mb-3"></i>
                                <h5>تقرير الاستقالات حسب القسم</h5>
                                <p class="text-muted">معدلات الاستقالة في الأقسام</p>
                                <a href="{{ route('employee-resignations.report', 'by-department') }}" target="_blank" class="btn btn-warning btn-sm">
                                    عرض التقرير
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-3x text-info mb-3"></i>
                                <h5>تقرير فترات الإشعار</h5>
                                <p class="text-muted">تحليل فترات الإشعار</p>
                                <a href="{{ route('employee-resignations.report', 'notice-periods') }}" target="_blank" class="btn btn-info btn-sm">
                                    عرض التقرير
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-tasks text-primary"></i>
                    إجراءات متعددة
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="bulk-actions-form">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label>الإجراء</label>
                        <select class="form-control" id="bulk_action" name="action" required>
                            <option value="">اختر الإجراء</option>
                            <option value="approve">الموافقة على المحدد</option>
                            <option value="reject">رفض المحدد</option>
                            <option value="export">تصدير المحدد</option>
                            <option value="delete">حذف المحدد</option>
                        </select>
                    </div>

                    <div id="bulk-notes-group" class="form-group" style="display: none;">
                        <label for="bulk_notes">ملاحظات</label>
                        <textarea class="form-control" id="bulk_notes" name="notes" rows="3"></textarea>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        سيتم تطبيق الإجراء على <span id="selected-count">0</span> استقالة محددة.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-check"></i>
                        تنفيذ الإجراء
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('styles')
<style>
/* Statistics cards styling */
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

.border-left-secondary {
    border-left: 0.25rem solid #858796 !important;
}

/* Card hover effects */
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease-in-out;
}

/* Statistics animation */
@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.stat-number {
    animation: countUp 0.6s ease-out;
}

/* Filter form styling */
.filter-form {
    background: linear-gradient(45deg, #f8f9fc, #ffffff);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

/* Table styling */
.table th {
    background: linear-gradient(45deg, #f8f9fc, #ffffff);
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    text-align: center;
    vertical-align: middle;
}

.table td {
    vertical-align: middle;
    text-align: center;
}

/* Status badges */
.status-badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: linear-gradient(45deg, #ffc107, #ffca2c);
    color: #856404;
    border: 1px solid #ffc107;
}

.status-approved {
    background: linear-gradient(45deg, #28a745, #34ce57);
    color: #155724;
    border: 1px solid #28a745;
}

.status-rejected {
    background: linear-gradient(45deg, #dc3545, #e4606d);
    color: #721c24;
    border: 1px solid #dc3545;
}

.status-completed {
    background: linear-gradient(45deg, #6c757d, #7d8a96);
    color: #383d41;
    border: 1px solid #6c757d;
}

/* Resignation type badges */
.type-voluntary {
    background: linear-gradient(45deg, #17a2b8, #20c9e7);
    color: #0c5460;
}

.type-involuntary {
    background: linear-gradient(45deg, #fd7e14, #ff922b);
    color: #8a4a00;
}

.type-retirement {
    background: linear-gradient(45deg, #6f42c1, #8662d1);
    color: #3d1a78;
}

.type-contract_end {
    background: linear-gradient(45deg, #20c997, #3dd5a7);
    color: #0f5132;
}

.type-mutual_agreement {
    background: linear-gradient(45deg, #6610f2, #7c3aed);
    color: #3d0a91;
}

/* Action buttons */
.action-btn {
    margin: 0 0.125rem;
    padding: 0.375rem 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease-in-out;
    border: none;
    font-size: 0.875rem;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.btn-view {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
}

.btn-edit {
    background: linear-gradient(45deg, #28a745, #1e7e34);
    color: white;
}

.btn-approve {
    background: linear-gradient(45deg, #28a745, #1e7e34);
    color: white;
}

.btn-reject {
    background: linear-gradient(45deg, #dc3545, #c82333);
    color: white;
}

.btn-complete {
    background: linear-gradient(45deg, #17a2b8, #138496);
    color: white;
}

/* Quick actions panel */
.quick-actions {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.quick-actions .btn {
    margin-bottom: 0.5rem;
    transition: all 0.2s ease-in-out;
    border: 2px solid transparent;
}

.quick-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

/* DataTables customization */
.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.375rem 0.75rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border-radius: 0.35rem;
    margin: 0 0.125rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border-color: #007bff;
    color: white !important;
}

/* Loading states */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 0.5rem;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 0.3rem solid #f3f3f3;
    border-top: 0.3rem solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal styling */
.modal-content {
    border-radius: 0.5rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    border: none;
}

.modal-header {
    background: linear-gradient(45deg, #f8f9fc, #ffffff);
    border-bottom: 2px solid #e3e6f0;
    border-radius: 0.5rem 0.5rem 0 0;
}

/* Checkbox styling */
.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.form-check-input:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Alert styling */
.alert {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .action-btn {
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
        margin: 0.125rem;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .quick-actions {
        padding: 1rem;
    }

    .card-body {
        padding: 1rem;
    }
}

/* Print styles */
@media print {
    .btn, .dropdown, nav, .sidebar, .modal, .quick-actions {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        break-inside: avoid;
        margin-bottom: 1rem;
    }

    .table {
        font-size: 0.75rem;
    }

    .table th, .table td {
        border: 1px solid #000 !important;
        padding: 0.25rem !important;
    }

    .status-badge {
        border: 1px solid #000 !important;
        color: #000 !important;
        background: transparent !important;
    }
}

/* Floating action button */
.fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease-in-out;
    z-index: 1000;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.2);
    color: white;
}

/* Notification badge */
.notification-badge {
    position: absolute;
    top: -0.5rem;
    right: -0.5rem;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
}

/* Pulse animation for urgent items */
@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

.urgent-item {
    animation: pulse 2s infinite;
}

/* Smooth transitions */
* {
    transition: all 0.2s ease-in-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
}
</style>
@endsection

@section('scripts')
<script>
let resignationsTable;
let selectedResignations = [];

$(document).ready(function() {
    // Initialize DataTable
    initializeDataTable();

    // Load statistics
    loadStatistics();

    // Load departments for filter
    loadDepartments();

    // Initialize event handlers
    initializeEventHandlers();

    // Auto-refresh every 5 minutes
    setInterval(function() {
        refreshData();
    }, 300000);

    // Initialize tooltips
    $('[title]').tooltip();
});

// Initialize DataTable
function initializeDataTable() {
    resignationsTable = $('#resignations-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("employee-resignations.data") }}',
            data: function(d) {
                d.employee_search = $('#employee_search').val();
                d.status = $('#status_filter').val();
                d.resignation_type = $('#resignation_type_filter').val();
                d.department_id = $('#department_filter').val();
                d.date_from = $('#date_from').val();
                d.date_to = $('#date_to').val();
                d.notice_period = $('#notice_period_filter').val();
            }
        },
        columns: [
            {
                data: 'id',
                name: 'id',
                render: function(data, type, row) {
                    return `
                        <div class="form-check">
                            <input class="form-check-input resignation-checkbox" type="checkbox"
                                   value="${data}" id="resignation_${data}">
                            <label class="form-check-label" for="resignation_${data}">
                                #${data}
                            </label>
                        </div>
                    `;
                }
            },
            {
                data: 'employee',
                name: 'employee.name',
                render: function(data, type, row) {
                    return `
                        <div class="d-flex align-items-center">
                            <img src="${data.avatar || '/images/default-avatar.png'}"
                                 class="rounded-circle mr-2" width="32" height="32">
                            <div>
                                <div class="font-weight-bold">${data.name}</div>
                                <small class="text-muted">${data.employee_code}</small>
                            </div>
                        </div>
                    `;
                }
            },
            {
                data: 'department',
                name: 'employee.department.name',
                render: function(data, type, row) {
                    return data ? data.name : '<span class="text-muted">غير محدد</span>';
                }
            },
            {
                data: 'resignation_type',
                name: 'resignation_type',
                render: function(data, type, row) {
                    const types = {
                        'voluntary': { text: 'طوعية', class: 'type-voluntary' },
                        'involuntary': { text: 'غير طوعية', class: 'type-involuntary' },
                        'retirement': { text: 'تقاعد', class: 'type-retirement' },
                        'contract_end': { text: 'انتهاء عقد', class: 'type-contract_end' },
                        'mutual_agreement': { text: 'اتفاق متبادل', class: 'type-mutual_agreement' }
                    };
                    const type = types[data] || { text: data, class: 'badge-secondary' };
                    return `<span class="badge status-badge ${type.class}">${type.text}</span>`;
                }
            },
            {
                data: 'resignation_date',
                name: 'resignation_date',
                render: function(data, type, row) {
                    return data ? moment(data).format('YYYY-MM-DD') : '-';
                }
            },
            {
                data: 'last_working_date',
                name: 'last_working_date',
                render: function(data, type, row) {
                    if (!data) return '-';
                    const date = moment(data);
                    const today = moment();
                    const diff = date.diff(today, 'days');

                    let badgeClass = 'badge-secondary';
                    let text = date.format('YYYY-MM-DD');

                    if (diff < 0) {
                        badgeClass = 'badge-danger';
                        text += ` (انتهى)`;
                    } else if (diff <= 7) {
                        badgeClass = 'badge-warning';
                        text += ` (${diff} أيام)`;
                    } else {
                        badgeClass = 'badge-info';
                        text += ` (${diff} أيام)`;
                    }

                    return `<span class="badge ${badgeClass}">${text}</span>`;
                }
            },
            {
                data: 'notice_period_days',
                name: 'notice_period_days',
                render: function(data, type, row) {
                    if (!data) return '-';

                    let badgeClass = 'badge-info';
                    if (data < 30) badgeClass = 'badge-warning';
                    else if (data > 60) badgeClass = 'badge-success';

                    return `<span class="badge ${badgeClass}">${data} يوم</span>`;
                }
            },
            {
                data: 'status',
                name: 'status',
                render: function(data, type, row) {
                    const statuses = {
                        'pending': { text: 'في الانتظار', class: 'status-pending' },
                        'approved': { text: 'موافق عليها', class: 'status-approved' },
                        'rejected': { text: 'مرفوضة', class: 'status-rejected' },
                        'completed': { text: 'مكتملة', class: 'status-completed' }
                    };
                    const status = statuses[data] || { text: data, class: 'badge-secondary' };
                    return `<span class="badge status-badge ${status.class}">${status.text}</span>`;
                }
            },
            {
                data: 'actions',
                name: 'actions',
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    let actions = `
                        <a href="/employee-affairs/resignations/${row.id}"
                           class="btn btn-view action-btn" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </a>
                    `;

                    if (row.status === 'pending') {
                        actions += `
                            <a href="/employee-affairs/resignations/${row.id}/edit"
                               class="btn btn-edit action-btn" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button onclick="approveResignation(${row.id})"
                                    class="btn btn-approve action-btn" title="موافقة">
                                <i class="fas fa-check"></i>
                            </button>
                            <button onclick="rejectResignation(${row.id})"
                                    class="btn btn-reject action-btn" title="رفض">
                                <i class="fas fa-times"></i>
                            </button>
                        `;
                    } else if (row.status === 'approved') {
                        actions += `
                            <button onclick="completeResignation(${row.id})"
                                    class="btn btn-complete action-btn" title="إكمال">
                                <i class="fas fa-flag-checkered"></i>
                            </button>
                        `;
                    }

                    return actions;
                }
            }
        ],
        order: [[4, 'desc']], // Order by resignation_date desc
        pageLength: 25,
        responsive: true,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
        },
        drawCallback: function() {
            // Initialize tooltips for new elements
            $('[title]').tooltip();

            // Update selected count
            updateSelectedCount();
        }
    });
}

// Load statistics
function loadStatistics() {
    $.ajax({
        url: '{{ route("employee-resignations.statistics") }}',
        method: 'GET',
        success: function(response) {
            animateCounter('#pending-count', response.pending);
            animateCounter('#approved-count', response.approved);
            animateCounter('#rejected-count', response.rejected);
            animateCounter('#completed-count', response.completed);
        },
        error: function() {
            $('#pending-count, #approved-count, #rejected-count, #completed-count')
                .html('<span class="text-danger">خطأ</span>');
        }
    });
}

// Animate counter
function animateCounter(selector, endValue) {
    const element = $(selector);
    const startValue = 0;
    const duration = 1000;
    const increment = endValue / (duration / 16);
    let currentValue = startValue;

    const timer = setInterval(function() {
        currentValue += increment;
        if (currentValue >= endValue) {
            currentValue = endValue;
            clearInterval(timer);
        }
        element.html(Math.floor(currentValue));
        element.addClass('stat-number');
    }, 16);
}

// Load departments
function loadDepartments() {
    $.ajax({
        url: '{{ route("api.departments") }}',
        method: 'GET',
        success: function(response) {
            const select = $('#department_filter');
            select.empty().append('<option value="">جميع الأقسام</option>');

            response.forEach(function(department) {
                select.append(`<option value="${department.id}">${department.name}</option>`);
            });
        }
    });
}

// Initialize event handlers
function initializeEventHandlers() {
    // Filter form submission
    $('#filter-form').on('submit', function(e) {
        e.preventDefault();
        applyFilters();
    });

    // Real-time search
    $('#employee_search').on('keyup', debounce(function() {
        applyFilters();
    }, 500));

    // Filter changes
    $('#status_filter, #resignation_type_filter, #department_filter, #notice_period_filter')
        .on('change', function() {
            applyFilters();
        });

    // Date range validation
    $('#date_from, #date_to').on('change', function() {
        validateDateRange();
        applyFilters();
    });

    // Select all checkbox
    $(document).on('change', '#select-all', function() {
        const isChecked = $(this).is(':checked');
        $('.resignation-checkbox').prop('checked', isChecked);
        updateSelectedResignations();
    });

    // Individual checkbox
    $(document).on('change', '.resignation-checkbox', function() {
        updateSelectedResignations();
    });

    // Bulk actions
    $('#bulk_action').on('change', function() {
        const action = $(this).val();
        if (action === 'approve' || action === 'reject') {
            $('#bulk-notes-group').show();
        } else {
            $('#bulk-notes-group').hide();
        }
    });

    // Bulk actions form
    $('#bulk-actions-form').on('submit', function(e) {
        e.preventDefault();
        executeBulkAction();
    });

    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        // Ctrl+N for new resignation
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            window.location.href = '{{ route("employee-resignations.create") }}';
        }

        // Ctrl+R for refresh
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            refreshData();
        }

        // Ctrl+E for export
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            $('#exportModal').modal('show');
        }

        // Delete key for bulk delete
        if (e.key === 'Delete' && selectedResignations.length > 0) {
            e.preventDefault();
            $('#bulk_action').val('delete');
            $('#bulkActionsModal').modal('show');
        }
    });
}

// Apply filters
function applyFilters() {
    resignationsTable.ajax.reload();
}

// Clear filters
function clearFilters() {
    $('#filter-form')[0].reset();
    applyFilters();
}

// Validate date range
function validateDateRange() {
    const dateFrom = $('#date_from').val();
    const dateTo = $('#date_to').val();

    if (dateFrom && dateTo && dateFrom > dateTo) {
        showAlert('error', 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
        $('#date_to').val('');
    }
}

// Update selected resignations
function updateSelectedResignations() {
    selectedResignations = [];
    $('.resignation-checkbox:checked').each(function() {
        selectedResignations.push($(this).val());
    });

    updateSelectedCount();

    // Show/hide bulk actions button
    if (selectedResignations.length > 0) {
        showBulkActionsButton();
    } else {
        hideBulkActionsButton();
    }
}

// Update selected count
function updateSelectedCount() {
    const count = selectedResignations.length;
    $('#selected-count').text(count);

    // Update select all checkbox state
    const totalCheckboxes = $('.resignation-checkbox').length;
    const checkedCheckboxes = $('.resignation-checkbox:checked').length;

    if (checkedCheckboxes === 0) {
        $('#select-all').prop('indeterminate', false).prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        $('#select-all').prop('indeterminate', false).prop('checked', true);
    } else {
        $('#select-all').prop('indeterminate', true);
    }
}

// Show bulk actions button
function showBulkActionsButton() {
    if ($('#bulk-actions-fab').length === 0) {
        $('body').append(`
            <button id="bulk-actions-fab" class="fab" data-toggle="modal" data-target="#bulkActionsModal">
                <i class="fas fa-tasks"></i>
                <span class="notification-badge">${selectedResignations.length}</span>
            </button>
        `);
    } else {
        $('#bulk-actions-fab .notification-badge').text(selectedResignations.length);
    }
}

// Hide bulk actions button
function hideBulkActionsButton() {
    $('#bulk-actions-fab').remove();
}

// Refresh data
function refreshData() {
    resignationsTable.ajax.reload(null, false);
    loadStatistics();
    showAlert('info', 'تم تحديث البيانات', 2000);
}

// Debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
// Quick action functions
function showPendingResignations() {
    $('#status_filter').val('pending');
    applyFilters();
    showAlert('info', 'تم تصفية الاستقالات المعلقة');
}

function showExpiringNotices() {
    // Custom filter for expiring notices
    $.ajax({
        url: '{{ route("employee-resignations.expiring-notices") }}',
        method: 'GET',
        success: function(response) {
            if (response.length === 0) {
                showAlert('info', 'لا توجد فترات إشعار منتهية');
                return;
            }

            // Update table with expiring notices
            resignationsTable.ajax.url('{{ route("employee-resignations.data") }}?expiring=true').load();
            showAlert('warning', `تم العثور على ${response.length} فترة إشعار منتهية`);
        }
    });
}

function showHandoverPending() {
    // Custom filter for pending handovers
    $.ajax({
        url: '{{ route("employee-resignations.handover-pending") }}',
        method: 'GET',
        success: function(response) {
            if (response.length === 0) {
                showAlert('info', 'لا توجد عمليات تسليم معلقة');
                return;
            }

            // Update table with handover pending
            resignationsTable.ajax.url('{{ route("employee-resignations.data") }}?handover_pending=true').load();
            showAlert('warning', `تم العثور على ${response.length} عملية تسليم معلقة`);
        }
    });
}

// Individual action functions
function approveResignation(id) {
    Swal.fire({
        title: 'الموافقة على الاستقالة',
        text: 'هل أنت متأكد من الموافقة على هذه الاستقالة؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، وافق',
        cancelButtonText: 'إلغاء',
        input: 'textarea',
        inputPlaceholder: 'ملاحظات الموافقة (اختيارية)...',
        inputAttributes: {
            'aria-label': 'ملاحظات الموافقة'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `{{ route("employee-resignations.approve", ":id") }}`.replace(':id', id),
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    approval_notes: result.value
                },
                success: function(response) {
                    showAlert('success', 'تم الموافقة على الاستقالة بنجاح');
                    resignationsTable.ajax.reload();
                    loadStatistics();
                },
                error: function(xhr) {
                    showAlert('error', 'حدث خطأ أثناء الموافقة على الاستقالة');
                }
            });
        }
    });
}

function rejectResignation(id) {
    Swal.fire({
        title: 'رفض الاستقالة',
        text: 'هل أنت متأكد من رفض هذه الاستقالة؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، ارفض',
        cancelButtonText: 'إلغاء',
        input: 'textarea',
        inputPlaceholder: 'سبب الرفض (مطلوب)...',
        inputAttributes: {
            'aria-label': 'سبب الرفض'
        },
        inputValidator: (value) => {
            if (!value || value.trim().length < 10) {
                return 'يجب إدخال سبب الرفض (10 أحرف على الأقل)';
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `{{ route("employee-resignations.reject", ":id") }}`.replace(':id', id),
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    rejection_reason: result.value
                },
                success: function(response) {
                    showAlert('success', 'تم رفض الاستقالة بنجاح');
                    resignationsTable.ajax.reload();
                    loadStatistics();
                },
                error: function(xhr) {
                    showAlert('error', 'حدث خطأ أثناء رفض الاستقالة');
                }
            });
        }
    });
}

function completeResignation(id) {
    Swal.fire({
        title: 'إكمال الاستقالة',
        text: 'هل أنت متأكد من إكمال هذه الاستقالة؟',
        icon: 'info',
        showCancelButton: true,
        confirmButtonColor: '#17a2b8',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، أكمل',
        cancelButtonText: 'إلغاء',
        html: `
            <div class="text-left">
                <div class="form-group">
                    <label for="final_settlement">مبلغ التسوية النهائية</label>
                    <input type="number" id="final_settlement" class="swal2-input"
                           placeholder="0.00" step="0.01" min="0">
                </div>
                <div class="form-group">
                    <label for="completion_notes">ملاحظات الإكمال</label>
                    <textarea id="completion_notes" class="swal2-textarea"
                              placeholder="ملاحظات حول إكمال الاستقالة..."></textarea>
                </div>
            </div>
        `,
        preConfirm: () => {
            return {
                final_settlement_amount: document.getElementById('final_settlement').value,
                completion_notes: document.getElementById('completion_notes').value
            };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `{{ route("employee-resignations.complete", ":id") }}`.replace(':id', id),
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    final_settlement_amount: result.value.final_settlement_amount,
                    completion_notes: result.value.completion_notes
                },
                success: function(response) {
                    showAlert('success', 'تم إكمال الاستقالة بنجاح');
                    resignationsTable.ajax.reload();
                    loadStatistics();
                },
                error: function(xhr) {
                    showAlert('error', 'حدث خطأ أثناء إكمال الاستقالة');
                }
            });
        }
    });
}

// Bulk actions
function executeBulkAction() {
    const action = $('#bulk_action').val();
    const notes = $('#bulk_notes').val();

    if (selectedResignations.length === 0) {
        showAlert('warning', 'يجب اختيار استقالة واحدة على الأقل');
        return;
    }

    const actionTexts = {
        'approve': 'الموافقة على',
        'reject': 'رفض',
        'export': 'تصدير',
        'delete': 'حذف'
    };

    const actionText = actionTexts[action] || action;

    Swal.fire({
        title: `${actionText} الاستقالات المحددة`,
        text: `هل أنت متأكد من ${actionText} ${selectedResignations.length} استقالة؟`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#007bff',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، تنفيذ',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '{{ route("employee-resignations.bulk-action") }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    action: action,
                    resignations: selectedResignations,
                    notes: notes
                },
                success: function(response) {
                    showAlert('success', `تم ${actionText} الاستقالات المحددة بنجاح`);
                    resignationsTable.ajax.reload();
                    loadStatistics();
                    selectedResignations = [];
                    $('.resignation-checkbox').prop('checked', false);
                    hideBulkActionsButton();
                    $('#bulkActionsModal').modal('hide');
                },
                error: function(xhr) {
                    showAlert('error', `حدث خطأ أثناء ${actionText} الاستقالات`);
                }
            });
        }
    });
}

// Alert function
function showAlert(type, message, duration = 5000) {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    const alertIcon = type === 'success' ? 'fa-check-circle' :
                     type === 'error' ? 'fa-exclamation-triangle' :
                     type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';

    // Remove existing alerts
    $('.alert-floating').remove();

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show"
             style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="fas ${alertIcon}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;

    $('body').append(alertHtml);

    // Auto-remove after specified duration
    setTimeout(function() {
        $('.alert-floating').fadeOut(function() {
            $(this).remove();
        });
    }, duration);
}

// Export functionality
$('#exportModal form').on('submit', function(e) {
    e.preventDefault();

    const formData = $(this).serialize();
    const url = $(this).attr('action') + '?' + formData;

    // Open export in new tab
    window.open(url, '_blank');

    $('#exportModal').modal('hide');
    showAlert('info', 'تم بدء عملية التصدير');
});

// Auto-save filter preferences
function saveFilterPreferences() {
    const filters = {
        employee_search: $('#employee_search').val(),
        status: $('#status_filter').val(),
        resignation_type: $('#resignation_type_filter').val(),
        department_id: $('#department_filter').val(),
        date_from: $('#date_from').val(),
        date_to: $('#date_to').val(),
        notice_period: $('#notice_period_filter').val()
    };

    localStorage.setItem('resignation_filters', JSON.stringify(filters));
}

// Load filter preferences
function loadFilterPreferences() {
    const savedFilters = localStorage.getItem('resignation_filters');
    if (savedFilters) {
        const filters = JSON.parse(savedFilters);

        Object.keys(filters).forEach(key => {
            if (filters[key]) {
                $(`#${key}`).val(filters[key]);
            }
        });
    }
}

// Save preferences on filter change
$('#filter-form input, #filter-form select').on('change', function() {
    saveFilterPreferences();
});

// Load preferences on page load
$(document).ready(function() {
    loadFilterPreferences();
});

// Print functionality
function printTable() {
    const printWindow = window.open('', '_blank');
    const tableHtml = $('#resignations-table').parent().html();

    printWindow.document.write(`
        <html>
        <head>
            <title>قائمة الاستقالات</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #000; padding: 8px; text-align: center; }
                th { background-color: #f0f0f0; }
                .btn { display: none; }
                @media print {
                    .btn, .form-check { display: none !important; }
                }
            </style>
        </head>
        <body>
            <h2>قائمة الاستقالات</h2>
            <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
            ${tableHtml}
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}

// Add print button to page
$(document).ready(function() {
    $('.card-header').first().append(`
        <button type="button" class="btn btn-secondary btn-sm float-left" onclick="printTable()">
            <i class="fas fa-print"></i>
            طباعة
        </button>
    `);
});
@endsection
