@extends('layouts.app')

@section('title', 'تعديل الاستقالة')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-user-times text-danger"></i>
                        تعديل الاستقالة #{{ $resignation->id }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-resignations.index') }}">الاستقالات</a></li>
                            <li class="breadcrumb-item active">تعديل الاستقالة</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('employee-resignations.show', $resignation->id) }}" class="btn btn-info btn-sm">
                        <i class="fas fa-eye"></i>
                        عرض التفاصيل
                    </a>
                    <a href="{{ route('employee-resignations.index') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-right"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Warning -->
    @if($resignation->status !== 'pending')
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                @if($resignation->status === 'approved')
                    هذه الاستقالة تم الموافقة عليها. يمكن تعديل بعض الحقول فقط.
                @elseif($resignation->status === 'rejected')
                    هذه الاستقالة تم رفضها. يمكن إعادة تقديمها بعد التعديل.
                @elseif($resignation->status === 'completed')
                    هذه الاستقالة مكتملة. لا يمكن تعديل المحتوى الأساسي.
                @endif
            </div>
        </div>
    </div>
    @endif

    <form action="{{ route('employee-resignations.update', $resignation->id) }}" method="POST" id="resignation-form" enctype="multipart/form-data">
        @csrf
        @method('PUT')
        
        <div class="row">
            <!-- Main Form -->
            <div class="col-lg-8">
                <!-- Employee Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-user"></i>
                            معلومات الموظف
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>اسم الموظف</label>
                                    <div class="card bg-light">
                                        <div class="card-body py-2">
                                            <div class="d-flex align-items-center">
                                                <img src="{{ $resignation->employee->avatar ?? '/images/default-avatar.png' }}" 
                                                     class="rounded-circle mr-3" width="40" height="40" alt="صورة الموظف">
                                                <div>
                                                    <strong>{{ $resignation->employee->name }}</strong><br>
                                                    <small class="text-muted">{{ $resignation->employee->employee_code }} - {{ $resignation->employee->department->name ?? 'غير محدد' }}</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>المنصب الحالي</label>
                                    <input type="text" class="form-control" value="{{ $resignation->employee->position->title ?? 'غير محدد' }}" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>تاريخ التوظيف</label>
                                    <input type="text" class="form-control" value="{{ $resignation->employee->hire_date ? $resignation->employee->hire_date->format('Y-m-d') : 'غير محدد' }}" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>سنوات الخدمة</label>
                                    <input type="text" class="form-control" value="{{ $resignation->employee->hire_date ? $resignation->employee->hire_date->diffInYears(now()) : 'غير محدد' }} سنة" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Resignation Details -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-danger">
                            <i class="fas fa-file-alt"></i>
                            تفاصيل الاستقالة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="resignation_type">نوع الاستقالة <span class="text-danger">*</span></label>
                                    <select class="form-control @error('resignation_type') is-invalid @enderror" 
                                            id="resignation_type" name="resignation_type" required 
                                            {{ $resignation->status === 'completed' ? 'disabled' : '' }}>
                                        <option value="">اختر نوع الاستقالة</option>
                                        <option value="voluntary" {{ $resignation->resignation_type === 'voluntary' ? 'selected' : '' }}>طوعية</option>
                                        <option value="involuntary" {{ $resignation->resignation_type === 'involuntary' ? 'selected' : '' }}>غير طوعية</option>
                                        <option value="retirement" {{ $resignation->resignation_type === 'retirement' ? 'selected' : '' }}>تقاعد</option>
                                        <option value="contract_end" {{ $resignation->resignation_type === 'contract_end' ? 'selected' : '' }}>انتهاء عقد</option>
                                        <option value="mutual_agreement" {{ $resignation->resignation_type === 'mutual_agreement' ? 'selected' : '' }}>اتفاق متبادل</option>
                                    </select>
                                    @error('resignation_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="resignation_date">تاريخ تقديم الاستقالة <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('resignation_date') is-invalid @enderror" 
                                           id="resignation_date" name="resignation_date" 
                                           value="{{ $resignation->resignation_date ? $resignation->resignation_date->format('Y-m-d') : '' }}" 
                                           required {{ $resignation->status === 'completed' ? 'readonly' : '' }}>
                                    @error('resignation_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="last_working_date">آخر يوم عمل <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('last_working_date') is-invalid @enderror" 
                                           id="last_working_date" name="last_working_date" 
                                           value="{{ $resignation->last_working_date ? $resignation->last_working_date->format('Y-m-d') : '' }}" 
                                           required {{ $resignation->status === 'approved' || $resignation->status === 'completed' ? 'readonly' : '' }}>
                                    @error('last_working_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="notice_period">فترة الإشعار (أيام)</label>
                                    <input type="number" class="form-control @error('notice_period') is-invalid @enderror" 
                                           id="notice_period" name="notice_period" min="0" max="365"
                                           value="{{ $resignation->notice_period }}" 
                                           {{ $resignation->status === 'completed' ? 'readonly' : '' }}>
                                    <small class="form-text text-muted">سيتم حساب فترة الإشعار تلقائياً بناءً على التواريخ</small>
                                    @error('notice_period')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="reason">سبب الاستقالة <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('reason') is-invalid @enderror" 
                                      id="reason" name="reason" rows="4" 
                                      placeholder="اشرح سبب الاستقالة بالتفصيل..." 
                                      required {{ $resignation->status === 'completed' ? 'readonly' : '' }}>{{ $resignation->reason }}</textarea>
                            <div class="form-text">
                                <small class="text-muted">
                                    <span id="reason-count">{{ strlen($resignation->reason) }}</span>/1000 حرف
                                </small>
                            </div>
                            @error('reason')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="additional_notes">ملاحظات إضافية</label>
                            <textarea class="form-control @error('additional_notes') is-invalid @enderror" 
                                      id="additional_notes" name="additional_notes" rows="3" 
                                      placeholder="أي ملاحظات أو تفاصيل إضافية..."
                                      {{ $resignation->status === 'completed' ? 'readonly' : '' }}>{{ $resignation->additional_notes }}</textarea>
                            @error('additional_notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Handover Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-info">
                            <i class="fas fa-handshake"></i>
                            معلومات التسليم
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="handover_to_employee_id">تسليم المهام إلى</label>
                                    <select class="form-control select2 @error('handover_to_employee_id') is-invalid @enderror" 
                                            id="handover_to_employee_id" name="handover_to_employee_id"
                                            {{ $resignation->status === 'completed' ? 'disabled' : '' }}>
                                        <option value="">اختر الموظف</option>
                                        @if($resignation->handoverToEmployee)
                                            <option value="{{ $resignation->handoverToEmployee->id }}" selected>
                                                {{ $resignation->handoverToEmployee->name }} - {{ $resignation->handoverToEmployee->employee_code }}
                                            </option>
                                        @endif
                                    </select>
                                    @error('handover_to_employee_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="handover_completion_date">تاريخ اكتمال التسليم</label>
                                    <input type="date" class="form-control @error('handover_completion_date') is-invalid @enderror" 
                                           id="handover_completion_date" name="handover_completion_date" 
                                           value="{{ $resignation->handover_completion_date ? $resignation->handover_completion_date->format('Y-m-d') : '' }}"
                                           {{ $resignation->status === 'completed' ? 'readonly' : '' }}>
                                    @error('handover_completion_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="handover_notes">ملاحظات التسليم</label>
                            <textarea class="form-control @error('handover_notes') is-invalid @enderror" 
                                      id="handover_notes" name="handover_notes" rows="3" 
                                      placeholder="تفاصيل المهام والمسؤوليات المسلمة..."
                                      {{ $resignation->status === 'completed' ? 'readonly' : '' }}>{{ $resignation->handover_notes }}</textarea>
                            @error('handover_notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Exit Interview -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-warning">
                            <i class="fas fa-comments"></i>
                            مقابلة الخروج
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="exit_interview_date">تاريخ مقابلة الخروج</label>
                                    <input type="date" class="form-control @error('exit_interview_date') is-invalid @enderror" 
                                           id="exit_interview_date" name="exit_interview_date" 
                                           value="{{ $resignation->exit_interview_date ? $resignation->exit_interview_date->format('Y-m-d') : '' }}">
                                    @error('exit_interview_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="exit_interview_conducted_by">أجريت بواسطة</label>
                                    <input type="text" class="form-control @error('exit_interview_conducted_by') is-invalid @enderror" 
                                           id="exit_interview_conducted_by" name="exit_interview_conducted_by" 
                                           value="{{ $resignation->exit_interview_conducted_by }}"
                                           placeholder="اسم الشخص الذي أجرى المقابلة">
                                    @error('exit_interview_conducted_by')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="exit_interview_notes">ملاحظات مقابلة الخروج</label>
                            <textarea class="form-control @error('exit_interview_notes') is-invalid @enderror" 
                                      id="exit_interview_notes" name="exit_interview_notes" rows="4" 
                                      placeholder="ملخص مقابلة الخروج والتوصيات...">{{ $resignation->exit_interview_notes }}</textarea>
                            @error('exit_interview_notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Side Panel -->
            <div class="col-lg-4">
                <!-- Status Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-info">
                            <i class="fas fa-info-circle"></i>
                            معلومات الحالة
                        </h6>
                    </div>
                    <div class="card-body">
                        <p><strong>الحالة الحالية:</strong></p>
                        <p class="mb-3">
                            @switch($resignation->status)
                                @case('pending')
                                    <span class="badge badge-warning">في الانتظار</span>
                                    @break
                                @case('approved')
                                    <span class="badge badge-success">موافق عليها</span>
                                    @break
                                @case('rejected')
                                    <span class="badge badge-danger">مرفوضة</span>
                                    @break
                                @case('completed')
                                    <span class="badge badge-secondary">مكتملة</span>
                                    @break
                            @endswitch
                        </p>

                        <p><strong>تاريخ الإنشاء:</strong></p>
                        <p class="text-muted mb-3">{{ $resignation->created_at->format('Y-m-d H:i') }}</p>

                        @if($resignation->approved_at)
                            <p><strong>تاريخ الموافقة:</strong></p>
                            <p class="text-muted mb-3">{{ $resignation->approved_at->format('Y-m-d H:i') }}</p>
                        @endif

                        @if($resignation->rejected_at)
                            <p><strong>تاريخ الرفض:</strong></p>
                            <p class="text-muted mb-3">{{ $resignation->rejected_at->format('Y-m-d H:i') }}</p>
                        @endif

                        <!-- Notice Period Calculation -->
                        @if($resignation->resignation_date && $resignation->last_working_date)
                            @php
                                $noticeDays = $resignation->resignation_date->diffInDays($resignation->last_working_date);
                            @endphp
                            <p><strong>فترة الإشعار المحسوبة:</strong></p>
                            <p class="text-muted">{{ $noticeDays }} يوم</p>
                        @endif
                    </div>
                </div>

                <!-- Documents -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-paperclip"></i>
                            المرفقات
                        </h6>
                    </div>
                    <div class="card-body">
                        @if($resignation->status !== 'completed')
                        <div class="form-group">
                            <label for="resignation_letter">خطاب الاستقالة</label>
                            <input type="file" class="form-control-file @error('resignation_letter') is-invalid @enderror" 
                                   id="resignation_letter" name="resignation_letter" accept=".pdf,.doc,.docx">
                            <small class="form-text text-muted">PDF, DOC, DOCX (حد أقصى 5MB)</small>
                            @error('resignation_letter')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        @endif

                        @if($resignation->resignation_letter_path)
                        <div class="mb-3">
                            <p><strong>خطاب الاستقالة الحالي:</strong></p>
                            <a href="{{ Storage::url($resignation->resignation_letter_path) }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-file-pdf"></i>
                                عرض الخطاب
                            </a>
                        </div>
                        @endif

                        @if($resignation->status !== 'completed')
                        <div class="form-group">
                            <label for="additional_documents">مستندات إضافية</label>
                            <input type="file" class="form-control-file @error('additional_documents') is-invalid @enderror" 
                                   id="additional_documents" name="additional_documents[]" multiple accept=".pdf,.doc,.docx,.jpg,.png">
                            <small class="form-text text-muted">يمكن رفع عدة ملفات (حد أقصى 5MB لكل ملف)</small>
                            @error('additional_documents')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="card shadow">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            @if($resignation->status !== 'completed')
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ التعديلات
                            </button>
                            @endif
                            
                            <a href="{{ route('employee-resignations.show', $resignation->id) }}" class="btn btn-info">
                                <i class="fas fa-eye"></i>
                                عرض التفاصيل
                            </a>
                            
                            <a href="{{ route('employee-resignations.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@section('styles')
<style>
/* Status-based styling */
.status-pending {
    border-left: 4px solid #ffc107;
    background: rgba(255, 193, 7, 0.1);
}

.status-approved {
    border-left: 4px solid #28a745;
    background: rgba(40, 167, 69, 0.1);
}

.status-rejected {
    border-left: 4px solid #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.status-completed {
    border-left: 4px solid #6c757d;
    background: rgba(108, 117, 125, 0.1);
}

/* Employee card styling */
.employee-card {
    transition: transform 0.2s ease-in-out;
}

.employee-card:hover {
    transform: translateY(-2px);
}

/* Form section headers */
.card-header {
    background: linear-gradient(45deg, #f8f9fc, #ffffff);
    border-bottom: 2px solid #e3e6f0;
}

/* Notice period calculation */
.notice-period-info {
    background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
    border: 1px solid #bbdefb;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 0.5rem;
}

/* File upload styling */
.file-upload-area {
    border: 2px dashed #d1d3e2;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background: #f8f9fc;
}

.file-upload-area:hover {
    border-color: #5a5c69;
    background: #ffffff;
}

.file-upload-area.dragover {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.1);
}

/* Character counter */
.char-counter {
    font-size: 0.875rem;
    color: #6c757d;
}

.char-counter.warning {
    color: #ffc107;
}

.char-counter.danger {
    color: #dc3545;
}

/* Handover section */
.handover-section {
    background: linear-gradient(45deg, #fff3cd, #ffffff);
    border: 1px solid #ffeaa7;
    border-radius: 0.5rem;
    padding: 1rem;
}

/* Exit interview section */
.exit-interview-section {
    background: linear-gradient(45deg, #d4edda, #ffffff);
    border: 1px solid #c3e6cb;
    border-radius: 0.5rem;
    padding: 1rem;
}

/* Action buttons */
.action-buttons .btn {
    margin-bottom: 0.5rem;
    transition: all 0.2s ease-in-out;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* Readonly field styling */
.form-control[readonly] {
    background-color: #f8f9fc;
    border-color: #e3e6f0;
    color: #6c757d;
}

/* Badge styling */
.badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
}

/* Timeline styling for status changes */
.status-timeline {
    position: relative;
    padding-left: 30px;
}

.status-timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item {
    position: relative;
    margin-bottom: 1rem;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e3e6f0;
}

.timeline-marker.active {
    background: #28a745;
    box-shadow: 0 0 0 2px #28a745;
}

.timeline-marker.pending {
    background: #ffc107;
    box-shadow: 0 0 0 2px #ffc107;
}

.timeline-marker.rejected {
    background: #dc3545;
    box-shadow: 0 0 0 2px #dc3545;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }

    .btn-block {
        margin-bottom: 0.5rem;
    }

    .timeline-item {
        font-size: 0.875rem;
    }
}

/* Print styles */
@media print {
    .btn, .card-header .dropdown, nav, .sidebar {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        break-inside: avoid;
        margin-bottom: 1rem;
    }

    .form-control {
        border: none !important;
        border-bottom: 1px solid #000 !important;
        background: transparent !important;
        padding: 0.25rem 0 !important;
    }

    .badge {
        border: 1px solid #000 !important;
        color: #000 !important;
        background: transparent !important;
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

/* Form validation styling */
.was-validated .form-control:valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.17z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6.4.4.4-.4M5.8 8.4l.4-.4.4.4m-.4-3.2v2.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Select2 customization */
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(1.5em + 0.75rem + 2px);
    border: 1px solid #d1d3e2;
}

.select2-container--bootstrap4 .select2-selection--single:focus {
    border-color: #5a5c69;
    box-shadow: 0 0 0 0.2rem rgba(90, 92, 105, 0.25);
}

/* Tooltip styling */
.tooltip-inner {
    background-color: #5a5c69;
    color: #fff;
    border-radius: 0.25rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}
</style>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2 for handover employee
    $('#handover_to_employee_id').select2({
        theme: 'bootstrap4',
        placeholder: 'اختر الموظف...',
        allowClear: true,
        ajax: {
            url: '{{ route("employees.search") }}',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    q: params.term,
                    page: params.page,
                    exclude: {{ $resignation->employee_id }} // Exclude the resigning employee
                };
            },
            processResults: function(data, params) {
                params.page = params.page || 1;
                return {
                    results: data.data,
                    pagination: {
                        more: (params.page * 30) < data.total
                    }
                };
            },
            cache: true
        }
    });

    // Character counter for reason field
    $('#reason').on('input', function() {
        const maxLength = 1000;
        const currentLength = $(this).val().length;
        const counter = $('#reason-count');

        counter.text(currentLength);

        if (currentLength > maxLength * 0.9) {
            counter.addClass('danger').removeClass('warning');
        } else if (currentLength > maxLength * 0.7) {
            counter.addClass('warning').removeClass('danger');
        } else {
            counter.removeClass('warning danger');
        }
    });

    // Auto-calculate notice period
    $('#resignation_date, #last_working_date').on('change', function() {
        calculateNoticePeriod();
    });

    // Form validation
    $('#resignation-form').on('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            return false;
        }

        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true);
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');

        // Re-enable button after 10 seconds (fallback)
        setTimeout(function() {
            submitBtn.prop('disabled', false);
            submitBtn.html(originalText);
        }, 10000);
    });

    // File upload handling
    setupFileUpload();

    // Initialize tooltips
    $('[title]').tooltip();

    // Auto-save draft functionality
    let autoSaveTimer;
    $('input, textarea, select').on('change input', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(function() {
            saveDraft();
        }, 30000); // Auto-save after 30 seconds of inactivity
    });

    // Warn before leaving with unsaved changes
    let formChanged = false;
    $('input, textarea, select').on('change', function() {
        formChanged = true;
    });

    $(window).on('beforeunload', function(e) {
        if (formChanged) {
            const message = 'لديك تغييرات غير محفوظة. هل تريد المغادرة؟';
            e.returnValue = message;
            return message;
        }
    });

    $('#resignation-form').on('submit', function() {
        formChanged = false;
    });
});

// Calculate notice period
function calculateNoticePeriod() {
    const resignationDate = $('#resignation_date').val();
    const lastWorkingDate = $('#last_working_date').val();

    if (resignationDate && lastWorkingDate) {
        const startDate = new Date(resignationDate);
        const endDate = new Date(lastWorkingDate);

        if (endDate >= startDate) {
            const timeDiff = endDate.getTime() - startDate.getTime();
            const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

            $('#notice_period').val(daysDiff);

            // Show notice period info
            showNoticePeriodInfo(daysDiff);
        } else {
            showAlert('warning', 'تاريخ آخر يوم عمل يجب أن يكون بعد تاريخ تقديم الاستقالة');
        }
    }
}

// Show notice period information
function showNoticePeriodInfo(days) {
    const infoHtml = `
        <div class="notice-period-info mt-2">
            <i class="fas fa-info-circle text-info"></i>
            <strong>فترة الإشعار المحسوبة: ${days} يوم</strong>
            <br>
            <small class="text-muted">
                ${days < 30 ? 'فترة إشعار قصيرة - قد تحتاج موافقة خاصة' :
                  days > 90 ? 'فترة إشعار طويلة - مناسبة للمناصب العليا' :
                  'فترة إشعار مناسبة'}
            </small>
        </div>
    `;

    $('.notice-period-info').remove();
    $('#notice_period').parent().append(infoHtml);
}

// Form validation
function validateForm() {
    let isValid = true;

    // Required fields validation
    $('[required]').each(function() {
        if (!$(this).val().trim()) {
            $(this).addClass('is-invalid');
            isValid = false;
        } else {
            $(this).removeClass('is-invalid');
        }
    });

    // Date validation
    const resignationDate = new Date($('#resignation_date').val());
    const lastWorkingDate = new Date($('#last_working_date').val());

    if (resignationDate && lastWorkingDate && lastWorkingDate < resignationDate) {
        $('#last_working_date').addClass('is-invalid');
        showAlert('error', 'تاريخ آخر يوم عمل يجب أن يكون بعد تاريخ تقديم الاستقالة');
        isValid = false;
    }

    // Reason length validation
    const reason = $('#reason').val();
    if (reason.length > 1000) {
        $('#reason').addClass('is-invalid');
        showAlert('error', 'سبب الاستقالة يجب ألا يتجاوز 1000 حرف');
        isValid = false;
    }

    return isValid;
}

// File upload setup
function setupFileUpload() {
    // Drag and drop functionality
    $('.file-upload-area').on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });

    $('.file-upload-area').on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });

    $('.file-upload-area').on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');

        const files = e.originalEvent.dataTransfer.files;
        handleFileSelection(files);
    });

    // File input change
    $('input[type="file"]').on('change', function() {
        const files = this.files;
        handleFileSelection(files);
    });
}

// Handle file selection
function handleFileSelection(files) {
    for (let i = 0; i < files.length; i++) {
        const file = files[i];

        // Validate file size (5MB max)
        if (file.size > 5 * 1024 * 1024) {
            showAlert('error', `الملف ${file.name} كبير جداً. الحد الأقصى 5MB`);
            continue;
        }

        // Validate file type
        const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'];
        if (!allowedTypes.includes(file.type)) {
            showAlert('error', `نوع الملف ${file.name} غير مدعوم`);
            continue;
        }

        // Show file preview
        showFilePreview(file);
    }
}

// Show file preview
function showFilePreview(file) {
    const previewHtml = `
        <div class="file-preview mb-2 p-2 border rounded">
            <div class="d-flex align-items-center">
                <i class="fas fa-file-alt text-primary mr-2"></i>
                <div class="flex-grow-1">
                    <strong>${file.name}</strong><br>
                    <small class="text-muted">${(file.size / 1024 / 1024).toFixed(2)} MB</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFilePreview(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `;

    $('.file-previews').append(previewHtml);
}

// Remove file preview
function removeFilePreview(button) {
    $(button).closest('.file-preview').remove();
}

// Save draft
function saveDraft() {
    const formData = new FormData($('#resignation-form')[0]);
    formData.append('_method', 'PUT');
    formData.append('save_draft', '1');

    $.ajax({
        url: $('#resignation-form').attr('action'),
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                showAlert('info', 'تم حفظ المسودة تلقائياً', 2000);
            }
        },
        error: function() {
            // Silently fail for auto-save
        }
    });
}

// Show alert function
function showAlert(type, message, duration = 5000) {
    const alertClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : type === 'warning' ? 'alert-warning' : 'alert-info';
    const alertIcon = type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-triangle' : type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="fas ${alertIcon}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;

    $('body').append(alertHtml);

    // Auto-remove after specified duration
    setTimeout(function() {
        $('.alert').fadeOut(function() {
            $(this).remove();
        });
    }, duration);
}

// Keyboard shortcuts
$(document).on('keydown', function(e) {
    // Ctrl+S for save
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        $('#resignation-form').submit();
    }

    // Ctrl+D for save draft
    if (e.ctrlKey && e.key === 'd') {
        e.preventDefault();
        saveDraft();
    }

    // Escape to go back
    if (e.key === 'Escape') {
        if (confirm('هل تريد العودة للقائمة؟ قد تفقد التغييرات غير المحفوظة.')) {
            window.location.href = '{{ route("employee-resignations.index") }}';
        }
    }
});
</script>
@endsection
