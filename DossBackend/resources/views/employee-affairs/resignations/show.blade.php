@extends('layouts.app')

@section('title', 'تفاصيل الاستقالة')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-user-times text-danger"></i>
                        تفاصيل الاستقالة #{{ $resignation->id }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-resignations.index') }}">الاستقالات</a></li>
                            <li class="breadcrumb-item active">تفاصيل الاستقالة</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <div class="dropdown">
                        <button class="btn btn-primary dropdown-toggle btn-sm" type="button" data-toggle="dropdown">
                            <i class="fas fa-cog"></i>
                            إجراءات
                        </button>
                        <div class="dropdown-menu">
                            @if($resignation->status === 'pending')
                                <a class="dropdown-item" href="{{ route('employee-resignations.edit', $resignation->id) }}">
                                    <i class="fas fa-edit text-primary"></i>
                                    تعديل الاستقالة
                                </a>
                                <div class="dropdown-divider"></div>
                                <button type="button" class="dropdown-item" data-toggle="modal" data-target="#approveModal">
                                    <i class="fas fa-check text-success"></i>
                                    الموافقة على الاستقالة
                                </button>
                                <button type="button" class="dropdown-item" data-toggle="modal" data-target="#rejectModal">
                                    <i class="fas fa-times text-danger"></i>
                                    رفض الاستقالة
                                </button>
                            @endif
                            
                            @if($resignation->status === 'approved')
                                <button type="button" class="dropdown-item" data-toggle="modal" data-target="#completeModal">
                                    <i class="fas fa-flag-checkered text-info"></i>
                                    إكمال الاستقالة
                                </button>
                            @endif
                            
                            <div class="dropdown-divider"></div>
                            <button type="button" class="dropdown-item" onclick="printResignation()">
                                <i class="fas fa-print text-secondary"></i>
                                طباعة التفاصيل
                            </button>
                            <button type="button" class="dropdown-item" data-toggle="modal" data-target="#exportModal">
                                <i class="fas fa-download text-info"></i>
                                تصدير PDF
                            </button>
                        </div>
                    </div>
                    <a href="{{ route('employee-resignations.index') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-right"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Alert -->
    <div class="row mb-4">
        <div class="col-12">
            @switch($resignation->status)
                @case('pending')
                    <div class="alert alert-warning">
                        <i class="fas fa-clock"></i>
                        <strong>في الانتظار:</strong> هذه الاستقالة في انتظار المراجعة والموافقة.
                    </div>
                    @break
                @case('approved')
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <strong>موافق عليها:</strong> تم الموافقة على هذه الاستقالة في {{ $resignation->approved_at->format('Y-m-d H:i') }}.
                    </div>
                    @break
                @case('rejected')
                    <div class="alert alert-danger">
                        <i class="fas fa-times-circle"></i>
                        <strong>مرفوضة:</strong> تم رفض هذه الاستقالة في {{ $resignation->rejected_at->format('Y-m-d H:i') }}.
                        @if($resignation->rejection_reason)
                            <br><strong>سبب الرفض:</strong> {{ $resignation->rejection_reason }}
                        @endif
                    </div>
                    @break
                @case('completed')
                    <div class="alert alert-secondary">
                        <i class="fas fa-flag-checkered"></i>
                        <strong>مكتملة:</strong> تم إكمال جميع إجراءات الاستقالة في {{ $resignation->completed_at->format('Y-m-d H:i') }}.
                    </div>
                    @break
            @endswitch
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Employee Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user"></i>
                        معلومات الموظف
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <img src="{{ $resignation->employee->avatar ?? '/images/default-avatar.png' }}" 
                                 class="rounded-circle mb-3" width="100" height="100" alt="صورة الموظف">
                        </div>
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>الاسم:</strong> {{ $resignation->employee->name }}</p>
                                    <p><strong>رقم الموظف:</strong> {{ $resignation->employee->employee_code }}</p>
                                    <p><strong>القسم:</strong> {{ $resignation->employee->department->name ?? 'غير محدد' }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>المنصب:</strong> {{ $resignation->employee->position->title ?? 'غير محدد' }}</p>
                                    <p><strong>تاريخ التوظيف:</strong> {{ $resignation->employee->hire_date ? $resignation->employee->hire_date->format('Y-m-d') : 'غير محدد' }}</p>
                                    <p><strong>سنوات الخدمة:</strong> {{ $resignation->employee->hire_date ? $resignation->employee->hire_date->diffInYears(now()) : 'غير محدد' }} سنة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resignation Details -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-file-alt"></i>
                        تفاصيل الاستقالة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>نوع الاستقالة:</strong></p>
                            <p class="mb-3">
                                @switch($resignation->resignation_type)
                                    @case('voluntary')
                                        <span class="badge badge-info">طوعية</span>
                                        @break
                                    @case('involuntary')
                                        <span class="badge badge-warning">غير طوعية</span>
                                        @break
                                    @case('retirement')
                                        <span class="badge badge-secondary">تقاعد</span>
                                        @break
                                    @case('contract_end')
                                        <span class="badge badge-primary">انتهاء عقد</span>
                                        @break
                                    @case('mutual_agreement')
                                        <span class="badge badge-success">اتفاق متبادل</span>
                                        @break
                                @endswitch
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>تاريخ تقديم الاستقالة:</strong></p>
                            <p class="mb-3">{{ $resignation->resignation_date ? $resignation->resignation_date->format('Y-m-d') : 'غير محدد' }}</p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>آخر يوم عمل:</strong></p>
                            <p class="mb-3">{{ $resignation->last_working_date ? $resignation->last_working_date->format('Y-m-d') : 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>فترة الإشعار:</strong></p>
                            <p class="mb-3">
                                @if($resignation->notice_period)
                                    {{ $resignation->notice_period }} يوم
                                    @if($resignation->resignation_date && $resignation->last_working_date)
                                        @php
                                            $calculatedDays = $resignation->resignation_date->diffInDays($resignation->last_working_date);
                                        @endphp
                                        @if($calculatedDays != $resignation->notice_period)
                                            <small class="text-muted">(محسوبة: {{ $calculatedDays }} يوم)</small>
                                        @endif
                                    @endif
                                @else
                                    غير محدد
                                @endif
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <p><strong>سبب الاستقالة:</strong></p>
                            <div class="bg-light p-3 rounded">
                                {{ $resignation->reason }}
                            </div>
                        </div>
                    </div>

                    @if($resignation->additional_notes)
                    <div class="row mt-3">
                        <div class="col-12">
                            <p><strong>ملاحظات إضافية:</strong></p>
                            <div class="bg-light p-3 rounded">
                                {{ $resignation->additional_notes }}
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Handover Information -->
            @if($resignation->handover_to_employee_id || $resignation->handover_notes)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-handshake"></i>
                        معلومات التسليم
                    </h6>
                </div>
                <div class="card-body">
                    @if($resignation->handoverToEmployee)
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>تم التسليم إلى:</strong></p>
                            <div class="d-flex align-items-center">
                                <img src="{{ $resignation->handoverToEmployee->avatar ?? '/images/default-avatar.png' }}" 
                                     class="rounded-circle mr-3" width="40" height="40" alt="صورة الموظف">
                                <div>
                                    <strong>{{ $resignation->handoverToEmployee->name }}</strong><br>
                                    <small class="text-muted">{{ $resignation->handoverToEmployee->employee_code }} - {{ $resignation->handoverToEmployee->department->name ?? 'غير محدد' }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            @if($resignation->handover_completion_date)
                            <p><strong>تاريخ اكتمال التسليم:</strong></p>
                            <p>{{ $resignation->handover_completion_date->format('Y-m-d') }}</p>
                            @endif
                        </div>
                    </div>
                    @endif

                    @if($resignation->handover_notes)
                    <div class="row">
                        <div class="col-12">
                            <p><strong>ملاحظات التسليم:</strong></p>
                            <div class="bg-light p-3 rounded">
                                {{ $resignation->handover_notes }}
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Exit Interview -->
            @if($resignation->exit_interview_date || $resignation->exit_interview_notes)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-comments"></i>
                        مقابلة الخروج
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @if($resignation->exit_interview_date)
                        <div class="col-md-6">
                            <p><strong>تاريخ المقابلة:</strong></p>
                            <p class="mb-3">{{ $resignation->exit_interview_date->format('Y-m-d') }}</p>
                        </div>
                        @endif
                        
                        @if($resignation->exit_interview_conducted_by)
                        <div class="col-md-6">
                            <p><strong>أجريت بواسطة:</strong></p>
                            <p class="mb-3">{{ $resignation->exit_interview_conducted_by }}</p>
                        </div>
                        @endif
                    </div>

                    @if($resignation->exit_interview_notes)
                    <div class="row">
                        <div class="col-12">
                            <p><strong>ملاحظات المقابلة:</strong></p>
                            <div class="bg-light p-3 rounded">
                                {{ $resignation->exit_interview_notes }}
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Documents -->
            @if($resignation->resignation_letter_path || $resignation->additional_documents)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-paperclip"></i>
                        المرفقات
                    </h6>
                </div>
                <div class="card-body">
                    @if($resignation->resignation_letter_path)
                    <div class="mb-3">
                        <p><strong>خطاب الاستقالة:</strong></p>
                        <a href="{{ Storage::url($resignation->resignation_letter_path) }}" target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-file-pdf"></i>
                            عرض خطاب الاستقالة
                        </a>
                    </div>
                    @endif

                    @if($resignation->additional_documents)
                    <div>
                        <p><strong>مستندات إضافية:</strong></p>
                        @foreach(json_decode($resignation->additional_documents, true) as $document)
                        <a href="{{ Storage::url($document['path']) }}" target="_blank" class="btn btn-outline-secondary btn-sm mr-2 mb-2">
                            <i class="fas fa-file"></i>
                            {{ $document['name'] }}
                        </a>
                        @endforeach
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Status Timeline -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-history"></i>
                        تاريخ الحالة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">تقديم الاستقالة</h6>
                                <p class="timeline-text">{{ $resignation->created_at->format('Y-m-d H:i') }}</p>
                                <small class="text-muted">تم تقديم طلب الاستقالة</small>
                            </div>
                        </div>

                        @if($resignation->approved_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">الموافقة</h6>
                                <p class="timeline-text">{{ $resignation->approved_at->format('Y-m-d H:i') }}</p>
                                <small class="text-muted">تم الموافقة على الاستقالة</small>
                            </div>
                        </div>
                        @endif

                        @if($resignation->rejected_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">الرفض</h6>
                                <p class="timeline-text">{{ $resignation->rejected_at->format('Y-m-d H:i') }}</p>
                                <small class="text-muted">تم رفض الاستقالة</small>
                            </div>
                        </div>
                        @endif

                        @if($resignation->handover_completion_date)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">اكتمال التسليم</h6>
                                <p class="timeline-text">{{ $resignation->handover_completion_date->format('Y-m-d H:i') }}</p>
                                <small class="text-muted">تم إكمال تسليم المهام</small>
                            </div>
                        </div>
                        @endif

                        @if($resignation->exit_interview_date)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-warning"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">مقابلة الخروج</h6>
                                <p class="timeline-text">{{ $resignation->exit_interview_date->format('Y-m-d H:i') }}</p>
                                <small class="text-muted">تم إجراء مقابلة الخروج</small>
                            </div>
                        </div>
                        @endif

                        @if($resignation->completed_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-secondary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">الإكمال</h6>
                                <p class="timeline-text">{{ $resignation->completed_at->format('Y-m-d H:i') }}</p>
                                <small class="text-muted">تم إكمال جميع الإجراءات</small>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt"></i>
                        إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if($resignation->status === 'pending')
                            <a href="{{ route('employee-resignations.edit', $resignation->id) }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit"></i>
                                تعديل الاستقالة
                            </a>
                            <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#approveModal">
                                <i class="fas fa-check"></i>
                                الموافقة
                            </button>
                            <button type="button" class="btn btn-danger btn-sm" data-toggle="modal" data-target="#rejectModal">
                                <i class="fas fa-times"></i>
                                الرفض
                            </button>
                        @endif

                        @if($resignation->status === 'approved')
                            <button type="button" class="btn btn-info btn-sm" data-toggle="modal" data-target="#completeModal">
                                <i class="fas fa-flag-checkered"></i>
                                إكمال الاستقالة
                            </button>
                        @endif

                        <button type="button" class="btn btn-secondary btn-sm" onclick="printResignation()">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                        
                        <a href="{{ route('employee-resignations.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-right"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-chart-bar"></i>
                        إحصائيات
                    </h6>
                </div>
                <div class="card-body">
                    <p><strong>أيام منذ التقديم:</strong></p>
                    <p class="text-muted mb-3">{{ $resignation->created_at->diffInDays(now()) }} يوم</p>

                    @if($resignation->resignation_date && $resignation->last_working_date)
                    <p><strong>فترة الإشعار:</strong></p>
                    <p class="text-muted mb-3">{{ $resignation->resignation_date->diffInDays($resignation->last_working_date) }} يوم</p>
                    @endif

                    @if($resignation->last_working_date)
                    <p><strong>أيام متبقية:</strong></p>
                    <p class="text-muted">
                        @php
                            $remainingDays = now()->diffInDays($resignation->last_working_date, false);
                        @endphp
                        @if($remainingDays > 0)
                            {{ $remainingDays }} يوم
                        @elseif($remainingDays == 0)
                            اليوم الأخير
                        @else
                            انتهت ({{ abs($remainingDays) }} يوم مضى)
                        @endif
                    </p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Approve Modal -->
<div class="modal fade" id="approveModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check text-success"></i>
                    الموافقة على الاستقالة
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-resignations.approve', $resignation->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        هل أنت متأكد من الموافقة على استقالة <strong>{{ $resignation->employee->name }}</strong>؟
                    </div>

                    <div class="form-group">
                        <label for="approval_notes">ملاحظات الموافقة</label>
                        <textarea class="form-control" id="approval_notes" name="approval_notes" rows="3"
                                  placeholder="أي ملاحظات حول الموافقة..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="approved_last_working_date">تأكيد آخر يوم عمل</label>
                        <input type="date" class="form-control" id="approved_last_working_date"
                               name="approved_last_working_date"
                               value="{{ $resignation->last_working_date ? $resignation->last_working_date->format('Y-m-d') : '' }}">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i>
                        تأكيد الموافقة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-times text-danger"></i>
                    رفض الاستقالة
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-resignations.reject', $resignation->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        هل أنت متأكد من رفض استقالة <strong>{{ $resignation->employee->name }}</strong>؟
                    </div>

                    <div class="form-group">
                        <label for="rejection_reason">سبب الرفض <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="4"
                                  placeholder="اشرح سبب رفض الاستقالة..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times"></i>
                        تأكيد الرفض
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Complete Modal -->
<div class="modal fade" id="completeModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-flag-checkered text-info"></i>
                    إكمال الاستقالة
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-resignations.complete', $resignation->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        إكمال الاستقالة يعني أن جميع الإجراءات قد تمت وأن الموظف قد غادر العمل رسمياً.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="final_settlement_amount">مبلغ التسوية النهائية</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="final_settlement_amount"
                                           name="final_settlement_amount" step="0.01" min="0"
                                           placeholder="0.00">
                                    <div class="input-group-append">
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="final_working_date">تاريخ آخر يوم عمل فعلي</label>
                                <input type="date" class="form-control" id="final_working_date"
                                       name="final_working_date"
                                       value="{{ $resignation->last_working_date ? $resignation->last_working_date->format('Y-m-d') : '' }}">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>قائمة التحقق من الإجراءات</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="handover_completed" name="checklist[]" value="handover_completed">
                            <label class="form-check-label" for="handover_completed">
                                تم تسليم جميع المهام والمسؤوليات
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="assets_returned" name="checklist[]" value="assets_returned">
                            <label class="form-check-label" for="assets_returned">
                                تم إرجاع جميع الأصول والمعدات
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="access_revoked" name="checklist[]" value="access_revoked">
                            <label class="form-check-label" for="access_revoked">
                                تم إلغاء جميع الصلاحيات والوصول
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="exit_interview_done" name="checklist[]" value="exit_interview_done">
                            <label class="form-check-label" for="exit_interview_done">
                                تم إجراء مقابلة الخروج
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="final_settlement_paid" name="checklist[]" value="final_settlement_paid">
                            <label class="form-check-label" for="final_settlement_paid">
                                تم دفع التسوية النهائية
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="completion_notes">ملاحظات الإكمال</label>
                        <textarea class="form-control" id="completion_notes" name="completion_notes" rows="3"
                                  placeholder="أي ملاحظات حول إكمال الاستقالة..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-flag-checkered"></i>
                        إكمال الاستقالة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-download text-info"></i>
                    تصدير تفاصيل الاستقالة
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-resignations.export-pdf', $resignation->id) }}" method="GET" target="_blank">
                <div class="modal-body">
                    <div class="form-group">
                        <label>اختر الأقسام المراد تصديرها:</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_employee_info" name="sections[]" value="employee_info" checked>
                            <label class="form-check-label" for="include_employee_info">
                                معلومات الموظف
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_resignation_details" name="sections[]" value="resignation_details" checked>
                            <label class="form-check-label" for="include_resignation_details">
                                تفاصيل الاستقالة
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_handover" name="sections[]" value="handover">
                            <label class="form-check-label" for="include_handover">
                                معلومات التسليم
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_exit_interview" name="sections[]" value="exit_interview">
                            <label class="form-check-label" for="include_exit_interview">
                                مقابلة الخروج
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_timeline" name="sections[]" value="timeline">
                            <label class="form-check-label" for="include_timeline">
                                تاريخ الحالة
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="export_format">تنسيق التصدير</label>
                        <select class="form-control" id="export_format" name="format">
                            <option value="pdf">PDF</option>
                            <option value="word">Word Document</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('styles')
<style>
/* Status-based card styling */
.status-pending {
    border-left: 4px solid #ffc107;
    background: rgba(255, 193, 7, 0.05);
}

.status-approved {
    border-left: 4px solid #28a745;
    background: rgba(40, 167, 69, 0.05);
}

.status-rejected {
    border-left: 4px solid #dc3545;
    background: rgba(220, 53, 69, 0.05);
}

.status-completed {
    border-left: 4px solid #6c757d;
    background: rgba(108, 117, 125, 0.05);
}

/* Employee avatar styling */
.employee-avatar {
    border: 3px solid #fff;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.employee-avatar:hover {
    transform: scale(1.05);
}

/* Timeline styling */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #007bff, #e3e6f0);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    background: #fff;
    border-radius: 0.5rem;
    padding: 1rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.timeline-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 1rem;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 3px solid #fff;
    box-shadow: 0 0 0 3px #007bff;
    z-index: 1;
}

.timeline-marker.bg-primary {
    background: #007bff;
    box-shadow: 0 0 0 3px #007bff;
}

.timeline-marker.bg-success {
    background: #28a745;
    box-shadow: 0 0 0 3px #28a745;
}

.timeline-marker.bg-danger {
    background: #dc3545;
    box-shadow: 0 0 0 3px #dc3545;
}

.timeline-marker.bg-info {
    background: #17a2b8;
    box-shadow: 0 0 0 3px #17a2b8;
}

.timeline-marker.bg-warning {
    background: #ffc107;
    box-shadow: 0 0 0 3px #ffc107;
}

.timeline-marker.bg-secondary {
    background: #6c757d;
    box-shadow: 0 0 0 3px #6c757d;
}

.timeline-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.timeline-text {
    color: #007bff;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

/* Badge styling */
.badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 500;
}

/* Card header styling */
.card-header {
    background: linear-gradient(45deg, #f8f9fc, #ffffff);
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
}

/* Document links styling */
.document-link {
    transition: all 0.2s ease-in-out;
    border-radius: 0.375rem;
}

.document-link:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* Statistics card styling */
.stats-card {
    background: linear-gradient(45deg, #f8f9fc, #ffffff);
    border: 1px solid #e3e6f0;
    transition: all 0.2s ease-in-out;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Quick actions styling */
.quick-actions .btn {
    margin-bottom: 0.5rem;
    transition: all 0.2s ease-in-out;
    border-radius: 0.375rem;
}

.quick-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* Modal styling */
.modal-header {
    background: linear-gradient(45deg, #f8f9fc, #ffffff);
    border-bottom: 2px solid #e3e6f0;
}

.modal-content {
    border-radius: 0.5rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Form check styling */
.form-check {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fc;
}

.form-check:last-child {
    border-bottom: none;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

/* Alert styling */
.alert {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Dropdown menu styling */
.dropdown-menu {
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border: none;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    transition: all 0.2s ease-in-out;
}

.dropdown-item:hover {
    background: #f8f9fc;
    transform: translateX(5px);
}

/* Print styles */
@media print {
    .btn, .dropdown, nav, .sidebar, .modal {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        break-inside: avoid;
        margin-bottom: 1rem;
    }

    .card-header {
        background: #f8f9fc !important;
        color: #000 !important;
        border-bottom: 2px solid #000 !important;
    }

    .badge {
        border: 1px solid #000 !important;
        color: #000 !important;
        background: transparent !important;
    }

    .timeline::before {
        background: #000 !important;
    }

    .timeline-marker {
        background: #000 !important;
        box-shadow: 0 0 0 3px #000 !important;
    }

    .alert {
        border: 1px solid #000 !important;
        background: transparent !important;
        color: #000 !important;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .timeline {
        padding-left: 20px;
    }

    .timeline::before {
        left: 10px;
    }

    .timeline-marker {
        left: -17px;
        width: 12px;
        height: 12px;
    }

    .timeline-item {
        padding: 0.75rem;
        font-size: 0.875rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn-sm {
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 0.5rem;
}

/* Animation for status changes */
@keyframes statusChange {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.status-changed {
    animation: statusChange 0.5s ease-in-out;
}

/* Hover effects */
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease-in-out;
}

/* Input group styling */
.input-group-text {
    background: #f8f9fc;
    border-color: #d1d3e2;
    color: #5a5c69;
}

/* Tooltip styling */
.tooltip-inner {
    background-color: #5a5c69;
    color: #fff;
    border-radius: 0.25rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

/* Success/Error message styling */
.alert-floating {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
</style>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize tooltips
    $('[title]').tooltip();

    // Auto-refresh status if resignation is pending
    @if($resignation->status === 'pending')
    setInterval(function() {
        checkStatusUpdate();
    }, 30000); // Check every 30 seconds
    @endif

    // Form validation for modals
    $('form').on('submit', function(e) {
        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');

        // Show loading state
        submitBtn.prop('disabled', true);
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...');

        // Re-enable button after 10 seconds (fallback)
        setTimeout(function() {
            submitBtn.prop('disabled', false);
            submitBtn.html(originalText);
        }, 10000);
    });

    // Modal event handlers
    $('#approveModal').on('show.bs.modal', function() {
        // Focus on approval notes
        setTimeout(function() {
            $('#approval_notes').focus();
        }, 500);
    });

    $('#rejectModal').on('show.bs.modal', function() {
        // Focus on rejection reason
        setTimeout(function() {
            $('#rejection_reason').focus();
        }, 500);
    });

    $('#completeModal').on('show.bs.modal', function() {
        // Calculate final settlement amount if not set
        calculateFinalSettlement();
    });

    // Character counter for rejection reason
    $('#rejection_reason').on('input', function() {
        const maxLength = 500;
        const currentLength = $(this).val().length;

        if (currentLength > maxLength * 0.9) {
            $(this).addClass('border-warning');
        } else {
            $(this).removeClass('border-warning');
        }
    });

    // Checklist validation for completion
    $('#completeModal form').on('submit', function(e) {
        const checkedItems = $(this).find('input[name="checklist[]"]:checked').length;

        if (checkedItems < 3) {
            e.preventDefault();
            showAlert('warning', 'يجب إكمال على الأقل 3 عناصر من قائمة التحقق');
            return false;
        }
    });

    // Export form handling
    $('#exportModal form').on('submit', function(e) {
        const selectedSections = $(this).find('input[name="sections[]"]:checked').length;

        if (selectedSections === 0) {
            e.preventDefault();
            showAlert('warning', 'يجب اختيار قسم واحد على الأقل للتصدير');
            return false;
        }
    });

    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        // Ctrl+P for print
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printResignation();
        }

        // Ctrl+E for export
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            $('#exportModal').modal('show');
        }

        // Escape to close modals
        if (e.key === 'Escape') {
            $('.modal').modal('hide');
        }
    });
});

// Check for status updates
function checkStatusUpdate() {
    $.ajax({
        url: '{{ route("employee-resignations.status", $resignation->id) }}',
        method: 'GET',
        success: function(response) {
            if (response.status !== '{{ $resignation->status }}') {
                // Status has changed, reload page
                location.reload();
            }
        },
        error: function() {
            // Silently fail
        }
    });
}

// Calculate final settlement amount
function calculateFinalSettlement() {
    // This would typically call an API to calculate the settlement
    // For now, we'll just show a placeholder
    const employeeId = {{ $resignation->employee_id }};

    $.ajax({
        url: '{{ route("employee-resignations.calculate-settlement") }}',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            employee_id: employeeId,
            resignation_id: {{ $resignation->id }}
        },
        success: function(response) {
            if (response.settlement_amount) {
                $('#final_settlement_amount').val(response.settlement_amount);
                showAlert('info', 'تم حساب مبلغ التسوية النهائية تلقائياً');
            }
        },
        error: function() {
            // Silently fail
        }
    });
}

// Print resignation details
function printResignation() {
    // Hide elements that shouldn't be printed
    $('.btn, .dropdown, nav, .sidebar').hide();

    // Print the page
    window.print();

    // Show elements again after printing
    setTimeout(function() {
        $('.btn, .dropdown, nav, .sidebar').show();
    }, 1000);
}

// Show alert function
function showAlert(type, message, duration = 5000) {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    const alertIcon = type === 'success' ? 'fa-check-circle' :
                     type === 'error' ? 'fa-exclamation-triangle' :
                     type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show alert-floating">
            <i class="fas ${alertIcon}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;

    $('body').append(alertHtml);

    // Auto-remove after specified duration
    setTimeout(function() {
        $('.alert-floating').fadeOut(function() {
            $(this).remove();
        });
    }, duration);
}

// Timeline animation on scroll
$(window).on('scroll', function() {
    $('.timeline-item').each(function() {
        const elementTop = $(this).offset().top;
        const elementBottom = elementTop + $(this).outerHeight();
        const viewportTop = $(window).scrollTop();
        const viewportBottom = viewportTop + $(window).height();

        if (elementBottom > viewportTop && elementTop < viewportBottom) {
            $(this).addClass('animate__animated animate__fadeInLeft');
        }
    });
});

// Status change animation
function animateStatusChange() {
    $('.alert').addClass('status-changed');
    setTimeout(function() {
        $('.alert').removeClass('status-changed');
    }, 500);
}

// Auto-save notes in modals
let autoSaveTimer;
$('textarea').on('input', function() {
    const textarea = $(this);
    clearTimeout(autoSaveTimer);

    autoSaveTimer = setTimeout(function() {
        // Save to localStorage as draft
        const key = 'resignation_' + {{ $resignation->id }} + '_' + textarea.attr('id');
        localStorage.setItem(key, textarea.val());
    }, 2000);
});

// Load saved drafts
$('textarea').each(function() {
    const textarea = $(this);
    const key = 'resignation_' + {{ $resignation->id }} + '_' + textarea.attr('id');
    const savedValue = localStorage.getItem(key);

    if (savedValue && !textarea.val()) {
        textarea.val(savedValue);
        textarea.after('<small class="text-muted">تم استرداد المسودة المحفوظة</small>');
    }
});

// Clear drafts on successful submission
$('form').on('submit', function() {
    const form = $(this);
    form.find('textarea').each(function() {
        const key = 'resignation_' + {{ $resignation->id }} + '_' + $(this).attr('id');
        localStorage.removeItem(key);
    });
});

// Real-time validation
$('#rejection_reason').on('input', function() {
    const value = $(this).val().trim();
    const submitBtn = $('#rejectModal').find('button[type="submit"]');

    if (value.length < 10) {
        $(this).addClass('is-invalid');
        submitBtn.prop('disabled', true);
    } else {
        $(this).removeClass('is-invalid').addClass('is-valid');
        submitBtn.prop('disabled', false);
    }
});

// Date validation
$('#approved_last_working_date, #final_working_date').on('change', function() {
    const selectedDate = new Date($(this).val());
    const today = new Date();
    const resignationDate = new Date('{{ $resignation->resignation_date ? $resignation->resignation_date->format("Y-m-d") : "" }}');

    if (selectedDate < resignationDate) {
        $(this).addClass('is-invalid');
        showAlert('error', 'التاريخ يجب أن يكون بعد تاريخ تقديم الاستقالة');
    } else {
        $(this).removeClass('is-invalid').addClass('is-valid');
    }
});
</script>
@endsection
