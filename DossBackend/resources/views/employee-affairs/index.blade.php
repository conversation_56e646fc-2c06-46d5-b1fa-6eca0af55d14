@extends('layouts.master')

@section('title', 'شؤون العاملين - لوحة التحكم')

@section('main-content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-users-cog text-primary"></i>
                        شؤون العاملين
                    </h1>
                    <p class="text-muted mb-0">لوحة التحكم الرئيسية لإدارة شؤون الموظفين</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="refreshStats()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث البيانات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <!-- Bonuses Card -->
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-1 overflow-hidden">
                            <p class="text-truncate font-size-14 mb-2">المكافآت</p>
                            <h4 class="mb-0">{{ $stats['bonuses']['total'] }}</h4>
                        </div>
                        <div class="text-primary">
                            <i class="bx bx-gift font-size-24"></i>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <span class="text-success me-1">{{ $stats['bonuses']['approved'] }}</span>
                        معتمد | 
                        <span class="text-warning">{{ $stats['bonuses']['pending'] }}</span>
                        في الانتظار
                    </p>
                </div>
            </div>
        </div>

        <!-- Deductions Card -->
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-1 overflow-hidden">
                            <p class="text-truncate font-size-14 mb-2">الخصومات</p>
                            <h4 class="mb-0">{{ $stats['deductions']['total'] }}</h4>
                        </div>
                        <div class="text-danger">
                            <i class="bx bx-minus-circle font-size-24"></i>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <span class="text-info me-1">{{ $stats['deductions']['active'] }}</span>
                        نشط | 
                        <span class="text-warning">{{ $stats['deductions']['pending'] }}</span>
                        في الانتظار
                    </p>
                </div>
            </div>
        </div>

        <!-- Transfers Card -->
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-1 overflow-hidden">
                            <p class="text-truncate font-size-14 mb-2">النقل</p>
                            <h4 class="mb-0">{{ $stats['transfers']['total'] }}</h4>
                        </div>
                        <div class="text-info">
                            <i class="bx bx-transfer font-size-24"></i>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <span class="text-success me-1">{{ $stats['transfers']['active'] }}</span>
                        نشط | 
                        <span class="text-warning">{{ $stats['transfers']['pending'] }}</span>
                        في الانتظار
                    </p>
                </div>
            </div>
        </div>

        <!-- Promotions Card -->
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-1 overflow-hidden">
                            <p class="text-truncate font-size-14 mb-2">الترقيات</p>
                            <h4 class="mb-0">{{ $stats['promotions']['total'] }}</h4>
                        </div>
                        <div class="text-success">
                            <i class="bx bx-trending-up font-size-24"></i>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <span class="text-success me-1">{{ $stats['promotions']['this_year'] }}</span>
                        هذا العام | 
                        <span class="text-warning">{{ $stats['promotions']['pending'] }}</span>
                        في الانتظار
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Row Statistics -->
    <div class="row">
        <!-- Suggestions Card -->
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-1 overflow-hidden">
                            <p class="text-truncate font-size-14 mb-2">الاقتراحات</p>
                            <h4 class="mb-0">{{ $stats['suggestions']['total'] }}</h4>
                        </div>
                        <div class="text-warning">
                            <i class="bx bx-bulb font-size-24"></i>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <span class="text-success me-1">{{ $stats['suggestions']['implemented'] }}</span>
                        منفذ | 
                        <span class="text-info">{{ $stats['suggestions']['pending_review'] }}</span>
                        قيد المراجعة
                    </p>
                </div>
            </div>
        </div>

        <!-- Complaints Card -->
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-1 overflow-hidden">
                            <p class="text-truncate font-size-14 mb-2">الشكاوى</p>
                            <h4 class="mb-0">{{ $stats['complaints']['total'] }}</h4>
                        </div>
                        <div class="text-danger">
                            <i class="bx bx-error-circle font-size-24"></i>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <span class="text-warning me-1">{{ $stats['complaints']['investigating'] }}</span>
                        قيد التحقيق | 
                        <span class="text-danger">{{ $stats['complaints']['high_severity'] }}</span>
                        عالية الخطورة
                    </p>
                </div>
            </div>
        </div>

        <!-- Resignations Card -->
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-1 overflow-hidden">
                            <p class="text-truncate font-size-14 mb-2">الاستقالات</p>
                            <h4 class="mb-0">{{ $stats['resignations']['total'] }}</h4>
                        </div>
                        <div class="text-secondary">
                            <i class="bx bx-exit font-size-24"></i>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <span class="text-info me-1">{{ $stats['resignations']['active'] }}</span>
                        نشط | 
                        <span class="text-warning">{{ $stats['resignations']['pending'] }}</span>
                        في الانتظار
                    </p>
                </div>
            </div>
        </div>

        <!-- Warnings Card -->
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-1 overflow-hidden">
                            <p class="text-truncate font-size-14 mb-2">الإنذارات</p>
                            <h4 class="mb-0">{{ $stats['warnings']['total'] }}</h4>
                        </div>
                        <div class="text-warning">
                            <i class="bx bx-error font-size-24"></i>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <span class="text-warning me-1">{{ $stats['warnings']['active'] }}</span>
                        نشط | 
                        <span class="text-danger">{{ $stats['warnings']['high_level'] }}</span>
                        مستوى عالي
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">الإجراءات السريعة</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="{{ route('employee-bonuses.create') }}" class="btn btn-primary btn-block">
                                <i class="bx bx-plus"></i> إضافة مكافأة
                            </a>
                        </div>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="{{ route('employee-bonuses.index') }}" class="btn btn-outline-primary btn-block">
                                <i class="bx bx-gift"></i> المكافآت
                            </a>
                        </div>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <button class="btn btn-outline-info btn-block" onclick="processRecurringDeductions()">
                                <i class="bx bx-refresh"></i> معالجة الخصومات
                            </button>
                        </div>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <button class="btn btn-outline-success btn-block" onclick="executeTransfers()">
                                <i class="bx bx-transfer"></i> تنفيذ النقل
                            </button>
                        </div>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <button class="btn btn-outline-warning btn-block" onclick="executePromotions()">
                                <i class="bx bx-trending-up"></i> تنفيذ الترقيات
                            </button>
                        </div>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="{{ route('employee-affairs.reports.monthly') }}" class="btn btn-outline-secondary btn-block">
                                <i class="bx bx-bar-chart"></i> التقارير
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Approvals -->
    @if(count($pendingApprovals['bonuses']) > 0 || count($pendingApprovals['deductions']) > 0 || count($pendingApprovals['transfers']) > 0 || count($pendingApprovals['promotions']) > 0 || count($pendingApprovals['resignations']) > 0)
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">الموافقات المعلقة</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>النوع</th>
                                    <th>الموظف</th>
                                    <th>التفاصيل</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($pendingApprovals['bonuses'] as $bonus)
                                <tr>
                                    <td><span class="badge badge-primary">مكافأة</span></td>
                                    <td>{{ $bonus->user->name }}</td>
                                    <td>{{ $bonus->bonus_type_label }} - {{ number_format($bonus->amount, 2) }} ريال</td>
                                    <td>{{ $bonus->created_at->format('Y-m-d') }}</td>
                                    <td>
                                        <a href="{{ route('employee-bonuses.show', $bonus) }}" class="btn btn-sm btn-info">عرض</a>
                                    </td>
                                </tr>
                                @endforeach
                                
                                @foreach($pendingApprovals['deductions'] as $deduction)
                                <tr>
                                    <td><span class="badge badge-danger">خصم</span></td>
                                    <td>{{ $deduction->user->name }}</td>
                                    <td>{{ $deduction->deduction_type_label }} - {{ number_format($deduction->amount, 2) }} ريال</td>
                                    <td>{{ $deduction->created_at->format('Y-m-d') }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-info">عرض</button>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

@push('scripts')
<script>
function processRecurringDeductions() {
    if (confirm('هل أنت متأكد من معالجة الخصومات المتكررة؟')) {
        $.post('{{ route("employee-affairs.process-recurring-deductions") }}', {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            alert(response.message);
            location.reload();
        })
        .fail(function() {
            alert('حدث خطأ أثناء المعالجة');
        });
    }
}

function executeTransfers() {
    if (confirm('هل أنت متأكد من تنفيذ النقل المعتمد؟')) {
        $.post('{{ route("employee-affairs.execute-transfers") }}', {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            alert(response.message);
            location.reload();
        })
        .fail(function() {
            alert('حدث خطأ أثناء التنفيذ');
        });
    }
}

function executePromotions() {
    if (confirm('هل أنت متأكد من تنفيذ الترقيات المعتمدة؟')) {
        $.post('{{ route("employee-affairs.execute-promotions") }}', {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            alert(response.message);
            location.reload();
        })
        .fail(function() {
            alert('حدث خطأ أثناء التنفيذ');
        });
    }
}
</script>
@endpush
@endsection
