@extends('layouts.app')

@section('title', 'إدارة الخصومات')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-minus-circle text-danger"></i>
                        إدارة خصومات الموظفين
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item active">الخصومات</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button type="button" class="btn btn-info" onclick="refreshPage()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                    <a href="{{ route('employee-deductions.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        خصم جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الخصومات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-deductions">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                في الانتظار
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="pending-deductions">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                معتمدة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="approved-deductions">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                إجمالي المبلغ
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-amount">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt"></i>
                        إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card border-left-warning h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                    <h6>خصومات في الانتظار</h6>
                                    <h4 class="text-warning" id="pending-count">-</h4>
                                    <a href="{{ route('employee-deductions.index', ['status' => 'pending']) }}" class="btn btn-sm btn-outline-warning">
                                        عرض الكل
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-left-info h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-calendar-alt fa-2x text-info mb-2"></i>
                                    <h6>أقساط مستحقة اليوم</h6>
                                    <h4 class="text-info" id="due-today">-</h4>
                                    <button class="btn btn-sm btn-outline-info" onclick="showDueToday()">
                                        عرض التفاصيل
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-left-success h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                                    <h6>إجمالي المحصل</h6>
                                    <h4 class="text-success" id="collected-amount">-</h4>
                                    <button class="btn btn-sm btn-outline-success" onclick="showCollectionReport()">
                                        تقرير التحصيل
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-left-danger h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                                    <h6>أقساط متأخرة</h6>
                                    <h4 class="text-danger" id="overdue-count">-</h4>
                                    <button class="btn btn-sm btn-outline-danger" onclick="showOverdue()">
                                        عرض المتأخرات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter"></i>
                فلترة النتائج
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('employee-deductions.index') }}" id="filter-form">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="employee_id">الموظف</label>
                            <select class="form-control select2" id="employee_id" name="employee_id">
                                <option value="">جميع الموظفين</option>
                                {{-- @foreach($employees as $employee)
                                    <option value="{{ $employee->id }}" {{ request('employee_id') == $employee->id ? 'selected' : '' }}>
                                        {{ $employee->name }} - {{ $employee->employee_code }}
                                    </option>
                                @endforeach --}}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">الحالة</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>في الانتظار</option>
                                <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>معتمد</option>
                                <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>مكتمل</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="type">النوع</label>
                            <select class="form-control" id="type" name="type">
                                <option value="">جميع الأنواع</option>
                                <option value="one_time" {{ request('type') == 'one_time' ? 'selected' : '' }}>لمرة واحدة</option>
                                <option value="installment" {{ request('type') == 'installment' ? 'selected' : '' }}>بالأقساط</option>
                                <option value="monthly" {{ request('type') == 'monthly' ? 'selected' : '' }}>شهري</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="category">الفئة</label>
                            <select class="form-control" id="category" name="category">
                                <option value="">جميع الفئات</option>
                                <option value="disciplinary" {{ request('category') == 'disciplinary' ? 'selected' : '' }}>تأديبي</option>
                                <option value="loan_repayment" {{ request('category') == 'loan_repayment' ? 'selected' : '' }}>سداد قرض</option>
                                <option value="advance_repayment" {{ request('category') == 'advance_repayment' ? 'selected' : '' }}>سداد سلفة</option>
                                <option value="insurance" {{ request('category') == 'insurance' ? 'selected' : '' }}>تأمين</option>
                                <option value="tax" {{ request('category') == 'tax' ? 'selected' : '' }}>ضرائب</option>
                                <option value="absence" {{ request('category') == 'absence' ? 'selected' : '' }}>غياب</option>
                                <option value="damage" {{ request('category') == 'damage' ? 'selected' : '' }}>تعويض أضرار</option>
                                <option value="other" {{ request('category') == 'other' ? 'selected' : '' }}>أخرى</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_range">فترة التاريخ</label>
                            <div class="input-group">
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                       value="{{ request('date_from') }}" placeholder="من">
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                       value="{{ request('date_to') }}" placeholder="إلى">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                        <a href="{{ route('employee-deductions.index') }}" class="btn btn-secondary">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين
                        </a>
                        <button type="button" class="btn btn-success" onclick="exportDeductions()">
                            <i class="fas fa-file-excel"></i>
                            تصدير Excel
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Deductions Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-table"></i>
                قائمة الخصومات
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="deductionsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>رقم الخصم</th>
                            <th>الموظف</th>
                            <th>النوع</th>
                            <th>الفئة</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>التاريخ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{-- @forelse($deductions as $deduction)
                            <tr>
                                <td>
                                    <a href="{{ route('employee-deductions.show', $deduction->id) }}" class="text-primary font-weight-bold">
                                        #{{ $deduction->id }}
                                    </a>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="{{ $deduction->employee->avatar ?? '/images/default-avatar.png' }}" 
                                             class="rounded-circle mr-2" width="30" height="30" alt="صورة الموظف">
                                        <div>
                                            <div class="font-weight-bold">{{ $deduction->employee->name }}</div>
                                            <small class="text-muted">{{ $deduction->employee->employee_code }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @switch($deduction->type)
                                        @case('one_time')
                                            <span class="badge badge-info">لمرة واحدة</span>
                                            @break
                                        @case('installment')
                                            <span class="badge badge-warning">بالأقساط</span>
                                            @break
                                        @case('monthly')
                                            <span class="badge badge-secondary">شهري</span>
                                            @break
                                    @endswitch
                                </td>
                                <td>
                                    @switch($deduction->category)
                                        @case('disciplinary')
                                            تأديبي
                                            @break
                                        @case('loan_repayment')
                                            سداد قرض
                                            @break
                                        @case('advance_repayment')
                                            سداد سلفة
                                            @break
                                        @case('insurance')
                                            تأمين
                                            @break
                                        @case('tax')
                                            ضرائب
                                            @break
                                        @case('absence')
                                            غياب
                                            @break
                                        @case('damage')
                                            تعويض أضرار
                                            @break
                                        @default
                                            {{ $deduction->category }}
                                    @endswitch
                                </td>
                                <td>
                                    <span class="text-danger font-weight-bold">{{ number_format($deduction->amount, 2) }} ج.م</span>
                                    @if($deduction->type === 'installment')
                                        <br><small class="text-muted">{{ $deduction->installments }} أقساط</small>
                                    @endif
                                </td>
                                <td>
                                    @switch($deduction->status)
                                        @case('pending')
                                            <span class="badge badge-warning">في الانتظار</span>
                                            @break
                                        @case('approved')
                                            <span class="badge badge-success">معتمد</span>
                                            @break
                                        @case('rejected')
                                            <span class="badge badge-danger">مرفوض</span>
                                            @break
                                        @case('completed')
                                            <span class="badge badge-info">مكتمل</span>
                                            @break
                                    @endswitch
                                </td>
                                <td>{{ $deduction->created_at->format('Y-m-d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('employee-deductions.show', $deduction->id) }}" 
                                           class="btn btn-sm btn-outline-info" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if($deduction->status === 'pending')
                                            <a href="{{ route('employee-deductions.edit', $deduction->id) }}" 
                                               class="btn btn-sm btn-outline-primary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty --}}
                            <tr>
                                <td colspan="8" class="text-center">
                                    <div class="py-4">
                                        <i class="fas fa-inbox fa-3x text-gray-300 mb-3"></i>
                                        <p class="text-gray-500">لا توجد خصومات مسجلة</p>
                                        <a href="{{ route('employee-deductions.create') }}" class="btn btn-primary">
                                            <i class="fas fa-plus"></i>
                                            إضافة خصم جديد
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {{-- @endforelse --}}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {{-- @if($deductions->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $deductions->appends(request()->query())->links() }}
                </div>
            @endif --}}
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        placeholder: 'اختر الموظف',
        allowClear: true
    });

    // Load statistics
    loadStatistics();

    // Initialize DataTable
    $('#deductionsTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "order": [[ 6, "desc" ]],
        "pageLength": 25,
        "responsive": true
    });
});

function loadStatistics() {
    // Simulate loading statistics
    setTimeout(function() {
        $('#total-deductions').text('0');
        $('#pending-deductions').text('0');
        $('#approved-deductions').text('0');
        $('#total-amount').text('0.00 ج.م');
        $('#pending-count').text('0');
        $('#due-today').text('0');
        $('#collected-amount').text('0.00 ج.م');
        $('#overdue-count').text('0');
    }, 1000);
}

function refreshPage() {
    location.reload();
}

function exportDeductions() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '{{ route("employee-deductions.index") }}?' + params.toString();
}

function showDueToday() {
    alert('سيتم عرض الأقساط المستحقة اليوم');
}

function showCollectionReport() {
    alert('سيتم عرض تقرير التحصيل');
}

function showOverdue() {
    alert('سيتم عرض الأقساط المتأخرة');
}
</script>
@endsection
