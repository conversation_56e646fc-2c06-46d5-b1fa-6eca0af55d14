@extends('layouts.app')

@section('title', 'تفاصيل الخصم')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-eye text-info"></i>
                        تفاصيل الخصم #{{ $deduction->id }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-deductions.index') }}">الخصومات</a></li>
                            <li class="breadcrumb-item active">تفاصيل الخصم</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <div class="btn-group" role="group">
                        @if($deduction->status === 'pending')
                            <a href="{{ route('employee-deductions.edit', $deduction->id) }}" class="btn btn-primary">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </a>
                        @endif
                        <button type="button" class="btn btn-success" onclick="printDeduction()">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                        <a href="{{ route('employee-deductions.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Actions -->
    @if($deduction->status === 'pending')
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-warning mb-1">
                                <i class="fas fa-clock"></i>
                                هذا الخصم في انتظار الموافقة
                            </h6>
                            <small class="text-muted">يمكنك اعتماد أو رفض هذا الخصم</small>
                        </div>
                        <div>
                            <button type="button" class="btn btn-success btn-sm" onclick="approveDeduction()">
                                <i class="fas fa-check"></i>
                                اعتماد
                            </button>
                            <button type="button" class="btn btn-danger btn-sm" onclick="rejectDeduction()">
                                <i class="fas fa-times"></i>
                                رفض
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Main Content -->
    <div class="row">
        <!-- Deduction Details -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle"></i>
                        تفاصيل الخصم
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>رقم الخصم:</strong> #{{ $deduction->id }}</p>
                            <p><strong>نوع الخصم:</strong> 
                                @switch($deduction->type)
                                    @case('one_time')
                                        <span class="badge badge-info">لمرة واحدة</span>
                                        @break
                                    @case('installment')
                                        <span class="badge badge-warning">بالأقساط</span>
                                        @break
                                    @case('monthly')
                                        <span class="badge badge-secondary">شهري</span>
                                        @break
                                @endswitch
                            </p>
                            <p><strong>فئة الخصم:</strong> 
                                @switch($deduction->category)
                                    @case('disciplinary')
                                        تأديبي
                                        @break
                                    @case('loan_repayment')
                                        سداد قرض
                                        @break
                                    @case('advance_repayment')
                                        سداد سلفة
                                        @break
                                    @case('insurance')
                                        تأمين
                                        @break
                                    @case('tax')
                                        ضرائب
                                        @break
                                    @case('absence')
                                        غياب
                                        @break
                                    @case('damage')
                                        تعويض أضرار
                                        @break
                                    @default
                                        {{ $deduction->category }}
                                @endswitch
                            </p>
                            <p><strong>المبلغ الإجمالي:</strong> 
                                <span class="text-danger font-weight-bold">{{ number_format($deduction->amount, 2) }} ج.م</span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>الحالة:</strong> 
                                @switch($deduction->status)
                                    @case('pending')
                                        <span class="badge badge-warning">في الانتظار</span>
                                        @break
                                    @case('approved')
                                        <span class="badge badge-success">معتمد</span>
                                        @break
                                    @case('rejected')
                                        <span class="badge badge-danger">مرفوض</span>
                                        @break
                                    @case('completed')
                                        <span class="badge badge-info">مكتمل</span>
                                        @break
                                @endswitch
                            </p>
                            <p><strong>تاريخ الإنشاء:</strong> {{ $deduction->created_at->format('Y-m-d H:i') }}</p>
                            @if($deduction->approved_at)
                                <p><strong>تاريخ الاعتماد:</strong> {{ $deduction->approved_at->format('Y-m-d H:i') }}</p>
                            @endif
                            @if($deduction->approved_by)
                                <p><strong>معتمد من:</strong> {{ $deduction->approver->name ?? 'غير محدد' }}</p>
                            @endif
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-12">
                            <h6><strong>سبب الخصم:</strong></h6>
                            <p class="text-muted">{{ $deduction->reason }}</p>
                            
                            @if($deduction->notes)
                                <h6><strong>ملاحظات:</strong></h6>
                                <p class="text-muted">{{ $deduction->notes }}</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Installment Details (if applicable) -->
            @if($deduction->type === 'installment')
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-list"></i>
                        تفاصيل الأقساط
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-primary">{{ $deduction->installments }}</h5>
                                <small class="text-muted">إجمالي الأقساط</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-success">{{ $deduction->paid_installments ?? 0 }}</h5>
                                <small class="text-muted">أقساط مدفوعة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-warning">{{ $deduction->installments - ($deduction->paid_installments ?? 0) }}</h5>
                                <small class="text-muted">أقساط متبقية</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="text-info">{{ number_format($deduction->installment_amount, 2) }}</h5>
                                <small class="text-muted">قيمة القسط (ج.م)</small>
                            </div>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="mb-3">
                        @php
                            $progress = $deduction->installments > 0 ? (($deduction->paid_installments ?? 0) / $deduction->installments) * 100 : 0;
                        @endphp
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">تقدم السداد</span>
                            <span class="text-muted">{{ number_format($progress, 1) }}%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ $progress }}%"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>تاريخ البداية:</strong> {{ $deduction->start_date ? $deduction->start_date->format('Y-m-d') : 'غير محدد' }}</p>
                            <p><strong>المبلغ المدفوع:</strong> 
                                <span class="text-success">{{ number_format($deduction->paid_amount ?? 0, 2) }} ج.م</span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>تاريخ الانتهاء المتوقع:</strong> 
                                @if($deduction->start_date)
                                    {{ $deduction->start_date->addMonths($deduction->installments - 1)->format('Y-m-d') }}
                                @else
                                    غير محدد
                                @endif
                            </p>
                            <p><strong>المبلغ المتبقي:</strong> 
                                <span class="text-danger">{{ number_format($deduction->amount - ($deduction->paid_amount ?? 0), 2) }} ج.م</span>
                            </p>
                        </div>
                    </div>

                    @if($deduction->status === 'approved' && ($deduction->paid_installments ?? 0) < $deduction->installments)
                        <div class="mt-3">
                            <button type="button" class="btn btn-primary btn-sm" onclick="recordPayment()">
                                <i class="fas fa-plus"></i>
                                تسجيل دفعة
                            </button>
                        </div>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Employee Info -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user"></i>
                        معلومات الموظف
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <img src="{{ $deduction->employee->avatar ?? '/images/default-avatar.png' }}" 
                             class="rounded-circle" width="80" height="80" alt="صورة الموظف">
                    </div>
                    <p><strong>الاسم:</strong> {{ $deduction->employee->name }}</p>
                    <p><strong>الكود:</strong> {{ $deduction->employee->employee_code }}</p>
                    <p><strong>القسم:</strong> {{ $deduction->employee->department->name ?? 'غير محدد' }}</p>
                    <p><strong>المنصب:</strong> {{ $deduction->employee->position->name ?? 'غير محدد' }}</p>
                    <p><strong>الراتب:</strong> {{ number_format($deduction->employee->salary ?? 0, 2) }} ج.م</p>
                    
                    <div class="mt-3">
                        <a href="{{ route('employees.show', $deduction->employee->id) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i>
                            عرض ملف الموظف
                        </a>
                    </div>
                </div>
            </div>

            <!-- Financial Summary -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-chart-line"></i>
                        الملخص المالي
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12 mb-2">
                            <h4 class="text-danger">{{ number_format($deduction->amount, 2) }}</h4>
                            <small class="text-muted">إجمالي الخصم (ج.م)</small>
                        </div>
                    </div>
                    
                    @if($deduction->type === 'installment')
                        <hr>
                        <div class="row text-center">
                            <div class="col-6">
                                <h5 class="text-success">{{ number_format($deduction->paid_amount ?? 0, 2) }}</h5>
                                <small class="text-muted">مدفوع (ج.م)</small>
                            </div>
                            <div class="col-6">
                                <h5 class="text-warning">{{ number_format($deduction->amount - ($deduction->paid_amount ?? 0), 2) }}</h5>
                                <small class="text-muted">متبقي (ج.م)</small>
                            </div>
                        </div>
                    @endif

                    <!-- Impact on Salary -->
                    @if($deduction->employee->salary)
                        <hr>
                        <div class="alert alert-info">
                            <h6><i class="fas fa-calculator"></i> تأثير على الراتب:</h6>
                            @if($deduction->type === 'installment')
                                <p class="mb-0">خصم شهري: {{ number_format($deduction->installment_amount, 2) }} ج.م</p>
                                <p class="mb-0">نسبة من الراتب: {{ number_format(($deduction->installment_amount / $deduction->employee->salary) * 100, 1) }}%</p>
                            @else
                                <p class="mb-0">نسبة من الراتب: {{ number_format(($deduction->amount / $deduction->employee->salary) * 100, 1) }}%</p>
                            @endif
                        </div>
                    @endif
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-cogs"></i>
                        إجراءات
                    </h6>
                </div>
                <div class="card-body">
                    @if($deduction->status === 'pending')
                        <button type="button" class="btn btn-success btn-block mb-2" onclick="approveDeduction()">
                            <i class="fas fa-check"></i>
                            اعتماد الخصم
                        </button>
                        <button type="button" class="btn btn-danger btn-block mb-2" onclick="rejectDeduction()">
                            <i class="fas fa-times"></i>
                            رفض الخصم
                        </button>
                        <a href="{{ route('employee-deductions.edit', $deduction->id) }}" class="btn btn-primary btn-block mb-2">
                            <i class="fas fa-edit"></i>
                            تعديل الخصم
                        </a>
                    @endif
                    
                    @if($deduction->type === 'installment' && $deduction->status === 'approved')
                        <button type="button" class="btn btn-info btn-block mb-2" onclick="recordPayment()">
                            <i class="fas fa-plus"></i>
                            تسجيل دفعة
                        </button>
                    @endif
                    
                    <button type="button" class="btn btn-secondary btn-block mb-2" onclick="printDeduction()">
                        <i class="fas fa-print"></i>
                        طباعة التفاصيل
                    </button>
                    
                    <a href="{{ route('employee-deductions.create') }}" class="btn btn-outline-primary btn-block">
                        <i class="fas fa-plus"></i>
                        خصم جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">اعتماد الخصم</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-deductions.approve', $deduction->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <p>هل أنت متأكد من اعتماد هذا الخصم؟</p>
                    <div class="form-group">
                        <label for="approval_notes">ملاحظات الاعتماد (اختياري)</label>
                        <textarea class="form-control" id="approval_notes" name="approval_notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i>
                        اعتماد
                    </button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div class="modal fade" id="rejectionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفض الخصم</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-deductions.reject', $deduction->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <p>هل أنت متأكد من رفض هذا الخصم؟</p>
                    <div class="form-group">
                        <label for="rejection_reason">سبب الرفض <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times"></i>
                        رفض
                    </button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تسجيل دفعة</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('employee-deductions.record-payment', $deduction->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="payment_amount">مبلغ الدفعة</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="payment_amount" name="payment_amount" 
                                   value="{{ $deduction->installment_amount }}" step="0.01" min="0" required>
                            <div class="input-group-append">
                                <span class="input-group-text">ج.م</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="payment_date">تاريخ الدفع</label>
                        <input type="date" class="form-control" id="payment_date" name="payment_date" 
                               value="{{ date('Y-m-d') }}" required>
                    </div>
                    <div class="form-group">
                        <label for="payment_notes">ملاحظات</label>
                        <textarea class="form-control" id="payment_notes" name="payment_notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        تسجيل الدفعة
                    </button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function approveDeduction() {
    $('#approvalModal').modal('show');
}

function rejectDeduction() {
    $('#rejectionModal').modal('show');
}

function recordPayment() {
    $('#paymentModal').modal('show');
}

function printDeduction() {
    window.print();
}
</script>
@endsection
