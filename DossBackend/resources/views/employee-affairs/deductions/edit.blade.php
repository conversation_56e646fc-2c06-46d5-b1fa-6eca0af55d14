@extends('layouts.app')

@section('title', 'تعديل خصم')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-edit text-primary"></i>
                        تعديل خصم الموظف
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('employee-affairs.index') }}">شؤون العاملين</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-deductions.index') }}">الخصومات</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('employee-deductions.show', $deduction->id) }}">عرض الخصم</a></li>
                            <li class="breadcrumb-item active">تعديل</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('employee-deductions.show', $deduction->id) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i>
                        العودة للعرض
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Check -->
    @if($deduction->status !== 'pending')
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>تنبيه:</strong> 
                    @if($deduction->status === 'approved')
                        هذا الخصم تم اعتماده ولا يمكن تعديله. يمكنك فقط عرض التفاصيل.
                    @elseif($deduction->status === 'rejected')
                        هذا الخصم تم رفضه. يمكنك إنشاء خصم جديد إذا لزم الأمر.
                    @elseif($deduction->status === 'completed')
                        هذا الخصم تم تطبيقه بالكامل ولا يمكن تعديله.
                    @endif
                    <a href="{{ route('employee-deductions.show', $deduction->id) }}" class="btn btn-sm btn-outline-primary ml-2">
                        عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
    @endif

    <!-- Form (only show if status is pending) -->
    @if($deduction->status === 'pending')
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit"></i>
                        تعديل بيانات الخصم
                    </h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('employee-deductions.update', $deduction->id) }}" method="POST" id="deduction-form" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <!-- Employee (Read-only) -->
                        <div class="form-group mb-3">
                            <label for="employee_name" class="form-label">الموظف</label>
                            <input type="text" class="form-control" id="employee_name" 
                                   value="{{ $deduction->employee->name }} - {{ $deduction->employee->employee_code }}" readonly>
                            <small class="form-text text-muted">لا يمكن تغيير الموظف بعد إنشاء الخصم</small>
                        </div>

                        <!-- Deduction Type -->
                        <div class="form-group mb-3">
                            <label for="type" class="form-label required">نوع الخصم</label>
                            <select class="form-control" id="type" name="type" required>
                                <option value="">اختر النوع</option>
                                <option value="one_time" {{ old('type', $deduction->type) == 'one_time' ? 'selected' : '' }}>خصم لمرة واحدة</option>
                                <option value="installment" {{ old('type', $deduction->type) == 'installment' ? 'selected' : '' }}>خصم بالأقساط</option>
                                <option value="monthly" {{ old('type', $deduction->type) == 'monthly' ? 'selected' : '' }}>خصم شهري</option>
                            </select>
                            @error('type')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Category -->
                        <div class="form-group mb-3">
                            <label for="category" class="form-label required">فئة الخصم</label>
                            <select class="form-control" id="category" name="category" required>
                                <option value="">اختر الفئة</option>
                                <option value="disciplinary" {{ old('category', $deduction->category) == 'disciplinary' ? 'selected' : '' }}>تأديبي</option>
                                <option value="loan_repayment" {{ old('category', $deduction->category) == 'loan_repayment' ? 'selected' : '' }}>سداد قرض</option>
                                <option value="advance_repayment" {{ old('category', $deduction->category) == 'advance_repayment' ? 'selected' : '' }}>سداد سلفة</option>
                                <option value="insurance" {{ old('category', $deduction->category) == 'insurance' ? 'selected' : '' }}>تأمين</option>
                                <option value="tax" {{ old('category', $deduction->category) == 'tax' ? 'selected' : '' }}>ضرائب</option>
                                <option value="absence" {{ old('category', $deduction->category) == 'absence' ? 'selected' : '' }}>غياب</option>
                                <option value="damage" {{ old('category', $deduction->category) == 'damage' ? 'selected' : '' }}>تعويض أضرار</option>
                                <option value="other" {{ old('category', $deduction->category) == 'other' ? 'selected' : '' }}>أخرى</option>
                            </select>
                            @error('category')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Amount -->
                        <div class="form-group mb-3">
                            <label for="amount" class="form-label required">المبلغ الإجمالي</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="amount" name="amount" 
                                       value="{{ old('amount', $deduction->amount) }}" step="0.01" min="0" required>
                                <div class="input-group-append">
                                    <span class="input-group-text">ج.م</span>
                                </div>
                            </div>
                            @error('amount')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Installment Details -->
                        <div id="installment-details" style="{{ $deduction->type === 'installment' ? '' : 'display: none;' }}">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="installments" class="form-label">عدد الأقساط</label>
                                        <input type="number" class="form-control" id="installments" name="installments" 
                                               value="{{ old('installments', $deduction->installments) }}" min="1" max="60"
                                               {{ $deduction->type === 'installment' ? 'required' : '' }}>
                                        @error('installments')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="installment_amount" class="form-label">قيمة القسط</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="installment_amount" name="installment_amount" 
                                                   value="{{ old('installment_amount', $deduction->installment_amount) }}" step="0.01" readonly>
                                            <div class="input-group-append">
                                                <span class="input-group-text">ج.م</span>
                                            </div>
                                        </div>
                                        @error('installment_amount')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mb-3">
                                <label for="start_date" class="form-label">تاريخ بداية الخصم</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" 
                                       value="{{ old('start_date', $deduction->start_date ? $deduction->start_date->format('Y-m-d') : '') }}">
                                @error('start_date')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Reason -->
                        <div class="form-group mb-3">
                            <label for="reason" class="form-label required">سبب الخصم</label>
                            <textarea class="form-control" id="reason" name="reason" rows="3" 
                                      placeholder="اذكر السبب التفصيلي للخصم" required>{{ old('reason', $deduction->reason) }}</textarea>
                            @error('reason')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Notes -->
                        <div class="form-group mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2" 
                                      placeholder="أي ملاحظات إضافية">{{ old('notes', $deduction->notes) }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Submit Buttons -->
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ التعديلات
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                            <a href="{{ route('employee-deductions.show', $deduction->id) }}" class="btn btn-light">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Info Panel -->
        <div class="col-lg-4">
            <!-- Current Deduction Info -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i>
                        معلومات الخصم الحالي
                    </h6>
                </div>
                <div class="card-body">
                    <p><strong>رقم الخصم:</strong> #{{ $deduction->id }}</p>
                    <p><strong>تاريخ الإنشاء:</strong> {{ $deduction->created_at->format('Y-m-d') }}</p>
                    <p><strong>الحالة:</strong> 
                        <span class="badge badge-warning">{{ $deduction->status === 'pending' ? 'في الانتظار' : $deduction->status }}</span>
                    </p>
                    @if($deduction->type === 'installment')
                        <p><strong>الأقساط المدفوعة:</strong> {{ $deduction->paid_installments ?? 0 }} من {{ $deduction->installments }}</p>
                        <p><strong>المبلغ المدفوع:</strong> {{ number_format($deduction->paid_amount ?? 0, 2) }} ج.م</p>
                        <p><strong>المبلغ المتبقي:</strong> {{ number_format($deduction->amount - ($deduction->paid_amount ?? 0), 2) }} ج.م</p>
                    @endif
                </div>
            </div>

            <!-- Employee Info -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user"></i>
                        معلومات الموظف
                    </h6>
                </div>
                <div class="card-body">
                    <p><strong>الاسم:</strong> {{ $deduction->employee->name }}</p>
                    <p><strong>الكود:</strong> {{ $deduction->employee->employee_code }}</p>
                    <p><strong>القسم:</strong> {{ $deduction->employee->department->name ?? 'غير محدد' }}</p>
                    <p><strong>المنصب:</strong> {{ $deduction->employee->position->name ?? 'غير محدد' }}</p>
                    <p><strong>الراتب:</strong> {{ number_format($deduction->employee->salary ?? 0, 2) }} ج.م</p>
                </div>
            </div>

            <!-- Installment Calculator -->
            <div class="card shadow mt-3" id="installment-calculator" style="{{ $deduction->type === 'installment' ? '' : 'display: none;' }}">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-calculator"></i>
                        حاسبة الأقساط
                    </h6>
                </div>
                <div class="card-body" id="installment-breakdown">
                    <!-- Installment breakdown will be shown here -->
                </div>
            </div>

            <!-- Edit Guidelines -->
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        إرشادات التعديل
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-edit"></i> يمكن تعديل:</h6>
                        <ul class="mb-0">
                            <li>نوع وفئة الخصم</li>
                            <li>المبلغ وعدد الأقساط</li>
                            <li>السبب والملاحظات</li>
                            <li>تاريخ بداية الخصم</li>
                        </ul>
                    </div>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-lock"></i> لا يمكن تعديل:</h6>
                        <ul class="mb-0">
                            <li>الموظف المحدد</li>
                            <li>الخصومات المعتمدة</li>
                            <li>الأقساط المدفوعة</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Handle deduction type change
    $('#type').change(function() {
        const type = $(this).val();
        if (type === 'installment') {
            $('#installment-details').show();
            $('#installments').prop('required', true);
            $('#installment_amount').prop('required', true);
            $('#installment-calculator').show();
        } else {
            $('#installment-details').hide();
            $('#installments').prop('required', false);
            $('#installment_amount').prop('required', false);
            $('#installment-calculator').hide();
        }
        calculateInstallments();
    });

    // Handle amount changes
    $('#amount, #installments').on('input', function() {
        calculateInstallments();
    });

    // Initial calculation
    calculateInstallments();

    // Form validation
    $('#deduction-form').on('submit', function(e) {
        const amount = parseFloat($('#amount').val());
        const reason = $('#reason').val().trim();
        
        if (amount <= 0) {
            e.preventDefault();
            alert('مبلغ الخصم يجب أن يكون أكبر من صفر');
            return false;
        }
        
        if (!reason || reason.length < 10) {
            e.preventDefault();
            alert('يجب كتابة سبب الخصم بتفصيل (10 أحرف على الأقل)');
            return false;
        }
        
        // Confirm submission
        if (!confirm('هل أنت متأكد من حفظ التعديلات؟')) {
            e.preventDefault();
            return false;
        }
    });
});

function calculateInstallments() {
    const amount = parseFloat($('#amount').val()) || 0;
    const installments = parseInt($('#installments').val()) || 1;
    const type = $('#type').val();
    
    if (type === 'installment' && amount > 0 && installments > 0) {
        const installmentAmount = (amount / installments).toFixed(2);
        $('#installment_amount').val(installmentAmount);
        
        // Show installment breakdown
        let breakdown = '<h6>تفاصيل الأقساط المحدثة:</h6>';
        breakdown += `<p><strong>إجمالي المبلغ:</strong> ${amount} ج.م</p>`;
        breakdown += `<p><strong>عدد الأقساط:</strong> ${installments}</p>`;
        breakdown += `<p><strong>قيمة القسط:</strong> ${installmentAmount} ج.م</p>`;
        
        // Calculate end date
        const startDate = new Date($('#start_date').val() || new Date());
        const endDate = new Date(startDate);
        endDate.setMonth(endDate.getMonth() + installments - 1);
        
        breakdown += `<p><strong>تاريخ الانتهاء المتوقع:</strong> ${endDate.toLocaleDateString('ar-EG')}</p>`;
        
        $('#installment-breakdown').html(breakdown);
    }
}

function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ ستفقد جميع التعديلات غير المحفوظة.')) {
        location.reload();
    }
}
</script>
@endsection
