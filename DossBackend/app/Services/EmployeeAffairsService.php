<?php

namespace App\Services;

use App\Models\EmployeeBonus;
use App\Models\EmployeeDeduction;
use App\Models\EmployeeTransfer;
use App\Models\EmployeePromotion;
use App\Models\EmployeeSuggestion;
use App\Models\EmployeeComplaint;
use App\Models\EmployeeResignation;
use App\Models\EmployeeWarning;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EmployeeAffairsService
{
    /**
     * Get employee affairs dashboard statistics
     */
    public function getDashboardStats(): array
    {
        return [
            'bonuses' => [
                'total' => EmployeeBonus::count(),
                'pending' => EmployeeBonus::pending()->count(),
                'approved' => EmployeeBonus::approved()->count(),
                'this_month' => EmployeeBonus::thisMonth()->count(),
            ],
            'deductions' => [
                'total' => EmployeeDeduction::count(),
                'pending' => EmployeeDeduction::pending()->count(),
                'approved' => EmployeeDeduction::approved()->count(),
                'active' => EmployeeDeduction::active()->count(),
            ],
            'transfers' => [
                'total' => EmployeeTransfer::count(),
                'pending' => EmployeeTransfer::pending()->count(),
                'approved' => EmployeeTransfer::approved()->count(),
                'active' => EmployeeTransfer::active()->count(),
            ],
            'promotions' => [
                'total' => EmployeePromotion::count(),
                'pending' => EmployeePromotion::pending()->count(),
                'approved' => EmployeePromotion::approved()->count(),
                'this_year' => EmployeePromotion::thisYear()->count(),
            ],
            'suggestions' => [
                'total' => EmployeeSuggestion::count(),
                'pending_review' => EmployeeSuggestion::pendingReview()->count(),
                'approved' => EmployeeSuggestion::approved()->count(),
                'implemented' => EmployeeSuggestion::implemented()->count(),
            ],
            'complaints' => [
                'total' => EmployeeComplaint::count(),
                'submitted' => EmployeeComplaint::submitted()->count(),
                'investigating' => EmployeeComplaint::investigating()->count(),
                'high_severity' => EmployeeComplaint::highSeverity()->count(),
            ],
            'resignations' => [
                'total' => EmployeeResignation::count(),
                'pending' => EmployeeResignation::pending()->count(),
                'approved' => EmployeeResignation::approved()->count(),
                'active' => EmployeeResignation::active()->count(),
            ],
            'warnings' => [
                'total' => EmployeeWarning::count(),
                'active' => EmployeeWarning::active()->count(),
                'unacknowledged' => EmployeeWarning::unacknowledged()->count(),
                'high_level' => EmployeeWarning::highLevel()->count(),
            ]
        ];
    }

    /**
     * Get employee profile summary
     */
    public function getEmployeeProfile($userId): array
    {
        $user = User::findOrFail($userId);
        
        return [
            'user' => $user,
            'bonuses' => [
                'total' => EmployeeBonus::byUser($userId)->approved()->sum('amount'),
                'count' => EmployeeBonus::byUser($userId)->approved()->count(),
                'this_year' => EmployeeBonus::byUser($userId)->approved()->thisYear()->sum('amount'),
            ],
            'deductions' => [
                'total' => EmployeeDeduction::byUser($userId)->approved()->sum('amount'),
                'remaining' => EmployeeDeduction::byUser($userId)->approved()->sum('remaining_amount'),
                'active_count' => EmployeeDeduction::byUser($userId)->active()->count(),
            ],
            'transfers' => [
                'count' => EmployeeTransfer::byUser($userId)->approved()->count(),
                'latest' => EmployeeTransfer::byUser($userId)->approved()->latest()->first(),
            ],
            'promotions' => [
                'count' => EmployeePromotion::byUser($userId)->approved()->count(),
                'latest' => EmployeePromotion::byUser($userId)->approved()->latest()->first(),
            ],
            'suggestions' => [
                'total' => EmployeeSuggestion::byUser($userId)->count(),
                'implemented' => EmployeeSuggestion::byUser($userId)->implemented()->count(),
                'reward_total' => EmployeeSuggestion::byUser($userId)->implemented()->sum('reward_amount'),
            ],
            'complaints' => [
                'filed' => EmployeeComplaint::byUser($userId)->count(),
                'against' => EmployeeComplaint::againstUser($userId)->count(),
            ],
            'warnings' => [
                'active' => EmployeeWarning::byUser($userId)->active()->count(),
                'total' => EmployeeWarning::byUser($userId)->count(),
                'highest_level' => EmployeeWarning::getHighestEscalationLevel($userId),
            ]
        ];
    }

    /**
     * Process monthly recurring deductions
     */
    public function processRecurringDeductions(): array
    {
        $processed = [];
        $errors = [];

        $recurringDeductions = EmployeeDeduction::approved()
            ->recurring()
            ->active()
            ->get();

        foreach ($recurringDeductions as $deduction) {
            try {
                if ($deduction->processInstallment()) {
                    $processed[] = $deduction->id;
                }
            } catch (\Exception $e) {
                $errors[] = [
                    'deduction_id' => $deduction->id,
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'processed' => count($processed),
            'errors' => count($errors),
            'processed_ids' => $processed,
            'error_details' => $errors
        ];
    }

    /**
     * Execute approved transfers
     */
    public function executeApprovedTransfers(): array
    {
        $executed = [];
        $errors = [];

        $transfers = EmployeeTransfer::approved()
            ->where('effective_date', '<=', now()->toDateString())
            ->get();

        foreach ($transfers as $transfer) {
            try {
                if ($transfer->executeTransfer()) {
                    $executed[] = $transfer->id;
                }
            } catch (\Exception $e) {
                $errors[] = [
                    'transfer_id' => $transfer->id,
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'executed' => count($executed),
            'errors' => count($errors),
            'executed_ids' => $executed,
            'error_details' => $errors
        ];
    }

    /**
     * Execute approved promotions
     */
    public function executeApprovedPromotions(): array
    {
        $executed = [];
        $errors = [];

        $promotions = EmployeePromotion::approved()
            ->where('effective_date', '<=', now()->toDateString())
            ->get();

        foreach ($promotions as $promotion) {
            try {
                if ($promotion->executePromotion()) {
                    $executed[] = $promotion->id;
                }
            } catch (\Exception $e) {
                $errors[] = [
                    'promotion_id' => $promotion->id,
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'executed' => count($executed),
            'errors' => count($errors),
            'executed_ids' => $executed,
            'error_details' => $errors
        ];
    }

    /**
     * Update expired warnings status
     */
    public function updateExpiredWarnings(): array
    {
        $updated = [];

        $expiredWarnings = EmployeeWarning::where('status', 'active')
            ->where('expiry_date', '<', now()->toDateString())
            ->get();

        foreach ($expiredWarnings as $warning) {
            if ($warning->updateStatus()) {
                $updated[] = $warning->id;
            }
        }

        return [
            'updated' => count($updated),
            'updated_ids' => $updated
        ];
    }

    /**
     * Get pending approvals summary
     */
    public function getPendingApprovals(): array
    {
        return [
            'bonuses' => EmployeeBonus::pending()->with('user')->get(),
            'deductions' => EmployeeDeduction::pending()->with('user')->get(),
            'transfers' => EmployeeTransfer::pending()->with('user')->get(),
            'promotions' => EmployeePromotion::pending()->with('user')->get(),
            'resignations' => EmployeeResignation::pending()->with('user')->get(),
        ];
    }

    /**
     * Get items requiring follow-up
     */
    public function getFollowUpItems(): array
    {
        return [
            'complaints' => EmployeeComplaint::requiresFollowUp()
                ->overdueFollowUp()
                ->with('user', 'assignedTo')
                ->get(),
            'warnings' => EmployeeWarning::requiresFollowUp()
                ->with('user', 'issuedBy')
                ->get(),
            'resignations' => EmployeeResignation::approved()
                ->where(function($q) {
                    $q->where('exit_interview_completed', false)
                      ->orWhere('handover_completed', false);
                })
                ->with('user')
                ->get(),
        ];
    }

    /**
     * Generate monthly report
     */
    public function generateMonthlyReport($month = null, $year = null): array
    {
        $month = $month ?? now()->month;
        $year = $year ?? now()->year;
        
        $startDate = Carbon::create($year, $month, 1)->startOfMonth();
        $endDate = Carbon::create($year, $month, 1)->endOfMonth();

        return [
            'period' => [
                'month' => $month,
                'year' => $year,
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
            ],
            'bonuses' => [
                'count' => EmployeeBonus::byDateRange($startDate, $endDate)->approved()->count(),
                'total_amount' => EmployeeBonus::byDateRange($startDate, $endDate)->approved()->sum('amount'),
                'by_type' => EmployeeBonus::byDateRange($startDate, $endDate)
                    ->approved()
                    ->select('bonus_type', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
                    ->groupBy('bonus_type')
                    ->get(),
            ],
            'deductions' => [
                'count' => EmployeeDeduction::byDateRange($startDate, $endDate)->approved()->count(),
                'total_amount' => EmployeeDeduction::byDateRange($startDate, $endDate)->approved()->sum('amount'),
                'by_type' => EmployeeDeduction::byDateRange($startDate, $endDate)
                    ->approved()
                    ->select('deduction_type', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
                    ->groupBy('deduction_type')
                    ->get(),
            ],
            'transfers' => [
                'count' => EmployeeTransfer::byDateRange($startDate, $endDate)->approved()->count(),
                'by_type' => EmployeeTransfer::byDateRange($startDate, $endDate)
                    ->approved()
                    ->select('transfer_type', DB::raw('COUNT(*) as count'))
                    ->groupBy('transfer_type')
                    ->get(),
            ],
            'promotions' => [
                'count' => EmployeePromotion::byDateRange($startDate, $endDate)->approved()->count(),
                'total_salary_increase' => EmployeePromotion::byDateRange($startDate, $endDate)->approved()->sum('salary_increase'),
                'by_type' => EmployeePromotion::byDateRange($startDate, $endDate)
                    ->approved()
                    ->select('promotion_type', DB::raw('COUNT(*) as count'))
                    ->groupBy('promotion_type')
                    ->get(),
            ],
            'resignations' => [
                'count' => EmployeeResignation::byDateRange($startDate, $endDate)->count(),
                'by_type' => EmployeeResignation::byDateRange($startDate, $endDate)
                    ->select('resignation_type', DB::raw('COUNT(*) as count'))
                    ->groupBy('resignation_type')
                    ->get(),
            ],
            'warnings' => [
                'count' => EmployeeWarning::byDateRange($startDate, $endDate)->count(),
                'by_type' => EmployeeWarning::byDateRange($startDate, $endDate)
                    ->select('warning_type', DB::raw('COUNT(*) as count'))
                    ->groupBy('warning_type')
                    ->get(),
            ]
        ];
    }
}
