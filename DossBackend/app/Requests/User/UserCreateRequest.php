<?php

namespace App\Requests\User;



use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name'=>'required|string|max:100|min:2',
            'email'=>'required|email|unique:users',
            'password'=>'required|string|min:4',
            'username'=>'required|string',
            'address'=>'required',
            'dob'=>'required|date|before:today',
            'phone'=>'required|numeric',
            'gender' => ['required', 'string', Rule::in(User::GENDER)],
            'employment_type' => ['required', 'string', Rule::in(User::EMPLOYMENT_TYPE)],
            'user_type' => ['required', 'string', Rule::in(User::USER_TYPE)],
            'joining_date' => 'nullable|date|before_or_equal:today',
            'status' => ['required', 'string', Rule::in(User::STATUS)],
            'role_id' => 'required|exists:roles,id',
            'company_id' => 'required|exists:companies,id',
            'branch_id' => 'required|exists:branches,id',
            'department_id' => 'required|exists:departments,id',
            'post_id' => 'required|exists:posts,id',
            'supervisor_id' => 'nullable|exists:users,id',
            'office_time_id' => 'required|exists:office_times,id',
            'leave_allocated' => 'nullable|numeric|gte:0',
            'remarks' => 'nullable|string|max:1000',
            'is_active' => ['nullable', 'boolean', Rule::in([1, 0])],
            'workspace_type' => ['nullable', 'boolean', Rule::in([1, 0])],
            'avatar' => ['required', 'file', 'mimes:jpeg,png,jpg,webp','max:5048'],
    
            // ✅ New fields:
            'id_number' => 'required|string|max:50',
            'fingerprint_code' => 'nullable|string|max:100',
            'be_connect_code' => 'nullable|string|max:100',
            'be_connect_client_code' => 'nullable|string|max:100',
            'full_name_en' => 'nullable|string|max:200',
            'bank_branch_code' => 'nullable|string|max:100',
            'bank_account_no' => 'required|numeric',
            'bank_name' => 'required|string|max:150',
            'bank_account_type' => 'required|string|in:current,saving', // عدل حسب القيم في enum
    
            // ✅ Insurance
            'insurance_type' => 'nullable|in:social,medical',
            'medical_insurance_category' => 'nullable|required_if:insurance_type,medical|in:public,private',
            'insurance_institution_id' => 'nullable|exists:insurance_institutions,id',
            'insurance_job_title' => 'nullable|string|max:100',
            'insurance_start_date' => 'nullable|date',
            'insurance_salary' => 'nullable|numeric|min:0',
    
            // ✅ Marital and Dependents
            'marital_status' => 'nullable|in:single,married',
            'emergency_contact_number' => 'nullable|string|max:20',
            'dependent_count' => 'nullable|numeric|min:1|max:20',
    
            'dependents' => 'nullable|array',
            'dependents.*.name' => 'required_with:dependents|string|max:100',
            'dependents.*.relation' => 'required_with:dependents|string|max:50',
            'dependents.*.birth_date' => 'required_with:dependents|date|before:today',
            'dependents.*.mobile' => 'nullable|string|max:20',
        ];
    }


}
