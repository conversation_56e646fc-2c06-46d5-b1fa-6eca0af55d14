<?php

namespace App\Http\Controllers;

use App\Models\EmployeeDeduction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class EmployeeDeductionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of deductions
     */
    public function index(Request $request)
    {
        $query = EmployeeDeduction::with(['user', 'approvedBy', 'createdBy']);

        // Apply filters
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('deduction_type')) {
            $query->where('deduction_type', $request->deduction_type);
        }

        if ($request->filled('approval_status')) {
            $query->where('approval_status', $request->approval_status);
        }

        if ($request->filled('date_from')) {
            $query->where('effective_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('effective_date', '<=', $request->date_to);
        }

        $deductions = $query->orderBy('created_at', 'desc')->paginate(15);
        $users = User::select('id', 'name')->get();

        return view('employee-affairs.deductions.index', compact('deductions', 'users'));
    }

    /**
     * Show the form for creating a new deduction
     */
    public function create()
    {
        $users = User::select('id', 'name')->get();
        return view('employee-affairs.deductions.create', compact('users'));
    }

    /**
     * Store a newly created deduction
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'deduction_type' => 'required|in:disciplinary,loan,advance,insurance,tax,absence,late,other',
            'amount' => 'required|numeric|min:0',
            'description' => 'required|string|max:500',
            'reason' => 'nullable|string|max:1000',
            'effective_date' => 'required|date|after_or_equal:today',
            'is_recurring' => 'boolean',
            'recurring_period' => 'nullable|in:monthly,quarterly,yearly',
            'installments' => 'nullable|integer|min:1',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = [
            'user_id' => $request->user_id,
            'deduction_type' => $request->deduction_type,
            'amount' => $request->amount,
            'description' => $request->description,
            'reason' => $request->reason,
            'approval_status' => 'pending',
            'effective_date' => $request->effective_date,
            'is_recurring' => $request->boolean('is_recurring'),
            'recurring_period' => $request->recurring_period,
            'installments' => $request->installments,
            'remaining_amount' => $request->amount,
            'notes' => $request->notes,
            'created_by' => Auth::id()
        ];

        EmployeeDeduction::create($data);

        return redirect()->route('employee-deductions.index')
            ->with('success', 'تم إنشاء الخصم بنجاح وهو في انتظار الموافقة');
    }

    /**
     * Display the specified deduction
     */
    public function show(EmployeeDeduction $employeeDeduction)
    {
        $employeeDeduction->load(['user', 'approvedBy', 'createdBy', 'updatedBy']);
        return view('employee-affairs.deductions.show', compact('employeeDeduction'));
    }

    /**
     * Show the form for editing the specified deduction
     */
    public function edit(EmployeeDeduction $employeeDeduction)
    {
        if (!$employeeDeduction->canBeApproved()) {
            return redirect()->route('employee-deductions.index')
                ->with('error', 'لا يمكن تعديل هذا الخصم');
        }

        $users = User::select('id', 'name')->get();
        return view('employee-affairs.deductions.edit', compact('employeeDeduction', 'users'));
    }

    /**
     * Update the specified deduction
     */
    public function update(Request $request, EmployeeDeduction $employeeDeduction)
    {
        if (!$employeeDeduction->canBeApproved()) {
            return redirect()->route('employee-deductions.index')
                ->with('error', 'لا يمكن تعديل هذا الخصم');
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'deduction_type' => 'required|in:disciplinary,loan,advance,insurance,tax,absence,late,other',
            'amount' => 'required|numeric|min:0',
            'description' => 'required|string|max:500',
            'reason' => 'nullable|string|max:1000',
            'effective_date' => 'required|date|after_or_equal:today',
            'is_recurring' => 'boolean',
            'recurring_period' => 'nullable|in:monthly,quarterly,yearly',
            'installments' => 'nullable|integer|min:1',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $employeeDeduction->update([
            'user_id' => $request->user_id,
            'deduction_type' => $request->deduction_type,
            'amount' => $request->amount,
            'description' => $request->description,
            'reason' => $request->reason,
            'effective_date' => $request->effective_date,
            'is_recurring' => $request->boolean('is_recurring'),
            'recurring_period' => $request->recurring_period,
            'installments' => $request->installments,
            'remaining_amount' => $request->amount,
            'notes' => $request->notes,
            'updated_by' => Auth::id()
        ]);

        return redirect()->route('employee-deductions.index')
            ->with('success', 'تم تحديث الخصم بنجاح');
    }

    /**
     * Approve the specified deduction
     */
    public function approve(EmployeeDeduction $employeeDeduction)
    {
        if (!$employeeDeduction->canBeApproved()) {
            return redirect()->back()
                ->with('error', 'لا يمكن الموافقة على هذا الخصم');
        }

        $employeeDeduction->approve();

        return redirect()->back()
            ->with('success', 'تم الموافقة على الخصم بنجاح');
    }

    /**
     * Reject the specified deduction
     */
    public function reject(EmployeeDeduction $employeeDeduction)
    {
        if (!$employeeDeduction->canBeRejected()) {
            return redirect()->back()
                ->with('error', 'لا يمكن رفض هذا الخصم');
        }

        $employeeDeduction->reject();

        return redirect()->back()
            ->with('success', 'تم رفض الخصم');
    }

    /**
     * Remove the specified deduction
     */
    public function destroy(EmployeeDeduction $employeeDeduction)
    {
        if (!$employeeDeduction->isPending()) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف هذا الخصم');
        }

        $employeeDeduction->delete();

        return redirect()->route('employee-deductions.index')
            ->with('success', 'تم حذف الخصم بنجاح');
    }

    /**
     * Process installment for specific deduction
     */
    public function processInstallment(EmployeeDeduction $employeeDeduction)
    {
        if (!$employeeDeduction->isApproved() || !$employeeDeduction->hasInstallments()) {
            return redirect()->back()
                ->with('error', 'لا يمكن معالجة قسط لهذا الخصم');
        }

        if ($employeeDeduction->processInstallment()) {
            return redirect()->back()
                ->with('success', 'تم معالجة القسط بنجاح');
        }

        return redirect()->back()
            ->with('error', 'فشل في معالجة القسط');
    }

    /**
     * Get deductions for specific user (AJAX)
     */
    public function getUserDeductions($userId)
    {
        $deductions = EmployeeDeduction::where('user_id', $userId)
            ->with(['approvedBy', 'createdBy'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $deductions
        ]);
    }

    /**
     * Get deduction statistics (AJAX)
     */
    public function getStats()
    {
        $stats = [
            'total' => EmployeeDeduction::count(),
            'pending' => EmployeeDeduction::pending()->count(),
            'approved' => EmployeeDeduction::approved()->count(),
            'rejected' => EmployeeDeduction::rejected()->count(),
            'active' => EmployeeDeduction::active()->count(),
            'this_month_total' => EmployeeDeduction::thisMonth()->approved()->sum('amount'),
            'this_year_total' => EmployeeDeduction::thisYear()->approved()->sum('amount'),
            'by_type' => EmployeeDeduction::approved()
                ->selectRaw('deduction_type, COUNT(*) as count, SUM(amount) as total')
                ->groupBy('deduction_type')
                ->get(),
            'recurring_total' => EmployeeDeduction::recurring()->active()->sum('remaining_amount')
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
