<?php

namespace App\Http\Controllers;

use App\Services\EmployeeAffairsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EmployeeAffairsController extends Controller
{
    protected $employeeAffairsService;

    public function __construct(EmployeeAffairsService $employeeAffairsService)
    {
        $this->employeeAffairsService = $employeeAffairsService;
        $this->middleware('auth');
    }

    /**
     * Display employee affairs dashboard
     */
    public function index()
    {
        $stats = $this->employeeAffairsService->getDashboardStats();
        $pendingApprovals = $this->employeeAffairsService->getPendingApprovals();
        $followUpItems = $this->employeeAffairsService->getFollowUpItems();

        return view('employee-affairs.index', compact('stats', 'pendingApprovals', 'followUpItems'));
    }

    /**
     * Display employee profile
     */
    public function profile($userId = null)
    {
        $userId = $userId ?? Auth::id();
        $profile = $this->employeeAffairsService->getEmployeeProfile($userId);

        return view('employee-affairs.profile', compact('profile'));
    }

    /**
     * Generate and display monthly report
     */
    public function monthlyReport(Request $request)
    {
        $month = $request->get('month', now()->month);
        $year = $request->get('year', now()->year);
        
        $report = $this->employeeAffairsService->generateMonthlyReport($month, $year);

        return view('employee-affairs.reports.monthly', compact('report'));
    }

    /**
     * Process recurring deductions (Admin only)
     */
    public function processRecurringDeductions()
    {
        $this->authorize('admin');
        
        $result = $this->employeeAffairsService->processRecurringDeductions();
        
        return response()->json([
            'success' => true,
            'message' => "تم معالجة {$result['processed']} خصم متكرر بنجاح",
            'data' => $result
        ]);
    }

    /**
     * Execute approved transfers (Admin only)
     */
    public function executeTransfers()
    {
        $this->authorize('admin');
        
        $result = $this->employeeAffairsService->executeApprovedTransfers();
        
        return response()->json([
            'success' => true,
            'message' => "تم تنفيذ {$result['executed']} نقل معتمد",
            'data' => $result
        ]);
    }

    /**
     * Execute approved promotions (Admin only)
     */
    public function executePromotions()
    {
        $this->authorize('admin');
        
        $result = $this->employeeAffairsService->executeApprovedPromotions();
        
        return response()->json([
            'success' => true,
            'message' => "تم تنفيذ {$result['executed']} ترقية معتمدة",
            'data' => $result
        ]);
    }

    /**
     * Update expired warnings (Admin only)
     */
    public function updateExpiredWarnings()
    {
        $this->authorize('admin');
        
        $result = $this->employeeAffairsService->updateExpiredWarnings();
        
        return response()->json([
            'success' => true,
            'message' => "تم تحديث {$result['updated']} إنذار منتهي الصلاحية",
            'data' => $result
        ]);
    }

    /**
     * Get pending approvals for AJAX
     */
    public function getPendingApprovals()
    {
        $pendingApprovals = $this->employeeAffairsService->getPendingApprovals();
        
        return response()->json([
            'success' => true,
            'data' => $pendingApprovals
        ]);
    }

    /**
     * Get follow-up items for AJAX
     */
    public function getFollowUpItems()
    {
        $followUpItems = $this->employeeAffairsService->getFollowUpItems();
        
        return response()->json([
            'success' => true,
            'data' => $followUpItems
        ]);
    }

    /**
     * Get dashboard stats for AJAX
     */
    public function getDashboardStats()
    {
        $stats = $this->employeeAffairsService->getDashboardStats();
        
        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
